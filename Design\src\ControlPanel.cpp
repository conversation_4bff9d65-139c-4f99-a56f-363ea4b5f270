/**
 * @file ControlPanel.cpp
 * @brief Implementation of professional HVNC Control Panel
 */

#include "ControlPanel.h"
#include "StyleManager.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QLabel>
#include <QProgressBar>
#include <QTableWidget>
#include <QTextEdit>
#include <QSlider>
#include <QTabWidget>
#include <QHeaderView>
#include <QMenu>
#include <QContextMenuEvent>
#include <QDateTime>
#include <QMessageBox>
#include <QApplication>
#include <QSplitter>
#include <QFrame>

ControlPanel::ControlPanel(QWidget* parent)
    : QWidget(parent)
    , m_tabWidget(nullptr)
    , m_serverConfigGroup(nullptr)
    , m_portSpinBox(nullptr)
    , m_passwordLineEdit(nullptr)
    , m_maxConnectionsSpinBox(nullptr)
    , m_startServerButton(nullptr)
    , m_stopServerButton(nullptr)
    , m_serverStatusLabel(nullptr)
    , m_serverStatusProgress(nullptr)
    , m_connectionTable(nullptr)
    , m_activeConnectionsLabel(nullptr)
    , m_totalBytesLabel(nullptr)
    , m_disconnectClientButton(nullptr)
    , m_refreshConnectionsButton(nullptr)
    , m_compressionSlider(nullptr)
    , m_compressionLabel(nullptr)
    , m_updateIntervalSpinBox(nullptr)
    , m_enableLoggingCheckBox(nullptr)
    , m_enableEncryptionCheckBox(nullptr)
    , m_qualityComboBox(nullptr)
    , m_autoStartCheckBox(nullptr)
    , m_logsTextEdit(nullptr)
    , m_clearLogsButton(nullptr)
    , m_autoScrollLogsCheckBox(nullptr)
    , m_serverRunning(false)
    , m_activeConnections(0)
    , m_totalBytesReceived(0)
    , m_totalBytesSent(0)
{
    initializeUI();
    applyProfessionalStyling();
    updateUIState();
}

ControlPanel::~ControlPanel() = default;

void ControlPanel::initializeUI()
{
    // Main layout
    auto* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(10, 10, 10, 10);
    mainLayout->setSpacing(5);

    // Create tab widget
    m_tabWidget = new QTabWidget(this);
    m_tabWidget->setTabPosition(QTabWidget::North);
    m_tabWidget->setMovable(false);

    // Add tabs
    m_tabWidget->addTab(createServerConfigTab(), "Server Configuration");
    m_tabWidget->addTab(createConnectionMonitorTab(), "Connection Monitor");
    m_tabWidget->addTab(createAdvancedSettingsTab(), "Advanced Settings");
    m_tabWidget->addTab(createLogsTab(), "Server Logs");

    mainLayout->addWidget(m_tabWidget);

    // Set minimum size
    setMinimumSize(350, 500);
    setMaximumWidth(400);
}

QWidget* ControlPanel::createServerConfigTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(15, 15, 15, 15);
    layout->setSpacing(10);

    // Server Configuration Group
    m_serverConfigGroup = new QGroupBox("HVNC Server Configuration");
    auto* configLayout = new QGridLayout(m_serverConfigGroup);
    configLayout->setSpacing(8);

    // Port configuration
    auto* portLabel = new QLabel("Server Port:");
    m_portSpinBox = new QSpinBox();
    m_portSpinBox->setRange(1024, 65535);
    m_portSpinBox->setValue(4444);
    m_portSpinBox->setSuffix(" (TCP)");
    configLayout->addWidget(portLabel, 0, 0);
    configLayout->addWidget(m_portSpinBox, 0, 1);

    // Password configuration
    auto* passwordLabel = new QLabel("Server Password:");
    m_passwordLineEdit = new QLineEdit();
    m_passwordLineEdit->setEchoMode(QLineEdit::Password);
    m_passwordLineEdit->setText("admin");
    m_passwordLineEdit->setPlaceholderText("Enter secure password");
    configLayout->addWidget(passwordLabel, 1, 0);
    configLayout->addWidget(m_passwordLineEdit, 1, 1);

    // Max connections
    auto* maxConnLabel = new QLabel("Max Connections:");
    m_maxConnectionsSpinBox = new QSpinBox();
    m_maxConnectionsSpinBox->setRange(1, 100);
    m_maxConnectionsSpinBox->setValue(10);
    m_maxConnectionsSpinBox->setSuffix(" clients");
    configLayout->addWidget(maxConnLabel, 2, 0);
    configLayout->addWidget(m_maxConnectionsSpinBox, 2, 1);

    layout->addWidget(m_serverConfigGroup);

    // Server Control Group
    auto* controlGroup = new QGroupBox("Server Control");
    auto* controlLayout = new QVBoxLayout(controlGroup);

    // Server status
    m_serverStatusLabel = new QLabel("Server Status: Stopped");
    m_serverStatusLabel->setStyleSheet("font-weight: bold; color: #d13438;");
    controlLayout->addWidget(m_serverStatusLabel);

    m_serverStatusProgress = new QProgressBar();
    m_serverStatusProgress->setRange(0, 100);
    m_serverStatusProgress->setValue(0);
    m_serverStatusProgress->setTextVisible(false);
    m_serverStatusProgress->setMaximumHeight(8);
    controlLayout->addWidget(m_serverStatusProgress);

    // Control buttons
    auto* buttonLayout = new QHBoxLayout();
    m_startServerButton = new QPushButton("Start Server");
    m_startServerButton->setIcon(QIcon(":/icons/play.png"));
    m_stopServerButton = new QPushButton("Stop Server");
    m_stopServerButton->setIcon(QIcon(":/icons/stop.png"));
    m_stopServerButton->setEnabled(false);

    buttonLayout->addWidget(m_startServerButton);
    buttonLayout->addWidget(m_stopServerButton);
    controlLayout->addLayout(buttonLayout);

    layout->addWidget(controlGroup);

    // Connect signals
    connect(m_startServerButton, &QPushButton::clicked, this, &ControlPanel::onStartServer);
    connect(m_stopServerButton, &QPushButton::clicked, this, &ControlPanel::onStopServer);
    connect(m_portSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ControlPanel::onSettingsChanged);
    connect(m_passwordLineEdit, &QLineEdit::textChanged, this, &ControlPanel::onSettingsChanged);
    connect(m_maxConnectionsSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), this, &ControlPanel::onSettingsChanged);

    layout->addStretch();
    return widget;
}

QWidget* ControlPanel::createConnectionMonitorTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(15, 15, 15, 15);
    layout->setSpacing(10);

    // Connection statistics
    auto* statsGroup = new QGroupBox("Connection Statistics");
    auto* statsLayout = new QGridLayout(statsGroup);

    m_activeConnectionsLabel = new QLabel("Active Connections: 0");
    m_totalBytesLabel = new QLabel("Total Data: 0 bytes");
    
    statsLayout->addWidget(m_activeConnectionsLabel, 0, 0);
    statsLayout->addWidget(m_totalBytesLabel, 0, 1);

    layout->addWidget(statsGroup);

    // Connection table
    auto* tableGroup = new QGroupBox("Active Connections");
    auto* tableLayout = new QVBoxLayout(tableGroup);

    m_connectionTable = new QTableWidget(0, 5);
    QStringList headers = {"Client ID", "IP Address", "Connected", "Received", "Sent"};
    m_connectionTable->setHorizontalHeaderLabels(headers);
    m_connectionTable->horizontalHeader()->setStretchLastSection(true);
    m_connectionTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_connectionTable->setContextMenuPolicy(Qt::CustomContextMenu);
    m_connectionTable->setAlternatingRowColors(true);

    tableLayout->addWidget(m_connectionTable);

    // Connection control buttons
    auto* connButtonLayout = new QHBoxLayout();
    m_disconnectClientButton = new QPushButton("Disconnect Selected");
    m_disconnectClientButton->setEnabled(false);
    m_refreshConnectionsButton = new QPushButton("Refresh");

    connButtonLayout->addWidget(m_disconnectClientButton);
    connButtonLayout->addWidget(m_refreshConnectionsButton);
    connButtonLayout->addStretch();

    tableLayout->addLayout(connButtonLayout);
    layout->addWidget(tableGroup);

    // Connect signals
    connect(m_connectionTable, &QTableWidget::customContextMenuRequested, 
            this, &ControlPanel::onConnectionTableContextMenu);
    connect(m_connectionTable, &QTableWidget::itemSelectionChanged, [this]() {
        m_disconnectClientButton->setEnabled(m_connectionTable->currentRow() >= 0);
    });
    connect(m_disconnectClientButton, &QPushButton::clicked, this, &ControlPanel::onDisconnectClient);

    return widget;
}

QWidget* ControlPanel::createAdvancedSettingsTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(15, 15, 15, 15);
    layout->setSpacing(10);

    // Performance Settings
    auto* perfGroup = new QGroupBox("Performance Settings");
    auto* perfLayout = new QGridLayout(perfGroup);

    // Compression level
    auto* compLabel = new QLabel("Compression Level:");
    m_compressionSlider = new QSlider(Qt::Horizontal);
    m_compressionSlider->setRange(0, 9);
    m_compressionSlider->setValue(6);
    m_compressionLabel = new QLabel("6 (Balanced)");
    
    perfLayout->addWidget(compLabel, 0, 0);
    perfLayout->addWidget(m_compressionSlider, 0, 1);
    perfLayout->addWidget(m_compressionLabel, 0, 2);

    // Update interval
    auto* intervalLabel = new QLabel("Update Interval:");
    m_updateIntervalSpinBox = new QSpinBox();
    m_updateIntervalSpinBox->setRange(10, 1000);
    m_updateIntervalSpinBox->setValue(50);
    m_updateIntervalSpinBox->setSuffix(" ms");
    
    perfLayout->addWidget(intervalLabel, 1, 0);
    perfLayout->addWidget(m_updateIntervalSpinBox, 1, 1, 1, 2);

    // Quality setting
    auto* qualityLabel = new QLabel("Image Quality:");
    m_qualityComboBox = new QComboBox();
    m_qualityComboBox->addItems({"Low (Fast)", "Medium", "High", "Lossless"});
    m_qualityComboBox->setCurrentIndex(1);
    
    perfLayout->addWidget(qualityLabel, 2, 0);
    perfLayout->addWidget(m_qualityComboBox, 2, 1, 1, 2);

    layout->addWidget(perfGroup);

    // Security Settings
    auto* secGroup = new QGroupBox("Security Settings");
    auto* secLayout = new QVBoxLayout(secGroup);

    m_enableEncryptionCheckBox = new QCheckBox("Enable AES Encryption");
    m_enableEncryptionCheckBox->setChecked(true);
    secLayout->addWidget(m_enableEncryptionCheckBox);

    layout->addWidget(secGroup);

    // General Settings
    auto* genGroup = new QGroupBox("General Settings");
    auto* genLayout = new QVBoxLayout(genGroup);

    m_enableLoggingCheckBox = new QCheckBox("Enable Detailed Logging");
    m_enableLoggingCheckBox->setChecked(true);
    genLayout->addWidget(m_enableLoggingCheckBox);

    m_autoStartCheckBox = new QCheckBox("Auto-start Server on Application Launch");
    genLayout->addWidget(m_autoStartCheckBox);

    layout->addWidget(genGroup);

    // Connect signals
    connect(m_compressionSlider, &QSlider::valueChanged, [this](int value) {
        QString text = QString::number(value);
        if (value == 0) text += " (No compression)";
        else if (value <= 3) text += " (Fast)";
        else if (value <= 6) text += " (Balanced)";
        else text += " (Best)";
        m_compressionLabel->setText(text);
        emit settingsChanged();
    });

    connect(m_updateIntervalSpinBox, QOverload<int>::of(&QSpinBox::valueChanged), 
            this, &ControlPanel::settingsChanged);
    connect(m_qualityComboBox, QOverload<int>::of(&QComboBox::currentIndexChanged), 
            this, &ControlPanel::settingsChanged);
    connect(m_enableEncryptionCheckBox, &QCheckBox::toggled, this, &ControlPanel::settingsChanged);
    connect(m_enableLoggingCheckBox, &QCheckBox::toggled, this, &ControlPanel::settingsChanged);
    connect(m_autoStartCheckBox, &QCheckBox::toggled, this, &ControlPanel::settingsChanged);

    layout->addStretch();
    return widget;
}

QWidget* ControlPanel::createLogsTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(15, 15, 15, 15);
    layout->setSpacing(10);

    // Logs group
    auto* logsGroup = new QGroupBox("Server Logs");
    auto* logsLayout = new QVBoxLayout(logsGroup);

    // Logs text area
    m_logsTextEdit = new QTextEdit();
    m_logsTextEdit->setReadOnly(true);
    m_logsTextEdit->setFont(QFont("Consolas", 9));
    // Note: QTextEdit doesn't have setMaximumBlockCount, we'll manage log size manually
    logsLayout->addWidget(m_logsTextEdit);

    // Logs control
    auto* logsControlLayout = new QHBoxLayout();
    m_clearLogsButton = new QPushButton("Clear Logs");
    m_autoScrollLogsCheckBox = new QCheckBox("Auto-scroll");
    m_autoScrollLogsCheckBox->setChecked(true);

    logsControlLayout->addWidget(m_clearLogsButton);
    logsControlLayout->addWidget(m_autoScrollLogsCheckBox);
    logsControlLayout->addStretch();

    logsLayout->addLayout(logsControlLayout);
    layout->addWidget(logsGroup);

    // Connect signals
    connect(m_clearLogsButton, &QPushButton::clicked, this, &ControlPanel::onClearLogs);

    // Add initial log message
    addLogMessage("INFO", "HVNC Design GUI initialized successfully");

    return widget;
}

void ControlPanel::applyProfessionalStyling()
{
    // Apply modern professional styling
    setStyleSheet(R"(
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #2c3e50;
        }
        QPushButton {
            background-color: #3498db;
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
        QPushButton:pressed {
            background-color: #21618c;
        }
        QPushButton:disabled {
            background-color: #bdc3c7;
            color: #7f8c8d;
        }
        QSpinBox, QLineEdit, QComboBox {
            border: 2px solid #bdc3c7;
            border-radius: 4px;
            padding: 5px;
            background-color: white;
        }
        QSpinBox:focus, QLineEdit:focus, QComboBox:focus {
            border-color: #3498db;
        }
        QTableWidget {
            gridline-color: #ecf0f1;
            background-color: white;
            alternate-background-color: #f8f9fa;
        }
        QTableWidget::item:selected {
            background-color: #3498db;
            color: white;
        }
        QTabWidget::pane {
            border: 1px solid #bdc3c7;
            border-radius: 4px;
        }
        QTabBar::tab {
            background-color: #ecf0f1;
            border: 1px solid #bdc3c7;
            padding: 8px 16px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #3498db;
            color: white;
        }
        QSlider::groove:horizontal {
            border: 1px solid #bdc3c7;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
        }
        QSlider::handle:horizontal {
            background: #3498db;
            border: 1px solid #2980b9;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }
        QProgressBar {
            border: 1px solid #bdc3c7;
            border-radius: 4px;
            background-color: #ecf0f1;
        }
        QProgressBar::chunk {
            background-color: #27ae60;
            border-radius: 3px;
        }
    )");
}

// Getters
int ControlPanel::getServerPort() const
{
    return m_portSpinBox ? m_portSpinBox->value() : 4444;
}

QString ControlPanel::getServerPassword() const
{
    return m_passwordLineEdit ? m_passwordLineEdit->text() : "admin";
}

int ControlPanel::getMaxConnections() const
{
    return m_maxConnectionsSpinBox ? m_maxConnectionsSpinBox->value() : 10;
}

int ControlPanel::getCompressionLevel() const
{
    return m_compressionSlider ? m_compressionSlider->value() : 6;
}

int ControlPanel::getUpdateInterval() const
{
    return m_updateIntervalSpinBox ? m_updateIntervalSpinBox->value() : 50;
}

bool ControlPanel::isServerRunning() const
{
    return m_serverRunning;
}

// Setters
void ControlPanel::setServerPort(int port)
{
    if (m_portSpinBox) {
        m_portSpinBox->setValue(port);
    }
}

void ControlPanel::setServerPassword(const QString& password)
{
    if (m_passwordLineEdit) {
        m_passwordLineEdit->setText(password);
    }
}

// Slots
void ControlPanel::updateServerStatus(bool running, int port, int connections)
{
    m_serverRunning = running;
    m_activeConnections = connections;

    if (running) {
        m_serverStatusLabel->setText(QString("Server Status: Running on port %1").arg(port));
        m_serverStatusLabel->setStyleSheet("font-weight: bold; color: #27ae60;");
        m_serverStatusProgress->setValue(100);
        m_startServerButton->setEnabled(false);
        m_stopServerButton->setEnabled(true);
    } else {
        m_serverStatusLabel->setText("Server Status: Stopped");
        m_serverStatusLabel->setStyleSheet("font-weight: bold; color: #d13438;");
        m_serverStatusProgress->setValue(0);
        m_startServerButton->setEnabled(true);
        m_stopServerButton->setEnabled(false);
    }

    if (m_activeConnectionsLabel) {
        m_activeConnectionsLabel->setText(QString("Active Connections: %1").arg(connections));
    }

    updateUIState();
}

void ControlPanel::addConnection(const QString& clientId, const QString& clientIP, const QString& connectTime)
{
    if (!m_connectionTable) return;

    int row = m_connectionTable->rowCount();
    m_connectionTable->insertRow(row);

    m_connectionTable->setItem(row, 0, new QTableWidgetItem(clientId));
    m_connectionTable->setItem(row, 1, new QTableWidgetItem(clientIP));
    m_connectionTable->setItem(row, 2, new QTableWidgetItem(connectTime));
    m_connectionTable->setItem(row, 3, new QTableWidgetItem("0 bytes"));
    m_connectionTable->setItem(row, 4, new QTableWidgetItem("0 bytes"));

    addLogMessage("INFO", QString("Client connected: %1 from %2").arg(clientId, clientIP));
}

void ControlPanel::removeConnection(const QString& clientId)
{
    if (!m_connectionTable) return;

    for (int row = 0; row < m_connectionTable->rowCount(); ++row) {
        auto* item = m_connectionTable->item(row, 0);
        if (item && item->text() == clientId) {
            m_connectionTable->removeRow(row);
            addLogMessage("INFO", QString("Client disconnected: %1").arg(clientId));
            break;
        }
    }
}

void ControlPanel::updateConnectionStats(const QString& clientId, qint64 bytesReceived, qint64 bytesSent)
{
    if (!m_connectionTable) return;

    for (int row = 0; row < m_connectionTable->rowCount(); ++row) {
        auto* item = m_connectionTable->item(row, 0);
        if (item && item->text() == clientId) {
            m_connectionTable->setItem(row, 3, new QTableWidgetItem(QString("%1 bytes").arg(bytesReceived)));
            m_connectionTable->setItem(row, 4, new QTableWidgetItem(QString("%1 bytes").arg(bytesSent)));
            break;
        }
    }

    m_totalBytesReceived += bytesReceived;
    m_totalBytesSent += bytesSent;

    if (m_totalBytesLabel) {
        qint64 total = m_totalBytesReceived + m_totalBytesSent;
        QString unit = "bytes";
        double value = total;

        if (value >= 1024 * 1024 * 1024) {
            value /= (1024 * 1024 * 1024);
            unit = "GB";
        } else if (value >= 1024 * 1024) {
            value /= (1024 * 1024);
            unit = "MB";
        } else if (value >= 1024) {
            value /= 1024;
            unit = "KB";
        }

        m_totalBytesLabel->setText(QString("Total Data: %1 %2").arg(value, 0, 'f', 1).arg(unit));
    }
}

void ControlPanel::addLogMessage(const QString& level, const QString& message)
{
    if (!m_logsTextEdit) return;

    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString colorCode;

    if (level == "ERROR") colorCode = "#d13438";
    else if (level == "WARNING") colorCode = "#f39c12";
    else if (level == "INFO") colorCode = "#27ae60";
    else colorCode = "#2c3e50";

    QString logEntry = QString("<span style='color: #7f8c8d;'>[%1]</span> "
                              "<span style='color: %2; font-weight: bold;'>[%3]</span> "
                              "<span style='color: #2c3e50;'>%4</span>")
                              .arg(timestamp, colorCode, level, message);

    m_logsTextEdit->append(logEntry);

    if (m_autoScrollLogsCheckBox && m_autoScrollLogsCheckBox->isChecked()) {
        m_logsTextEdit->moveCursor(QTextCursor::End);
    }
}

// Private slots
void ControlPanel::onStartServer()
{
    int port = getServerPort();
    QString password = getServerPassword();
    int maxConnections = getMaxConnections();

    addLogMessage("INFO", QString("Starting HVNC server on port %1 with max %2 connections").arg(port).arg(maxConnections));
    emit startServerRequested(port, password, maxConnections);
}

void ControlPanel::onStopServer()
{
    addLogMessage("INFO", "Stopping HVNC server");
    emit stopServerRequested();
}

void ControlPanel::onSettingsChanged()
{
    emit settingsChanged();
}

void ControlPanel::onDisconnectClient()
{
    if (!m_connectionTable) return;

    int currentRow = m_connectionTable->currentRow();
    if (currentRow >= 0) {
        auto* item = m_connectionTable->item(currentRow, 0);
        if (item) {
            QString clientId = item->text();
            addLogMessage("INFO", QString("Disconnecting client: %1").arg(clientId));
            emit disconnectClientRequested(clientId);
        }
    }
}

void ControlPanel::onClearLogs()
{
    if (m_logsTextEdit) {
        m_logsTextEdit->clear();
        addLogMessage("INFO", "Logs cleared");
    }
}

void ControlPanel::onConnectionTableContextMenu(const QPoint& pos)
{
    if (!m_connectionTable) return;

    QTableWidgetItem* item = m_connectionTable->itemAt(pos);
    if (!item) return;

    QMenu contextMenu(this);
    QAction* disconnectAction = contextMenu.addAction("Disconnect Client");
    QAction* viewDetailsAction = contextMenu.addAction("View Details");

    QAction* selectedAction = contextMenu.exec(m_connectionTable->mapToGlobal(pos));

    if (selectedAction == disconnectAction) {
        onDisconnectClient();
    } else if (selectedAction == viewDetailsAction) {
        // TODO: Implement client details dialog
        addLogMessage("INFO", "Client details view not yet implemented");
    }
}

void ControlPanel::updateUIState()
{
    // Update UI elements based on current state
    if (m_portSpinBox) {
        m_portSpinBox->setEnabled(!m_serverRunning);
    }
    if (m_passwordLineEdit) {
        m_passwordLineEdit->setEnabled(!m_serverRunning);
    }
    if (m_maxConnectionsSpinBox) {
        m_maxConnectionsSpinBox->setEnabled(!m_serverRunning);
    }
}
