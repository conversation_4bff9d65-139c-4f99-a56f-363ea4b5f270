# HVNC GUI Design - Windows Qt6 Project File
# Professional Windows GUI for HVNC Server Management and Desktop Viewing

QT += core widgets network concurrent

CONFIG += c++20
CONFIG += debug_and_release
CONFIG += warn_on
CONFIG += windows

TARGET = HVNCDesign
TEMPLATE = app

# Version information
VERSION = 1.0.0
QMAKE_TARGET_COMPANY = "HVNC Team"
QMAKE_TARGET_PRODUCT = "HVNC Design for Windows"
QMAKE_TARGET_DESCRIPTION = "Professional Qt6 Windows GUI for HVNC Server Management"
QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2024 HVNC Team"

# Include directories
INCLUDEPATH += $$PWD/include

# Source files (Windows-only)
SOURCES += \
    src/main.cpp \
    src/MainWindow.cpp \
    src/DesktopViewer.cpp \
    src/ServerManager.cpp \
    src/StyleManager.cpp \
    src/ImageProcessor.cpp \
    src/ConnectionManager.cpp

# Header files (Windows-only)
HEADERS += \
    include/MainWindow.h \
    include/DesktopViewer.h \
    include/ServerManager.h \
    include/StyleManager.h \
    include/ImageProcessor.h \
    include/ConnectionManager.h \
    include/Common.h

# Platform specific configurations
win32 {
    # Windows specific libraries (including HVNC networking)
    LIBS += -lws2_32 -luser32 -lgdi32 -lkernel32 -ladvapi32
    LIBS += -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -lcomctl32
    LIBS += -lopengl32 -lglu32 -lnetapi32 -liphlpapi

    # Windows specific defines
    DEFINES += WIN32_LEAN_AND_MEAN NOMINMAX _CRT_SECURE_NO_WARNINGS WINDOWS_PLATFORM
    
    # MSVC specific options
    msvc {
        QMAKE_CXXFLAGS += /W4 /permissive- /Zc:__cplusplus /utf-8
    }
    
    # MinGW specific options
    mingw {
        QMAKE_CXXFLAGS += -Wall -Wextra -Wpedantic
    }
}

# Windows output directories
CONFIG(debug, debug|release) {
    DESTDIR = $$PWD/windows/debug
    OBJECTS_DIR = $$PWD/windows/debug/obj
    MOC_DIR = $$PWD/windows/debug/moc
    RCC_DIR = $$PWD/windows/debug/rcc
    UI_DIR = $$PWD/windows/debug/ui
}

CONFIG(release, debug|release) {
    DESTDIR = $$PWD/windows/release
    OBJECTS_DIR = $$PWD/windows/release/obj
    MOC_DIR = $$PWD/windows/release/moc
    RCC_DIR = $$PWD/windows/release/rcc
    UI_DIR = $$PWD/windows/release/ui
}

# Create output directories
QMAKE_PRE_LINK += $$QMAKE_MKDIR $$shell_path($$DESTDIR)

# Clean targets
QMAKE_CLEAN += $$DESTDIR/$$TARGET*

# Minimum Qt version check
lessThan(QT_MAJOR_VERSION, 6) {
    error("This project requires Qt 6.0 or later")
}

# Build information
message("Building HVNC Design for Windows with Qt $$QT_VERSION")
message("Target: $$TARGET.exe")
message("Destination: $$DESTDIR")
