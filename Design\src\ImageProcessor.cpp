/**
 * @file ImageProcessor.cpp
 * @brief Implementation of ImageProcessor class
 */

#include "ImageProcessor.h"
#include <QBuffer>
#include <QImageReader>
#include <QImageWriter>
#include <QElapsedTimer>
#include <QDataStream>
#include <algorithm>

#ifdef _WIN32
#include <windows.h>
#include <winternl.h>
#pragma comment(lib, "ntdll.lib")
#endif

ImageProcessor::ImageProcessor(QObject* parent)
    : QObject(parent)
    , m_defaultCompressionType(CompressionType::LZNT1)
    , m_maxImageWidth(4096)
    , m_maxImageHeight(4096)
    , m_enableOptimization(true)
    , m_defaultGamma(1.0)
    , m_transparentColor(255, 174, 201) // RGB(255, 174, 201) from existing server
    , m_totalProcessingTime(0)
    , m_processedImageCount(0)
    , m_isInitialized(false)
#ifdef _WIN32
    , m_ntdllHandle(nullptr)
    , m_rtlDecompressBuffer(nullptr)
#endif
{
    initialize();
}

ImageProcessor::~ImageProcessor() {
#ifdef _WIN32
    if (m_ntdllHandle) {
        FreeLibrary(static_cast<HMODULE>(m_ntdllHandle));
    }
#endif
    
    HVNC_DEBUG() << "ImageProcessor destroyed";
}

void ImageProcessor::initialize() {
    if (m_isInitialized) return;
    
    HVNC_DEBUG() << "Initializing ImageProcessor";
    
    setupCompressionSupport();
    
    m_isInitialized = true;
    HVNC_INFO() << "ImageProcessor initialized successfully";
}

void ImageProcessor::setupCompressionSupport() {
    m_supportedTypes.clear();
    
    // Always support no compression
    m_supportedTypes.append(CompressionType::None);
    
    // Check for Zlib support (Qt built-in)
    m_supportedTypes.append(CompressionType::Zlib);
    
#ifdef _WIN32
    // Setup Windows LZNT1 compression support
    m_ntdllHandle = LoadLibraryA("ntdll.dll");
    if (m_ntdllHandle) {
        m_rtlDecompressBuffer = GetProcAddress(static_cast<HMODULE>(m_ntdllHandle), "RtlDecompressBuffer");
        if (m_rtlDecompressBuffer) {
            m_supportedTypes.append(CompressionType::LZNT1);
            HVNC_DEBUG() << "LZNT1 compression support enabled";
        }
    }
#endif
    
    HVNC_INFO() << "Compression support initialized:" << m_supportedTypes.size() << "types";
}

QImage ImageProcessor::processImageData(const QByteArray& data, int width, int height, ImageFormat format) {
    if (data.isEmpty() || !validateImageDimensions(width, height)) {
        emit processingError("Invalid image data or dimensions");
        return QImage();
    }
    
    QElapsedTimer timer;
    timer.start();
    
    QImage result;
    
    try {
        switch (format) {
            case ImageFormat::Raw24:
                result = convertRawToImage(data, width, height, 3);
                break;
            case ImageFormat::Raw32:
                result = convertRawToImage(data, width, height, 4);
                break;
            case ImageFormat::Compressed:
                // Assume LZNT1 compression for compatibility with existing server
                {
                    int expectedSize = calculateExpectedDataSize(width, height, 3);
                    QByteArray decompressed = decompressImageData(data, expectedSize, CompressionType::LZNT1);
                    if (!decompressed.isEmpty()) {
                        result = convertRawToImage(decompressed, width, height, 3);
                    }
                }
                break;
            case ImageFormat::JPEG:
            case ImageFormat::PNG:
                {
                    QBuffer buffer;
                    buffer.setData(data);
                    buffer.open(QIODevice::ReadOnly);
                    
                    QImageReader reader(&buffer);
                    result = reader.read();
                }
                break;
        }
        
        if (!result.isNull()) {
            // Apply transparency if needed
            if (format == ImageFormat::Raw24 || format == ImageFormat::Compressed) {
                result = applyTransparency(result, m_transparentColor);
            }
            
            // Optimize for display if enabled
            if (m_enableOptimization) {
                result = optimizeForDisplay(result);
            }
        }
        
    } catch (const std::exception& e) {
        emit processingError(QString("Image processing failed: %1").arg(e.what()));
        return QImage();
    }
    
    qint64 processingTime = timer.elapsed();
    m_totalProcessingTime += processingTime;
    m_processedImageCount++;
    
    if (result.isNull()) {
        emit processingError("Failed to process image data");
    } else {
        emit imageProcessed(result);
        HVNC_DEBUG() << "Image processed successfully in" << processingTime << "ms";
    }
    
    return result;
}

QByteArray ImageProcessor::decompressImageData(const QByteArray& compressedData, int uncompressedSize, CompressionType compressionType) {
    if (compressedData.isEmpty() || uncompressedSize <= 0) {
        return QByteArray();
    }
    
    if (!isCompressionSupported(compressionType)) {
        HVNC_WARNING() << "Unsupported compression type:" << static_cast<int>(compressionType);
        return QByteArray();
    }
    
    switch (compressionType) {
        case CompressionType::None:
            return compressedData;
        case CompressionType::LZNT1:
            return decompressLZNT1(compressedData, uncompressedSize);
        case CompressionType::Zlib:
            return decompressZlib(compressedData);
        case CompressionType::LZ4:
            return decompressLZ4(compressedData, uncompressedSize);
        default:
            return QByteArray();
    }
}

QImage ImageProcessor::convertRawToImage(const QByteArray& data, int width, int height, int bytesPerPixel) {
    if (data.isEmpty() || !validateImageDimensions(width, height)) {
        return QImage();
    }
    
    int expectedSize = calculateExpectedDataSize(width, height, bytesPerPixel);
    if (data.size() < expectedSize) {
        HVNC_WARNING() << "Insufficient image data:" << data.size() << "expected:" << expectedSize;
        return QImage();
    }
    
    QImage::Format imageFormat;
    if (bytesPerPixel == 3) {
        imageFormat = QImage::Format_RGB888;
    } else if (bytesPerPixel == 4) {
        imageFormat = QImage::Format_RGBA8888;
    } else {
        HVNC_WARNING() << "Unsupported bytes per pixel:" << bytesPerPixel;
        return QImage();
    }
    
    // Create image from raw data
    QImage image(reinterpret_cast<const uchar*>(data.constData()), width, height, imageFormat);
    
    // Convert BGR to RGB if needed (Windows bitmap format)
    if (bytesPerPixel == 3) {
        image = image.rgbSwapped();
    }
    
    // Create a copy to ensure data ownership
    return image.copy();
}

QImage ImageProcessor::applyTransparency(const QImage& image, const QColor& transparentColor) {
    if (image.isNull()) {
        return image;
    }
    
    QImage result = image.convertToFormat(QImage::Format_ARGB32);
    
    // Replace transparent color with actual transparency
    QRgb transparentRgb = transparentColor.rgb();
    QRgb transparent = qRgba(0, 0, 0, 0);
    
    for (int y = 0; y < result.height(); ++y) {
        QRgb* line = reinterpret_cast<QRgb*>(result.scanLine(y));
        for (int x = 0; x < result.width(); ++x) {
            if ((line[x] & 0x00FFFFFF) == (transparentRgb & 0x00FFFFFF)) {
                line[x] = transparent;
            }
        }
    }
    
    return result;
}

QImage ImageProcessor::scaleImage(const QImage& image, const QSize& targetSize, bool smooth) {
    if (image.isNull() || targetSize.isEmpty()) {
        return image;
    }
    
    Qt::TransformationMode mode = smooth ? Qt::SmoothTransformation : Qt::FastTransformation;
    return image.scaled(targetSize, Qt::KeepAspectRatio, mode);
}

QImage ImageProcessor::optimizeForDisplay(const QImage& image) {
    if (image.isNull()) {
        return image;
    }
    
    // Convert to optimal format for display
    QImage optimized = image;
    
    // Convert to RGB32 for better performance on most displays
    if (image.format() != QImage::Format_RGB32 && image.format() != QImage::Format_ARGB32) {
        optimized = image.convertToFormat(QImage::Format_RGB32);
    }
    
    return optimized;
}

QList<ImageProcessor::CompressionType> ImageProcessor::getSupportedCompressionTypes() const {
    return m_supportedTypes;
}

bool ImageProcessor::isCompressionSupported(CompressionType type) const {
    return m_supportedTypes.contains(type);
}

void ImageProcessor::setDefaultCompressionType(CompressionType type) {
    if (isCompressionSupported(type)) {
        m_defaultCompressionType = type;
    }
}

QByteArray ImageProcessor::decompressLZNT1(const QByteArray& compressedData, int uncompressedSize) {
#ifdef _WIN32
    if (!m_rtlDecompressBuffer) {
        return QByteArray();
    }
    
    QByteArray result(uncompressedSize, 0);
    ULONG finalSize = 0;
    
    typedef NTSTATUS (NTAPI *RtlDecompressBufferFunc)(
        USHORT CompressionFormat,
        PUCHAR UncompressedBuffer,
        ULONG UncompressedBufferSize,
        PUCHAR CompressedBuffer,
        ULONG CompressedBufferSize,
        PULONG FinalUncompressedSize
    );
    
    RtlDecompressBufferFunc decompressFunc = reinterpret_cast<RtlDecompressBufferFunc>(m_rtlDecompressBuffer);
    
    NTSTATUS status = decompressFunc(
        2, // COMPRESSION_FORMAT_LZNT1
        reinterpret_cast<PUCHAR>(result.data()),
        uncompressedSize,
        reinterpret_cast<PUCHAR>(const_cast<char*>(compressedData.constData())),
        compressedData.size(),
        &finalSize
    );
    
    if (status == 0 && finalSize > 0) { // STATUS_SUCCESS
        result.resize(finalSize);
        return result;
    }
#else
    Q_UNUSED(compressedData)
    Q_UNUSED(uncompressedSize)
#endif
    
    return QByteArray();
}

QByteArray ImageProcessor::decompressZlib(const QByteArray& compressedData) {
    return qUncompress(compressedData);
}

QByteArray ImageProcessor::decompressLZ4(const QByteArray& compressedData, int uncompressedSize) {
    // LZ4 decompression would require external library
    // For now, return empty array
    Q_UNUSED(compressedData)
    Q_UNUSED(uncompressedSize)
    return QByteArray();
}

bool ImageProcessor::validateImageDimensions(int width, int height) const {
    return (width > 0 && height > 0 && 
            width <= m_maxImageWidth && height <= m_maxImageHeight);
}

int ImageProcessor::calculateExpectedDataSize(int width, int height, int bytesPerPixel) const {
    return width * height * bytesPerPixel;
}

void ImageProcessor::convertBGRToRGB(QByteArray& data, int pixelCount) {
    if (data.size() < pixelCount * 3) {
        return;
    }
    
    uchar* pixels = reinterpret_cast<uchar*>(data.data());
    for (int i = 0; i < pixelCount; ++i) {
        std::swap(pixels[i * 3], pixels[i * 3 + 2]); // Swap B and R
    }
}

QImage ImageProcessor::applyGammaCorrection(const QImage& image, double gamma) {
    if (image.isNull() || gamma <= 0.0) {
        return image;
    }
    
    QImage result = image.copy();
    
    // Create gamma correction lookup table
    uchar gammaTable[256];
    for (int i = 0; i < 256; ++i) {
        gammaTable[i] = static_cast<uchar>(qBound(0.0, 255.0 * qPow(i / 255.0, 1.0 / gamma), 255.0));
    }
    
    // Apply gamma correction
    for (int y = 0; y < result.height(); ++y) {
        QRgb* line = reinterpret_cast<QRgb*>(result.scanLine(y));
        for (int x = 0; x < result.width(); ++x) {
            QRgb pixel = line[x];
            line[x] = qRgba(
                gammaTable[qRed(pixel)],
                gammaTable[qGreen(pixel)],
                gammaTable[qBlue(pixel)],
                qAlpha(pixel)
            );
        }
    }
    
    return result;
}
