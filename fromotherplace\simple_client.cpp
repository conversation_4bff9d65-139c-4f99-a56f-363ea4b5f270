// simple_client.cpp - Simplified screenshot capture with raw BMP output
#define WIN32_LEAN_AND_MEAN
#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <fstream>

#pragma comment(lib, "Ws2_32.lib")
#pragma comment(lib, "User32.lib")
#pragma comment(lib, "Gdi32.lib")

// Simple BMP file header structures
#pragma pack(push, 1)
struct BMPFileHeader {
    uint16_t bfType = 0x4D42;      // "BM"
    uint32_t bfSize;
    uint16_t bfReserved1 = 0;
    uint16_t bfReserved2 = 0;
    uint32_t bfOffBits = 54;       // Offset to pixel data
};

struct BMPInfoHeader {
    uint32_t biSize = 40;
    int32_t biWidth;
    int32_t biHeight;
    uint16_t biPlanes = 1;
    uint16_t biBitCount = 24;
    uint32_t biCompression = 0;    // BI_RGB
    uint32_t biSizeImage;
    int32_t biXPelsPerMeter = 0;
    int32_t biYPelsPerMeter = 0;
    uint32_t biClrUsed = 0;
    uint32_t biClrImportant = 0;
};
#pragma pack(pop)

std::vector<uint8_t> CaptureScreenToBMP() {
    // Get screen dimensions
    int width = GetSystemMetrics(SM_CXSCREEN);
    int height = GetSystemMetrics(SM_CYSCREEN);
    
    std::cout << "[*] Screen resolution: " << width << "x" << height << "\n";
    
    // Create device contexts
    HDC hScreenDC = GetDC(NULL);
    HDC hMemoryDC = CreateCompatibleDC(hScreenDC);
    
    // Create bitmap
    HBITMAP hBitmap = CreateCompatibleBitmap(hScreenDC, width, height);
    HGDIOBJ hOldBitmap = SelectObject(hMemoryDC, hBitmap);
    
    // Capture the screen
    BitBlt(hMemoryDC, 0, 0, width, height, hScreenDC, 0, 0, SRCCOPY);
    
    // Calculate BMP parameters
    int rowSize = ((width * 3 + 3) / 4) * 4;  // 4-byte aligned
    int imageSize = rowSize * height;
    int fileSize = 54 + imageSize;
    
    // Create BMP headers
    BMPFileHeader fileHeader;
    fileHeader.bfSize = fileSize;
    
    BMPInfoHeader infoHeader;
    infoHeader.biWidth = width;
    infoHeader.biHeight = height;  // Positive = bottom-up
    infoHeader.biSizeImage = imageSize;
    
    // Prepare bitmap info for GetDIBits
    BITMAPINFO bmi = {};
    bmi.bmiHeader.biSize = sizeof(BITMAPINFOHEADER);
    bmi.bmiHeader.biWidth = width;
    bmi.bmiHeader.biHeight = height;  // Positive for bottom-up
    bmi.bmiHeader.biPlanes = 1;
    bmi.bmiHeader.biBitCount = 24;
    bmi.bmiHeader.biCompression = BI_RGB;
    
    // Allocate buffer for pixel data
    std::vector<uint8_t> pixelData(imageSize);
    
    // Get the bitmap bits
    int result = GetDIBits(hMemoryDC, hBitmap, 0, height, pixelData.data(), &bmi, DIB_RGB_COLORS);
    
    std::cout << "[*] GetDIBits result: " << result << " (should be " << height << ")\n";
    
    // Create complete BMP file in memory
    std::vector<uint8_t> bmpData;
    bmpData.reserve(fileSize);
    
    // Add file header
    bmpData.insert(bmpData.end(), (uint8_t*)&fileHeader, (uint8_t*)&fileHeader + sizeof(fileHeader));
    
    // Add info header
    bmpData.insert(bmpData.end(), (uint8_t*)&infoHeader, (uint8_t*)&infoHeader + sizeof(infoHeader));
    
    // Add pixel data (already in correct BGR format for BMP)
    bmpData.insert(bmpData.end(), pixelData.begin(), pixelData.end());
    
    // Cleanup
    SelectObject(hMemoryDC, hOldBitmap);
    DeleteObject(hBitmap);
    DeleteDC(hMemoryDC);
    ReleaseDC(NULL, hScreenDC);
    
    std::cout << "[*] BMP data size: " << bmpData.size() << " bytes\n";
    
    return bmpData;
}

bool SendToServer(const std::vector<uint8_t>& data, const std::string& serverIP = "127.0.0.1", int port = 8888) {
    WSADATA wsa;
    WSAStartup(MAKEWORD(2, 2), &wsa);
    
    SOCKET sock = socket(AF_INET, SOCK_STREAM, 0);
    if (sock == INVALID_SOCKET) {
        std::cerr << "[!] Socket creation failed\n";
        WSACleanup();
        return false;
    }
    
    sockaddr_in server = {};
    server.sin_family = AF_INET;
    inet_pton(AF_INET, serverIP.c_str(), &server.sin_addr);
    server.sin_port = htons(port);
    
    std::cout << "[*] Connecting to " << serverIP << ":" << port << "...\n";
    
    if (connect(sock, (sockaddr*)&server, sizeof(server)) == SOCKET_ERROR) {
        std::cerr << "[!] Connection failed\n";
        closesocket(sock);
        WSACleanup();
        return false;
    }
    
    std::cout << "[+] Connected to server\n";
    
    // Send data size first
    uint32_t dataSize = static_cast<uint32_t>(data.size());
    send(sock, (char*)&dataSize, sizeof(dataSize), 0);
    
    // Send data
    size_t totalSent = 0;
    while (totalSent < data.size()) {
        int sent = send(sock, (char*)data.data() + totalSent, data.size() - totalSent, 0);
        if (sent == SOCKET_ERROR) {
            std::cerr << "[!] Send failed\n";
            closesocket(sock);
            WSACleanup();
            return false;
        }
        totalSent += sent;
    }
    
    // Wait for acknowledgment
    char ack[3] = {0};
    recv(sock, ack, 2, 0);
    
    closesocket(sock);
    WSACleanup();
    
    std::cout << "[+] Screenshot sent successfully\n";
    return true;
}

int main() {
    std::cout << "=== Simple Screenshot Capture Test ===\n\n";
    
    // Capture screen
    auto bmpData = CaptureScreenToBMP();
    
    if (bmpData.empty()) {
        std::cerr << "[!] Failed to capture screen\n";
        return 1;
    }
    
    // Save locally for testing
    std::ofstream localFile("test_screenshot.bmp", std::ios::binary);
    localFile.write((char*)bmpData.data(), bmpData.size());
    localFile.close();
    std::cout << "[+] Saved local copy as 'test_screenshot.bmp'\n";
    
    // Send to server
    if (!SendToServer(bmpData)) {
        std::cerr << "[!] Failed to send to server\n";
        return 1;
    }
    
    std::cout << "\n[+] Test completed successfully!\n";
    std::cout << "Check 'test_screenshot.bmp' to verify the image quality.\n";
    
    return 0;
}
