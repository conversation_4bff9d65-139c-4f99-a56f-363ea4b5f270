/****************************************************************************
** Meta object code from reading C++ file 'ServerManager.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../include/ServerManager.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ServerManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSServerManagerENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSServerManagerENDCLASS = QtMocHelpers::stringData(
    "ServerManager",
    "serverStateChanged",
    "",
    "ServerState",
    "state",
    "clientConnected",
    "ClientId",
    "clientId",
    "clientInfo",
    "clientDisconnected",
    "desktopImageReceived",
    "image",
    "desktopRegionUpdated",
    "x",
    "y",
    "cursorPositionChanged",
    "position",
    "statusMessage",
    "message",
    "errorOccurred",
    "title",
    "dataTransferUpdate",
    "bytesReceived",
    "bytesSent",
    "onNewConnection",
    "onClientDisconnected",
    "onClientDataReady",
    "onServerError",
    "QAbstractSocket::SocketError",
    "error",
    "onHeartbeatTimer",
    "onStatisticsTimer"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSServerManagerENDCLASS_t {
    uint offsetsAndSizes[64];
    char stringdata0[14];
    char stringdata1[19];
    char stringdata2[1];
    char stringdata3[12];
    char stringdata4[6];
    char stringdata5[16];
    char stringdata6[9];
    char stringdata7[9];
    char stringdata8[11];
    char stringdata9[19];
    char stringdata10[21];
    char stringdata11[6];
    char stringdata12[21];
    char stringdata13[2];
    char stringdata14[2];
    char stringdata15[22];
    char stringdata16[9];
    char stringdata17[14];
    char stringdata18[8];
    char stringdata19[14];
    char stringdata20[6];
    char stringdata21[19];
    char stringdata22[14];
    char stringdata23[10];
    char stringdata24[16];
    char stringdata25[21];
    char stringdata26[18];
    char stringdata27[14];
    char stringdata28[29];
    char stringdata29[6];
    char stringdata30[17];
    char stringdata31[18];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSServerManagerENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSServerManagerENDCLASS_t qt_meta_stringdata_CLASSServerManagerENDCLASS = {
    {
        QT_MOC_LITERAL(0, 13),  // "ServerManager"
        QT_MOC_LITERAL(14, 18),  // "serverStateChanged"
        QT_MOC_LITERAL(33, 0),  // ""
        QT_MOC_LITERAL(34, 11),  // "ServerState"
        QT_MOC_LITERAL(46, 5),  // "state"
        QT_MOC_LITERAL(52, 15),  // "clientConnected"
        QT_MOC_LITERAL(68, 8),  // "ClientId"
        QT_MOC_LITERAL(77, 8),  // "clientId"
        QT_MOC_LITERAL(86, 10),  // "clientInfo"
        QT_MOC_LITERAL(97, 18),  // "clientDisconnected"
        QT_MOC_LITERAL(116, 20),  // "desktopImageReceived"
        QT_MOC_LITERAL(137, 5),  // "image"
        QT_MOC_LITERAL(143, 20),  // "desktopRegionUpdated"
        QT_MOC_LITERAL(164, 1),  // "x"
        QT_MOC_LITERAL(166, 1),  // "y"
        QT_MOC_LITERAL(168, 21),  // "cursorPositionChanged"
        QT_MOC_LITERAL(190, 8),  // "position"
        QT_MOC_LITERAL(199, 13),  // "statusMessage"
        QT_MOC_LITERAL(213, 7),  // "message"
        QT_MOC_LITERAL(221, 13),  // "errorOccurred"
        QT_MOC_LITERAL(235, 5),  // "title"
        QT_MOC_LITERAL(241, 18),  // "dataTransferUpdate"
        QT_MOC_LITERAL(260, 13),  // "bytesReceived"
        QT_MOC_LITERAL(274, 9),  // "bytesSent"
        QT_MOC_LITERAL(284, 15),  // "onNewConnection"
        QT_MOC_LITERAL(300, 20),  // "onClientDisconnected"
        QT_MOC_LITERAL(321, 17),  // "onClientDataReady"
        QT_MOC_LITERAL(339, 13),  // "onServerError"
        QT_MOC_LITERAL(353, 28),  // "QAbstractSocket::SocketError"
        QT_MOC_LITERAL(382, 5),  // "error"
        QT_MOC_LITERAL(388, 16),  // "onHeartbeatTimer"
        QT_MOC_LITERAL(405, 17)   // "onStatisticsTimer"
    },
    "ServerManager",
    "serverStateChanged",
    "",
    "ServerState",
    "state",
    "clientConnected",
    "ClientId",
    "clientId",
    "clientInfo",
    "clientDisconnected",
    "desktopImageReceived",
    "image",
    "desktopRegionUpdated",
    "x",
    "y",
    "cursorPositionChanged",
    "position",
    "statusMessage",
    "message",
    "errorOccurred",
    "title",
    "dataTransferUpdate",
    "bytesReceived",
    "bytesSent",
    "onNewConnection",
    "onClientDisconnected",
    "onClientDataReady",
    "onServerError",
    "QAbstractSocket::SocketError",
    "error",
    "onHeartbeatTimer",
    "onStatisticsTimer"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSServerManagerENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       9,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  104,    2, 0x06,    1 /* Public */,
       5,    2,  107,    2, 0x06,    3 /* Public */,
       9,    1,  112,    2, 0x06,    6 /* Public */,
      10,    2,  115,    2, 0x06,    8 /* Public */,
      12,    4,  120,    2, 0x06,   11 /* Public */,
      15,    2,  129,    2, 0x06,   16 /* Public */,
      17,    1,  134,    2, 0x06,   19 /* Public */,
      19,    2,  137,    2, 0x06,   21 /* Public */,
      21,    3,  142,    2, 0x06,   24 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      24,    0,  149,    2, 0x08,   28 /* Private */,
      25,    0,  150,    2, 0x08,   29 /* Private */,
      26,    0,  151,    2, 0x08,   30 /* Private */,
      27,    1,  152,    2, 0x08,   31 /* Private */,
      30,    0,  155,    2, 0x08,   33 /* Private */,
      31,    0,  156,    2, 0x08,   34 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, 0x80000000 | 6, QMetaType::QString,    7,    8,
    QMetaType::Void, 0x80000000 | 6,    7,
    QMetaType::Void, 0x80000000 | 6, QMetaType::QImage,    7,   11,
    QMetaType::Void, 0x80000000 | 6, QMetaType::QImage, QMetaType::Int, QMetaType::Int,    7,   11,   13,   14,
    QMetaType::Void, 0x80000000 | 6, QMetaType::QPoint,    7,   16,
    QMetaType::Void, QMetaType::QString,   18,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   20,   18,
    QMetaType::Void, 0x80000000 | 6, QMetaType::LongLong, QMetaType::LongLong,    7,   22,   23,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 28,   29,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ServerManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSServerManagerENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSServerManagerENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSServerManagerENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ServerManager, std::true_type>,
        // method 'serverStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ServerState, std::false_type>,
        // method 'clientConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clientDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        // method 'desktopImageReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QImage &, std::false_type>,
        // method 'desktopRegionUpdated'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QImage &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'cursorPositionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'statusMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'errorOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'dataTransferUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<qint64, std::false_type>,
        QtPrivate::TypeAndForceComplete<qint64, std::false_type>,
        // method 'onNewConnection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClientDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClientDataReady'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onServerError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractSocket::SocketError, std::false_type>,
        // method 'onHeartbeatTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStatisticsTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ServerManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ServerManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->serverStateChanged((*reinterpret_cast< std::add_pointer_t<ServerState>>(_a[1]))); break;
        case 1: _t->clientConnected((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 2: _t->clientDisconnected((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1]))); break;
        case 3: _t->desktopImageReceived((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImage>>(_a[2]))); break;
        case 4: _t->desktopRegionUpdated((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImage>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[4]))); break;
        case 5: _t->cursorPositionChanged((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 6: _t->statusMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 7: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 8: _t->dataTransferUpdate((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3]))); break;
        case 9: _t->onNewConnection(); break;
        case 10: _t->onClientDisconnected(); break;
        case 11: _t->onClientDataReady(); break;
        case 12: _t->onServerError((*reinterpret_cast< std::add_pointer_t<QAbstractSocket::SocketError>>(_a[1]))); break;
        case 13: _t->onHeartbeatTimer(); break;
        case 14: _t->onStatisticsTimer(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
        case 12:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType(); break;
            case 0:
                *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType::fromType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ServerManager::*)(ServerState );
            if (_t _q_method = &ServerManager::serverStateChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(ClientId , const QString & );
            if (_t _q_method = &ServerManager::clientConnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(ClientId );
            if (_t _q_method = &ServerManager::clientDisconnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(ClientId , const QImage & );
            if (_t _q_method = &ServerManager::desktopImageReceived; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(ClientId , const QImage & , int , int );
            if (_t _q_method = &ServerManager::desktopRegionUpdated; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(ClientId , const QPoint & );
            if (_t _q_method = &ServerManager::cursorPositionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(const QString & );
            if (_t _q_method = &ServerManager::statusMessage; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(const QString & , const QString & );
            if (_t _q_method = &ServerManager::errorOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
        {
            using _t = void (ServerManager::*)(ClientId , qint64 , qint64 );
            if (_t _q_method = &ServerManager::dataTransferUpdate; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 8;
                return;
            }
        }
    }
}

const QMetaObject *ServerManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ServerManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSServerManagerENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ServerManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void ServerManager::serverStateChanged(ServerState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ServerManager::clientConnected(ClientId _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ServerManager::clientDisconnected(ClientId _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ServerManager::desktopImageReceived(ClientId _t1, const QImage & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ServerManager::desktopRegionUpdated(ClientId _t1, const QImage & _t2, int _t3, int _t4)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t4))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ServerManager::cursorPositionChanged(ClientId _t1, const QPoint & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ServerManager::statusMessage(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ServerManager::errorOccurred(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}

// SIGNAL 8
void ServerManager::dataTransferUpdate(ClientId _t1, qint64 _t2, qint64 _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 8, _a);
}
QT_WARNING_POP
