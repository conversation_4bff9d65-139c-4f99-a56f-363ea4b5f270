# HVNC GUI Design - Modern Qt6 C++20 Application
# Professional GUI for HVNC Server Management and Desktop Viewing
cmake_minimum_required(VERSION 3.21)

project(HVNCDesign 
    VERSION 1.0.0
    DESCRIPTION "Professional Qt6 GUI for HVNC Server Management"
    LANGUAGES CXX
)

# Set C++20 standard
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Enable modern CMake features
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Find Qt6 components (removed Multimedia for compatibility)
find_package(Qt6 REQUIRED COMPONENTS
    Core
    Widgets
    Network
    Concurrent
)

# Enable Qt6 automatic MOC, UIC, and RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# Set output directories
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)

# Include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/../Server
    ${CMAKE_CURRENT_SOURCE_DIR}/../common
    ${CMAKE_CURRENT_SOURCE_DIR}/../Client
)

# Source files (excluding problematic common files for now)
set(SOURCES
    src/main.cpp
    src/MainWindow.cpp
    src/DesktopViewer.cpp
    src/ServerManager.cpp
    src/StyleManager.cpp
    src/ImageProcessor.cpp
    src/ConnectionManager.cpp
)

# Header files
set(HEADERS
    include/MainWindow.h
    include/DesktopViewer.h
    include/ServerManager.h
    include/StyleManager.h
    include/ImageProcessor.h
    include/Common.h
)

# UI files (none currently - using code-based UI)
# set(UI_FILES
#     ui/MainWindow.ui
# )

# Resource files (none currently)
# set(RESOURCES
#     resources/resources.qrc
# )

# Create executable
add_executable(${PROJECT_NAME}
    ${SOURCES}
    ${HEADERS}
    # ${UI_FILES}
    # ${RESOURCES}
)

# Link Qt6 libraries
target_link_libraries(${PROJECT_NAME}
    Qt6::Core
    Qt6::Widgets
    Qt6::Network
    Qt6::Concurrent
)

# Windows specific libraries (including HVNC networking)
if(WIN32)
    target_link_libraries(${PROJECT_NAME}
        ws2_32
        user32
        gdi32
        kernel32
        advapi32
        shell32
        ole32
        oleaut32
        uuid
        comdlg32
        comctl32
        netapi32
        iphlpapi
        opengl32
        glu32
    )

    # Windows-specific definitions
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        WINDOWS_PLATFORM
        _WIN32_WINNT=0x0601
        UNICODE=0
        _UNICODE=0
        _MBCS
    )
endif()

# Compiler specific options
if(MSVC)
    target_compile_options(${PROJECT_NAME} PRIVATE
        /W4
        /permissive-
        /Zc:__cplusplus
        /utf-8
    )
    target_compile_definitions(${PROJECT_NAME} PRIVATE
        _CRT_SECURE_NO_WARNINGS
        NOMINMAX
        WIN32_LEAN_AND_MEAN
    )
else()
    target_compile_options(${PROJECT_NAME} PRIVATE
        -Wall
        -Wextra
        -Wpedantic
        -Wconversion
        -Wsign-conversion
    )
endif()

# Debug/Release configurations
if(CMAKE_BUILD_TYPE STREQUAL "Debug")
    target_compile_definitions(${PROJECT_NAME} PRIVATE DEBUG)
else()
    target_compile_definitions(${PROJECT_NAME} PRIVATE NDEBUG)
endif()

# Install configuration
install(TARGETS ${PROJECT_NAME}
    RUNTIME DESTINATION bin
    LIBRARY DESTINATION lib
    ARCHIVE DESTINATION lib
)

# Copy Qt6 runtime libraries on Windows
if(WIN32)
    find_program(WINDEPLOYQT_EXECUTABLE windeployqt HINTS ${Qt6_DIR}/../../../bin)
    if(WINDEPLOYQT_EXECUTABLE)
        add_custom_command(TARGET ${PROJECT_NAME} POST_BUILD
            COMMAND ${WINDEPLOYQT_EXECUTABLE} $<TARGET_FILE:${PROJECT_NAME}>
            COMMENT "Deploying Qt libraries"
        )
    endif()
endif()

# Create build info (optional - uncomment when BuildInfo.h.in is created)
# configure_file(
#     ${CMAKE_CURRENT_SOURCE_DIR}/include/BuildInfo.h.in
#     ${CMAKE_CURRENT_BINARY_DIR}/include/BuildInfo.h
#     @ONLY
# )
#
# target_include_directories(${PROJECT_NAME} PRIVATE ${CMAKE_CURRENT_BINARY_DIR}/include)
