/**
 * @file main.cpp
 * @brief Main entry point for HVNC Design GUI Application
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This is the main entry point for the HVNC Design application.
 * It initializes the Qt application, sets up logging, applies the
 * dark theme, and creates the main window.
 */

#include "Common.h"
#include "MainWindow.h"
#include "StyleManager.h"
// #include "BuildInfo.h" // Optional build info

#include <QApplication>
#include <QDir>
#include <QStandardPaths>
#include <QLoggingCategory>
#include <QStyleFactory>
#include <QFontDatabase>
#include <QSplashScreen>
#include <QPixmap>
#include <QTimer>

// Define logging category
Q_LOGGING_CATEGORY(hvncDesign, "hvnc.design")

/**
 * @brief Setup application logging
 * 
 * Configures Qt logging system to write to both console and file.
 * Creates log directory if it doesn't exist and sets up log rotation.
 */
void setupLogging() {
    // Create logs directory
    QString logDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation) + "/logs";
    QDir().mkpath(logDir);
    
    // Set logging rules
    QLoggingCategory::setFilterRules(
        "hvnc.design.debug=true\n"
        "hvnc.design.info=true\n"
        "hvnc.design.warning=true\n"
        "hvnc.design.critical=true\n"
        "qt.network.ssl.debug=false"
    );
    
    HVNC_INFO() << "HVNC Design application starting...";
    HVNC_INFO() << "Version: 1.0.0";
    HVNC_INFO() << "Build Info: Qt6 C++20 Release";
}

/**
 * @brief Setup application properties
 * @param app Reference to QApplication instance
 * 
 * Sets up application metadata, organization info, and other properties.
 */
void setupApplication(QApplication& app) {
    // Set application properties
    app.setApplicationName(HVNCDesign::APP_NAME);
    app.setApplicationVersion(HVNCDesign::APP_VERSION);
    app.setOrganizationName(HVNCDesign::APP_ORGANIZATION);
    app.setOrganizationDomain(HVNCDesign::APP_DOMAIN);
    
    // Set application icon
    app.setWindowIcon(QIcon(":/icons/app_icon.png"));
    
    // High DPI support is enabled by default in Qt6
    // app.setAttribute(Qt::AA_EnableHighDpiScaling); // Deprecated in Qt6
    // app.setAttribute(Qt::AA_UseHighDpiPixmaps);     // Deprecated in Qt6
    
    // Set application style
    app.setStyle(QStyleFactory::create("Fusion"));
    
    HVNC_INFO() << "Application properties configured";
}

/**
 * @brief Load custom fonts
 * 
 * Loads custom fonts from resources if available.
 */
void loadCustomFonts() {
    // Load custom fonts from resources
    QStringList fontPaths = {
        ":/fonts/SegoeUI.ttf",
        ":/fonts/SegoeUI-Bold.ttf",
        ":/fonts/SegoeUI-Italic.ttf",
        ":/fonts/Consolas.ttf"
    };
    
    for (const QString& fontPath : fontPaths) {
        int fontId = QFontDatabase::addApplicationFont(fontPath);
        if (fontId != -1) {
            QStringList fontFamilies = QFontDatabase::applicationFontFamilies(fontId);
            HVNC_DEBUG() << "Loaded font:" << fontFamilies;
        }
    }
}

/**
 * @brief Create and show splash screen
 * @param app Reference to QApplication instance
 * @return Pointer to splash screen (caller takes ownership)
 * 
 * Creates a professional splash screen with loading animation.
 */
QSplashScreen* createSplashScreen(QApplication& app) {
    // Create splash screen pixmap
    QPixmap splashPixmap(400, 300);
    splashPixmap.fill(QColor(HVNCDesign::Colors::BACKGROUND_PRIMARY));
    
    QPainter painter(&splashPixmap);
    painter.setRenderHint(QPainter::Antialiasing);
    
    // Draw application name
    QFont titleFont(HVNCDesign::Fonts::PRIMARY_FAMILY, HVNCDesign::Fonts::SIZE_XLARGE, QFont::Bold);
    painter.setFont(titleFont);
    painter.setPen(QColor(HVNCDesign::Colors::TEXT_PRIMARY));
    painter.drawText(splashPixmap.rect(), Qt::AlignCenter, HVNCDesign::APP_NAME);
    
    // Draw version
    QFont versionFont(HVNCDesign::Fonts::PRIMARY_FAMILY, HVNCDesign::Fonts::SIZE_NORMAL);
    painter.setFont(versionFont);
    painter.setPen(QColor(HVNCDesign::Colors::TEXT_SECONDARY));
    QRect versionRect = splashPixmap.rect();
    versionRect.setTop(versionRect.center().y() + 30);
    painter.drawText(versionRect, Qt::AlignCenter, QString("Version %1").arg(HVNCDesign::APP_VERSION));
    
    // Draw loading text
    QRect loadingRect = splashPixmap.rect();
    loadingRect.setTop(loadingRect.bottom() - 40);
    painter.drawText(loadingRect, Qt::AlignCenter, "Loading...");
    
    painter.end();
    
    // Create splash screen
    QSplashScreen* splash = new QSplashScreen(splashPixmap);
    splash->setWindowFlags(Qt::SplashScreen | Qt::WindowStaysOnTopHint);
    splash->show();
    
    // Process events to show splash screen
    app.processEvents();
    
    HVNC_DEBUG() << "Splash screen created and shown";
    return splash;
}

/**
 * @brief Main application entry point
 * @param argc Argument count
 * @param argv Argument values
 * @return Application exit code
 */
int main(int argc, char *argv[]) {
    // Create QApplication instance
    QApplication app(argc, argv);
    
    try {
        // Setup application
        setupApplication(app);
        setupLogging();
        loadCustomFonts();
        
        // Create splash screen
        QSplashScreen* splash = createSplashScreen(app);
        
        // Initialize style manager and apply dark theme
        StyleManager styleManager;
        styleManager.applyDarkTheme();
        
        splash->showMessage("Initializing components...", Qt::AlignBottom | Qt::AlignCenter, 
                           QColor(HVNCDesign::Colors::TEXT_SECONDARY));
        app.processEvents();
        
        // Create main window (must be created before timer to stay in scope)
        MainWindow* mainWindow = new MainWindow();

        // Small delay to show splash screen
        QTimer::singleShot(1500, [&, mainWindow, splash]() {
            // Close splash screen
            splash->finish(mainWindow);
            delete splash;

            // Show main window
            mainWindow->show();

            HVNC_INFO() << "Main window created and shown";
        });

        // Start event loop
        int result = app.exec();

        // Clean up main window
        delete mainWindow;
        
        HVNC_INFO() << "Application exiting with code:" << result;
        return result;
        
    } catch (const std::exception& e) {
        HVNC_CRITICAL() << "Unhandled exception in main:" << e.what();
        
        // Show error message to user
        QMessageBox::critical(nullptr, "Fatal Error", 
            QString("A fatal error occurred:\n%1\n\nThe application will now exit.").arg(e.what()));
        
        return -1;
    } catch (...) {
        HVNC_CRITICAL() << "Unknown exception in main";
        
        // Show generic error message
        QMessageBox::critical(nullptr, "Fatal Error", 
            "An unknown fatal error occurred.\nThe application will now exit.");
        
        return -1;
    }
}
