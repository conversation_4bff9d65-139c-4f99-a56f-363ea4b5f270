@echo off
:: ============================================================================
:: HVNC Design GUI Launcher
:: ============================================================================
:: Launches the HVNC Design GUI for Windows
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Design GUI Launcher                            ║%NC%
echo %BLUE%║                    Professional Qt6 GUI for Windows                         ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Look for the GUI executable
echo %CYAN%[SEARCH]%NC% Looking for HVNCDesign.exe...

set "GUI_FOUND=false"
set "GUI_PATH="

:: Check root directory first
if exist "..\HVNCDesign.exe" (
    set "GUI_PATH=..\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% GUI found in root directory
) else if exist "windows\release\HVNCDesign.exe" (
    set "GUI_PATH=windows\release\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% GUI found in windows\release directory
) else if exist "release\HVNCDesign.exe" (
    set "GUI_PATH=release\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% GUI found in release directory
) else if exist "HVNCDesign.exe" (
    set "GUI_PATH=HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% GUI found in current directory
)

if "%GUI_FOUND%"=="true" (
    echo %BLUE%[LAUNCH]%NC% Starting HVNC Design GUI...
    echo %CYAN%[PATH]%NC% %GUI_PATH%
    echo.
    
    start "" "!GUI_PATH!"
    
    if errorlevel 1 (
        echo %RED%[FAILED]%NC% Failed to launch GUI
        pause
        exit /b 1
    )
    
    echo %GREEN%[SUCCESS]%NC% HVNC Design GUI launched successfully!
    echo.
    echo %CYAN%Next Steps:%NC%
    echo   %WHITE%1.%NC% The Qt6 GUI is now running
    echo   %WHITE%2.%NC% Use the interface to configure HVNC server
    echo   %WHITE%3.%NC% Start the server from the GUI
    echo   %WHITE%4.%NC% Deploy Client.exe to target computers
    echo   %WHITE%5.%NC% Remote desktop captures will appear in the viewer
    
) else (
    echo %RED%[NOT FOUND]%NC% HVNCDesign.exe not found!
    echo.
    echo %YELLOW%[BUILD REQUIRED]%NC% You need to build the GUI first:
    echo   %WHITE%1.%NC% Run build-windows.bat to build the GUI
    echo   %WHITE%2.%NC% Or run the main build.bat from the root directory
    echo.
    pause
    exit /b 1
)

echo.
echo %GREEN%[COMPLETE]%NC% GUI launcher finished!
timeout /t 3 >nul
