#include <windows.h>
#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>

#pragma comment(lib, "ws2_32.lib")

// Simple test client that sends uncompressed RGB data
class SimpleTestClient {
private:
    SOCKET clientSocket;
    bool connected;

public:
    SimpleTestClient() : clientSocket(INVALID_SOCKET), connected(false) {}

    bool Connect(const std::string& serverIP, int port) {
        WSADATA wsaData;
        if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
            std::cout << "WSAStartup failed" << std::endl;
            return false;
        }

        clientSocket = socket(AF_INET, SOCK_STREAM, 0);
        if (clientSocket == INVALID_SOCKET) {
            std::cout << "Socket creation failed" << std::endl;
            WSACleanup();
            return false;
        }

        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(port);
        inet_pton(AF_INET, serverIP.c_str(), &serverAddr.sin_addr);

        if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            std::cout << "Connection failed: " << WSAGetLastError() << std::endl;
            closesocket(clientSocket);
            WSACleanup();
            return false;
        }

        connected = true;
        std::cout << "Connected to server " << serverIP << ":" << port << std::endl;
        return true;
    }

    void SendHandshake() {
        if (!connected) return;

        // Send magic bytes like the original client
        const BYTE magicBytes[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
        send(clientSocket, (char*)magicBytes, sizeof(magicBytes), 0);

        // Send connection type (desktop = 0)
        DWORD connectionType = 0;
        send(clientSocket, (char*)&connectionType, sizeof(connectionType), 0);

        std::cout << "Handshake sent" << std::endl;
    }

    std::vector<BYTE> CaptureScreen(int& width, int& height) {
        HDC hScreen = GetDC(NULL);
        HDC hDC = CreateCompatibleDC(hScreen);

        width = GetSystemMetrics(SM_CXSCREEN);
        height = GetSystemMetrics(SM_CYSCREEN);

        // Limit size for testing
        if (width > 800) width = 800;
        if (height > 600) height = 600;

        HBITMAP hBitmap = CreateCompatibleBitmap(hScreen, width, height);
        SelectObject(hDC, hBitmap);
        BitBlt(hDC, 0, 0, width, height, hScreen, 0, 0, SRCCOPY);

        BITMAPINFOHEADER bi = {};
        bi.biSize = sizeof(BITMAPINFOHEADER);
        bi.biWidth = width;
        bi.biHeight = -height; // top-down
        bi.biPlanes = 1;
        bi.biBitCount = 24;
        bi.biCompression = BI_RGB;

        int imageSize = width * height * 3; // 24-bit RGB
        std::vector<BYTE> image(imageSize);

        GetDIBits(hDC, hBitmap, 0, height, image.data(), (BITMAPINFO*)&bi, DIB_RGB_COLORS);

        DeleteObject(hBitmap);
        DeleteDC(hDC);
        ReleaseDC(NULL, hScreen);

        std::cout << "Captured screen: " << width << "x" << height << " (" << imageSize << " bytes)" << std::endl;
        return image;
    }

    void SendDesktopData() {
        if (!connected) return;

        // Wait for server to request screen size
        DWORD requestedWidth, requestedHeight;
        recv(clientSocket, (char*)&requestedWidth, sizeof(requestedWidth), 0);
        recv(clientSocket, (char*)&requestedHeight, sizeof(requestedHeight), 0);

        std::cout << "Server requested: " << requestedWidth << "x" << requestedHeight << std::endl;

        // Capture screen
        int actualWidth, actualHeight;
        std::vector<BYTE> imageData = CaptureScreen(actualWidth, actualHeight);

        // Send "has pixels" flag
        DWORD hasPixels = 1;
        send(clientSocket, (char*)&hasPixels, sizeof(hasPixels), 0);

        // Send screen dimensions
        DWORD screenWidth = GetSystemMetrics(SM_CXSCREEN);
        DWORD screenHeight = GetSystemMetrics(SM_CYSCREEN);
        send(clientSocket, (char*)&screenWidth, sizeof(screenWidth), 0);
        send(clientSocket, (char*)&screenHeight, sizeof(screenHeight), 0);

        // Send image dimensions
        send(clientSocket, (char*)&actualWidth, sizeof(actualWidth), 0);
        send(clientSocket, (char*)&actualHeight, sizeof(actualHeight), 0);

        // Send image size
        DWORD imageSize = imageData.size();
        send(clientSocket, (char*)&imageSize, sizeof(imageSize), 0);

        // Send raw RGB data (uncompressed)
        send(clientSocket, (char*)imageData.data(), imageSize, 0);

        std::cout << "Sent desktop data: " << actualWidth << "x" << actualHeight 
                  << " (" << imageSize << " bytes, uncompressed)" << std::endl;

        // Wait for server response
        DWORD response;
        recv(clientSocket, (char*)&response, sizeof(response), 0);
    }

    void RunLoop() {
        if (!connected) return;

        SendHandshake();

        while (connected) {
            SendDesktopData();
            Sleep(1000); // Send every second
        }
    }

    ~SimpleTestClient() {
        if (connected) {
            closesocket(clientSocket);
            WSACleanup();
        }
    }
};

int main() {
    std::cout << "=== Simple HVNC Test Client ===" << std::endl;
    std::cout << "Sends uncompressed RGB data for debugging" << std::endl;

    SimpleTestClient client;
    
    if (client.Connect("127.0.0.1", 8080)) {
        client.RunLoop();
    }

    std::cout << "Client finished" << std::endl;
    return 0;
}
