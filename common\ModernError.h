#pragma once

#include <system_error>
#include <optional>
#include <string>
#include <string_view>
#include <source_location>
#include <Windows.h>

namespace hvnc {

// Custom error categories for HVNC-specific errors
enum class hvnc_error {
    success = 0,
    connection_failed,
    invalid_parameter,
    memory_allocation_failed,
    file_operation_failed,
    network_error,
    desktop_creation_failed,
    process_creation_failed,
    registry_error,
    unknown_error
};

// Error category for HVNC errors
class hvnc_error_category : public std::error_category {
public:
    [[nodiscard]] const char* name() const noexcept override {
        return "hvnc";
    }

    [[nodiscard]] std::string message(int ev) const override {
        switch (static_cast<hvnc_error>(ev)) {
            case hvnc_error::success:
                return "Success";
            case hvnc_error::connection_failed:
                return "Connection failed";
            case hvnc_error::invalid_parameter:
                return "Invalid parameter";
            case hvnc_error::memory_allocation_failed:
                return "Memory allocation failed";
            case hvnc_error::file_operation_failed:
                return "File operation failed";
            case hvnc_error::network_error:
                return "Network error";
            case hvnc_error::desktop_creation_failed:
                return "Desktop creation failed";
            case hvnc_error::process_creation_failed:
                return "Process creation failed";
            case hvnc_error::registry_error:
                return "Registry operation failed";
            case hvnc_error::unknown_error:
            default:
                return "Unknown error";
        }
    }
};

// Get the HVNC error category instance
[[nodiscard]] inline const hvnc_error_category& get_hvnc_category() noexcept {
    static hvnc_error_category instance;
    return instance;
}

// Create error_code from hvnc_error
[[nodiscard]] inline std::error_code make_error_code(hvnc_error e) noexcept {
    return {static_cast<int>(e), get_hvnc_category()};
}

// Windows error category
class windows_error_category : public std::error_category {
public:
    [[nodiscard]] const char* name() const noexcept override {
        return "windows";
    }

    [[nodiscard]] std::string message(int ev) const override {
        LPSTR message_buffer = nullptr;
        DWORD size = FormatMessageA(
            FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM | FORMAT_MESSAGE_IGNORE_INSERTS,
            nullptr,
            static_cast<DWORD>(ev),
            MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT),
            reinterpret_cast<LPSTR>(&message_buffer),
            0,
            nullptr
        );

        if (size == 0 || !message_buffer) {
            return "Unknown Windows error";
        }

        std::string result{message_buffer, size};
        LocalFree(message_buffer);

        // Remove trailing newlines
        while (!result.empty() && (result.back() == '\r' || result.back() == '\n')) {
            result.pop_back();
        }

        return result;
    }
};

// Get the Windows error category instance
[[nodiscard]] inline const windows_error_category& get_windows_category() noexcept {
    static windows_error_category instance;
    return instance;
}

// Create error_code from Windows error
[[nodiscard]] inline std::error_code make_windows_error(DWORD error_code = GetLastError()) noexcept {
    return {static_cast<int>(error_code), get_windows_category()};
}

// Result type for operations that can fail
template<typename T>
class result {
private:
    std::optional<T> value_;
    std::error_code error_;

public:
    // Success constructor
    result(T&& value) noexcept(std::is_nothrow_move_constructible_v<T>)
        : value_(std::move(value)) {}

    result(const T& value) noexcept(std::is_nothrow_copy_constructible_v<T>)
        : value_(value) {}

    // Error constructor
    result(std::error_code error) noexcept : error_(error) {}

    // Check if result contains a value
    [[nodiscard]] bool has_value() const noexcept { return value_.has_value(); }
    [[nodiscard]] explicit operator bool() const noexcept { return has_value(); }

    // Get the value (throws if error)
    [[nodiscard]] T& value() & {
        if (!has_value()) {
            throw std::system_error(error_);
        }
        return *value_;
    }

    [[nodiscard]] const T& value() const & {
        if (!has_value()) {
            throw std::system_error(error_);
        }
        return *value_;
    }

    [[nodiscard]] T&& value() && {
        if (!has_value()) {
            throw std::system_error(error_);
        }
        return std::move(*value_);
    }

    // Get value or default
    template<typename U>
    [[nodiscard]] T value_or(U&& default_value) const & {
        return has_value() ? *value_ : static_cast<T>(std::forward<U>(default_value));
    }

    template<typename U>
    [[nodiscard]] T value_or(U&& default_value) && {
        return has_value() ? std::move(*value_) : static_cast<T>(std::forward<U>(default_value));
    }

    // Get the error
    [[nodiscard]] const std::error_code& error() const noexcept { return error_; }

    // Dereference operators
    [[nodiscard]] T& operator*() & { return value(); }
    [[nodiscard]] const T& operator*() const & { return value(); }
    [[nodiscard]] T&& operator*() && { return std::move(value()); }

    [[nodiscard]] T* operator->() { return &value(); }
    [[nodiscard]] const T* operator->() const { return &value(); }
};

// Specialization for void
template<>
class result<void> {
private:
    std::error_code error_;

public:
    // Success constructor
    result() noexcept = default;

    // Error constructor
    result(std::error_code error) noexcept : error_(error) {}

    // Check if result is success
    [[nodiscard]] bool has_value() const noexcept { return !error_; }
    [[nodiscard]] explicit operator bool() const noexcept { return has_value(); }

    // Get the error
    [[nodiscard]] const std::error_code& error() const noexcept { return error_; }

    // Throw if error
    void value() const {
        if (error_) {
            throw std::system_error(error_);
        }
    }
};

// Helper functions for creating results
template<typename T>
[[nodiscard]] result<std::decay_t<T>> make_result(T&& value) {
    return result<std::decay_t<T>>{std::forward<T>(value)};
}

[[nodiscard]] inline result<void> make_result() {
    return result<void>{};
}

template<typename T>
[[nodiscard]] result<T> make_error_result(std::error_code error) {
    return result<T>{error};
}

[[nodiscard]] inline result<void> make_error_result(std::error_code error) {
    return result<void>{error};
}

// Exception class with source location
class hvnc_exception : public std::system_error {
private:
    std::source_location location_;
    std::string what_message_;

public:
    hvnc_exception(std::error_code error, 
                   std::string_view message = {},
                   std::source_location location = std::source_location::current())
        : std::system_error(error), location_(location) {
        
        if (!message.empty()) {
            what_message_ = std::string{message} + ": " + std::system_error::what();
        } else {
            what_message_ = std::system_error::what();
        }
    }

    [[nodiscard]] const char* what() const noexcept override {
        return what_message_.c_str();
    }

    [[nodiscard]] const std::source_location& where() const noexcept {
        return location_;
    }

    [[nodiscard]] std::string location_string() const {
        return std::string{location_.file_name()} + ":" + 
               std::to_string(location_.line()) + " in " + 
               std::string{location_.function_name()};
    }
};

// Macro for throwing HVNC exceptions with automatic source location
#define THROW_HVNC_ERROR(error_code, message) \
    throw hvnc::hvnc_exception{hvnc::make_error_code(error_code), message}

#define THROW_WINDOWS_ERROR(message) \
    throw hvnc::hvnc_exception{hvnc::make_windows_error(), message}

// Utility functions for error checking
template<typename T>
[[nodiscard]] result<T> check_windows_result(T value, T error_value) noexcept {
    if (value == error_value) {
        return make_error_result<T>(make_windows_error());
    }
    return make_result(std::move(value));
}

[[nodiscard]] inline result<void> check_windows_bool(BOOL value) noexcept {
    if (!value) {
        return make_error_result(make_windows_error());
    }
    return make_result();
}

} // namespace hvnc

// Enable error_code creation from hvnc_error
namespace std {
    template<>
    struct is_error_code_enum<hvnc::hvnc_error> : true_type {};
}
