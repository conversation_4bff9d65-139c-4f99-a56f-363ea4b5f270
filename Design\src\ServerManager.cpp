/**
 * @file ServerManager.cpp
 * @brief Implementation of ServerManager class
 */

#include "ServerManager.h"
#include "ImageProcessor.h"
#include <QThread>
#include <QTimer>
#include <QDateTime>
#include <QHostAddress>
#include <QNetworkInterface>
#include <QPainter>
#include <QFont>

// Windows-specific includes for HVNC functionality
#ifdef _WIN32
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <winternl.h>

    // Define NTSTATUS if not available
    #ifndef NTSTATUS
    typedef LONG NTSTATUS;
    #endif

    // Define COMPRESSION_FORMAT_LZNT1 if not available
    #ifndef COMPRESSION_FORMAT_LZNT1
    #define COMPRESSION_FORMAT_LZNT1 2
    #endif
#endif

// Note: HVNC server integration will be added after basic GUI works

ServerManager::ServerManager(QObject* parent)
    : QObject(parent)
    , m_tcpServer(nullptr)
    , m_imageProcessor(nullptr)
    , m_heartbeatTimer(nullptr)
    , m_statisticsTimer(nullptr)
    , m_serverState(ServerState::Stopped)
    , m_serverPort(0)
    , m_nextClientId(1)
    , m_totalBytesReceived(0)
    , m_totalBytesSent(0)
    , m_totalConnections(0)
    , m_maxClients(HVNCDesign::MAX_CLIENTS)
    , m_heartbeatInterval(HVNCDesign::HEARTBEAT_INTERVAL_MS)
    , m_statisticsInterval(5000) // 5 seconds
    , m_isInitialized(false)
    , m_isRunning(false) {
    
    initialize();
}

ServerManager::~ServerManager() {
    stopServer();
    
    delete m_imageProcessor;
    delete m_heartbeatTimer;
    delete m_statisticsTimer;
    
    HVNC_DEBUG() << "ServerManager destroyed";
}

void ServerManager::initialize() {
    if (m_isInitialized) return;
    
    HVNC_DEBUG() << "Initializing ServerManager";
    
    // Create image processor
    m_imageProcessor = new ImageProcessor(this);
    
    // Create timers
    m_heartbeatTimer = new QTimer(this);
    m_heartbeatTimer->setInterval(m_heartbeatInterval);
    connect(m_heartbeatTimer, &QTimer::timeout, this, &ServerManager::onHeartbeatTimer);
    
    m_statisticsTimer = new QTimer(this);
    m_statisticsTimer->setInterval(m_statisticsInterval);
    connect(m_statisticsTimer, &QTimer::timeout, this, &ServerManager::onStatisticsTimer);

    m_isInitialized = true;
    HVNC_INFO() << "ServerManager initialized successfully";
}

bool ServerManager::startServer(PortNumber port) {
    if (m_serverState == ServerState::Running || m_serverState == ServerState::Starting) {
        HVNC_WARNING() << "Server is already running or starting";
        return false;
    }
    
    HVNC_INFO() << "Starting HVNC server on port" << port;
    setServerState(ServerState::Starting);
    
    // Setup Qt TCP server for monitoring (parallel to existing server)
    if (!setupServer(port)) {
        setServerState(ServerState::Error);
        emit errorOccurred("Server Error", "Failed to setup server socket");
        return false;
    }
    
    // Note: Using QTcpServer for actual network handling
    // Placeholder server thread removed to avoid confusion
    
    m_serverPort = port;
    m_serverStartTime = QDateTime::currentDateTime();
    m_isRunning = true;
    setServerState(ServerState::Running);
    
    // Start timers
    m_heartbeatTimer->start();
    m_statisticsTimer->start();
    
    emit statusMessage(QString("Server started on port %1").arg(port));
    HVNC_INFO() << "HVNC server started successfully on port" << port;
    
    return true;
}

void ServerManager::stopServer() {
    if (m_serverState == ServerState::Stopped || m_serverState == ServerState::Stopping) {
        return;
    }
    
    HVNC_INFO() << "Stopping HVNC server";
    setServerState(ServerState::Stopping);
    
    // Stop timers
    m_heartbeatTimer->stop();
    m_statisticsTimer->stop();
    
    // Disconnect all clients
    QList<ClientId> clientIds = m_clients.keys();
    for (ClientId clientId : clientIds) {
        disconnectClient(clientId);
    }
    
    // Cleanup server
    cleanupServer();

    m_isRunning = false;
    setServerState(ServerState::Stopped);
    emit statusMessage("Server stopped");
    HVNC_INFO() << "HVNC server stopped";
}

const ServerManager::ClientInfo* ServerManager::getClientInfo(ClientId clientId) const {
    auto it = m_clients.find(clientId);
    return (it != m_clients.end()) ? &it.value() : nullptr;
}

QList<ServerManager::ClientInfo> ServerManager::getAllClients() const {
    return m_clients.values();
}

bool ServerManager::sendInputEvent(ClientId clientId, const QByteArray& event) {
    QTcpSocket* socket = getClientSocket(clientId);
    if (!socket || socket->state() != QAbstractSocket::ConnectedState) {
        return false;
    }
    
    qint64 bytesWritten = socket->write(event);
    if (bytesWritten > 0) {
        updateClientStatistics(clientId, 0, bytesWritten);
        return true;
    }
    
    return false;
}

bool ServerManager::sendMouseEvent(ClientId clientId, int x, int y, MouseButton button, bool pressed) {
    auto it = m_clientSockets.find(clientId);
    if (it == m_clientSockets.end()) {
        HVNC_WARNING() << "Client not found for mouse event:" << clientId;
        return false;
    }

    QTcpSocket* socket = it.value();
    if (!socket || socket->state() != QAbstractSocket::ConnectedState) {
        HVNC_WARNING() << "Client socket not connected for mouse event";
        return false;
    }

    // Working Server/ protocol: Send mouse event
    // Format: DWORD eventType, DWORD x, DWORD y, DWORD button, DWORD pressed
    const DWORD eventType = 0x1001; // Mouse event
    int buttonValue = static_cast<int>(button);
    DWORD data[5] = { eventType, (DWORD)x, (DWORD)y, (DWORD)buttonValue, pressed ? 1u : 0u };

    qint64 bytesWritten = socket->write((char*)data, sizeof(data));
    if (bytesWritten == sizeof(data)) {
        HVNC_DEBUG() << "Sent mouse event to client" << clientId << "pos:" << x << "," << y
                     << "button:" << buttonValue << "pressed:" << pressed;
        updateClientStatistics(clientId, 0, sizeof(data));
        return true;
    } else {
        HVNC_WARNING() << "Failed to send mouse event to client" << clientId;
        return false;
    }
}

bool ServerManager::sendKeyboardEvent(ClientId clientId, int key, bool pressed, KeyModifier modifiers) {
    auto it = m_clientSockets.find(clientId);
    if (it == m_clientSockets.end()) {
        HVNC_WARNING() << "Client not found for keyboard event:" << clientId;
        return false;
    }

    QTcpSocket* socket = it.value();
    if (!socket || socket->state() != QAbstractSocket::ConnectedState) {
        HVNC_WARNING() << "Client socket not connected for keyboard event";
        return false;
    }

    // Working Server/ protocol: Send keyboard event
    // Format: DWORD eventType, DWORD keyCode, DWORD pressed, DWORD modifiers
    const DWORD eventType = 0x1002; // Keyboard event
    DWORD data[4] = { eventType, (DWORD)key, pressed ? 1u : 0u, (DWORD)modifiers };

    qint64 bytesWritten = socket->write((char*)data, sizeof(data));
    if (bytesWritten == sizeof(data)) {
        HVNC_DEBUG() << "Sent keyboard event to client" << clientId << "key:" << key
                     << "pressed:" << pressed << "modifiers:" << (int)modifiers;
        updateClientStatistics(clientId, 0, sizeof(data));
        return true;
    } else {
        HVNC_WARNING() << "Failed to send keyboard event to client" << clientId;
        return false;
    }
}

bool ServerManager::requestDesktopUpdate(ClientId clientId, bool fullUpdate) {
    // Create desktop update request packet
    QByteArray requestData;
    QDataStream stream(&requestData, QIODevice::WriteOnly);
    
    stream << static_cast<quint32>(0x1001); // Custom message for desktop update
    stream << static_cast<quint32>(fullUpdate ? 1 : 0); // Full update flag
    
    return sendInputEvent(clientId, requestData);
}

void ServerManager::disconnectClient(ClientId clientId) {
    auto clientIt = m_clients.find(clientId);
    if (clientIt == m_clients.end()) {
        return;
    }
    
    HVNC_INFO() << "Disconnecting client" << clientId;
    
    QTcpSocket* socket = getClientSocket(clientId);
    if (socket) {
        socket->disconnectFromHost();
        if (socket->state() != QAbstractSocket::UnconnectedState) {
            socket->waitForDisconnected(3000);
        }
    }
    
    removeClient(clientId);
}

QMap<QString, QVariant> ServerManager::getServerStatistics() const {
    QMap<QString, QVariant> stats;
    
    stats["serverState"] = static_cast<int>(m_serverState);
    stats["serverPort"] = m_serverPort;
    stats["connectedClients"] = m_clients.size();
    stats["totalConnections"] = m_totalConnections;
    stats["totalBytesReceived"] = m_totalBytesReceived;
    stats["totalBytesSent"] = m_totalBytesSent;
    stats["maxClients"] = m_maxClients;
    
    if (m_serverState == ServerState::Running) {
        qint64 uptime = m_serverStartTime.msecsTo(QDateTime::currentDateTime());
        stats["uptimeMs"] = uptime;
        stats["uptimeSeconds"] = uptime / 1000;
    }
    
    return stats;
}



void ServerManager::onNewConnection() {
    if (!m_tcpServer) return;

    while (m_tcpServer->hasPendingConnections()) {
        QTcpSocket* socket = m_tcpServer->nextPendingConnection();

        QString clientAddress = QString("%1:%2")
            .arg(socket->peerAddress().toString())
            .arg(socket->peerPort());

        HVNC_INFO() << "New connection from" << clientAddress;

        if (m_clients.size() >= m_maxClients) {
            HVNC_WARNING() << "Maximum clients reached, rejecting connection from" << clientAddress;
            socket->disconnectFromHost();
            socket->deleteLater();
            continue;
        }

        // Process handshake
        ClientId clientId = processClientHandshake(socket);
        if (clientId != 0) {
            addClient(socket);
            HVNC_INFO() << "Client" << clientAddress << "connected successfully with ID" << clientId;
        } else {
            HVNC_WARNING() << "Handshake failed for client" << clientAddress;
            socket->disconnectFromHost();
            socket->deleteLater();
        }
    }
}

void ServerManager::onClientDisconnected() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) return;
    
    auto it = m_socketToClient.find(socket);
    if (it != m_socketToClient.end()) {
        ClientId clientId = it.value();
        removeClient(clientId);
        socket->deleteLater();
    }
}

void ServerManager::onClientDataReady() {
    QTcpSocket* socket = qobject_cast<QTcpSocket*>(sender());
    if (!socket) return;

    auto it = m_socketToClient.find(socket);
    if (it == m_socketToClient.end()) return;

    ClientId clientId = it.value();

    // Working Server/ protocol: process data as it arrives
    processWorkingServerData(socket, clientId);
}

void ServerManager::onServerError(QAbstractSocket::SocketError error) {
    Q_UNUSED(error)
    
    if (m_tcpServer) {
        QString errorString = m_tcpServer->errorString();
        HVNC_CRITICAL() << "Server error:" << errorString;
        emit errorOccurred("Server Error", errorString);
        setServerState(ServerState::Error);
    }
}

void ServerManager::onHeartbeatTimer() {
    // Send heartbeat to all connected clients
    for (auto it = m_clients.begin(); it != m_clients.end(); ++it) {
        ClientId clientId = it.key();
        QTcpSocket* socket = getClientSocket(clientId);
        
        if (socket && socket->state() == QAbstractSocket::ConnectedState) {
            // Send simple heartbeat packet
            QByteArray heartbeat;
            QDataStream stream(&heartbeat, QIODevice::WriteOnly);
            stream << static_cast<quint32>(0x1000); // Heartbeat message
            stream << static_cast<quint32>(QDateTime::currentMSecsSinceEpoch());
            
            socket->write(heartbeat);
        }
    }
}

void ServerManager::onStatisticsTimer() {
    // Update statistics and emit signals
    for (auto it = m_clients.begin(); it != m_clients.end(); ++it) {
        ClientId clientId = it.key();
        const ClientInfo& info = it.value();

        emit dataTransferUpdate(clientId, info.bytesReceived, info.bytesSent);
    }
}

// Private helper methods
bool ServerManager::setupServer(PortNumber port) {
    m_tcpServer = new QTcpServer(this);

    connect(m_tcpServer, &QTcpServer::newConnection, this, &ServerManager::onNewConnection);

    if (!m_tcpServer->listen(QHostAddress::Any, port)) {
        HVNC_CRITICAL() << "Failed to start server on port" << port << ":" << m_tcpServer->errorString();
        delete m_tcpServer;
        m_tcpServer = nullptr;
        return false;
    }

    HVNC_INFO() << "Qt TCP server listening on port" << m_tcpServer->serverPort();
    return true;
}

void ServerManager::cleanupServer() {
    if (m_tcpServer) {
        m_tcpServer->close();
        delete m_tcpServer;
        m_tcpServer = nullptr;
    }

    // Clear all client data
    for (auto socket : m_clientSockets.values()) {
        if (socket) {
            socket->disconnectFromHost();
            socket->deleteLater();
        }
    }

    m_clients.clear();
    m_clientSockets.clear();
    m_socketToClient.clear();
}

ClientId ServerManager::processClientHandshake(QTcpSocket* socket) {
    // Working Server/ protocol: read magic bytes first
    static const BYTE gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
    enum Connection { desktop = 0, input = 1 };

    // Wait for magic bytes
    while (socket->bytesAvailable() < sizeof(gc_magik)) {
        if (!socket->waitForReadyRead(5000)) {
            HVNC_WARNING() << "Client handshake timeout waiting for magic bytes";
            return 0;
        }
    }

    // Read and validate magic bytes
    QByteArray magicData = socket->read(sizeof(gc_magik));

    HVNC_DEBUG() << "Received magic data size:" << magicData.size() << "expected:" << sizeof(gc_magik);
    if (magicData.size() > 0) {
        QString hexData;
        for (int i = 0; i < magicData.size(); ++i) {
            hexData += QString("%1 ").arg((unsigned char)magicData[i], 2, 16, QChar('0'));
        }
        HVNC_DEBUG() << "Received magic bytes:" << hexData;

        QString expectedHex;
        for (int i = 0; i < sizeof(gc_magik); ++i) {
            expectedHex += QString("%1 ").arg((unsigned char)gc_magik[i], 2, 16, QChar('0'));
        }
        HVNC_DEBUG() << "Expected magic bytes:" << expectedHex;
    }

    if (magicData.size() != sizeof(gc_magik) ||
        memcmp(magicData.data(), gc_magik, sizeof(gc_magik)) != 0) {
        HVNC_WARNING() << "Invalid handshake magic bytes";
        return 0;
    }

    HVNC_INFO() << "Magic bytes validated successfully";

    // Wait for connection type
    while (socket->bytesAvailable() < sizeof(Connection)) {
        if (!socket->waitForReadyRead(5000)) {
            HVNC_WARNING() << "Client handshake timeout waiting for connection type";
            return 0;
        }
    }

    // Read connection type
    Connection connectionType;
    QByteArray connectionData = socket->read(sizeof(connectionType));
    if (connectionData.size() != sizeof(connectionType)) {
        HVNC_WARNING() << "Failed to read connection type";
        return 0;
    }
    memcpy(&connectionType, connectionData.data(), sizeof(connectionType));

    HVNC_INFO() << "Working Server/ protocol handshake successful, connection type:" << connectionType;

    if (connectionType == desktop) {
        // Send initial screen size request (working Server/ protocol)
        quint32 screenWidth = 1920;  // Default size
        quint32 screenHeight = 1080;
        socket->write((char*)&screenWidth, sizeof(screenWidth));
        socket->write((char*)&screenHeight, sizeof(screenHeight));
        socket->waitForBytesWritten(3000);

        HVNC_INFO() << "Sent screen size request:" << screenWidth << "x" << screenHeight;
    }

    return generateClientId();
}

QImage ServerManager::decompressDesktopImage(const QByteArray& imageData, int width, int height) {
    HVNC_INFO() << "=== PROCESSING DATA ===";
    HVNC_INFO() << "Data size:" << imageData.size() << "bytes";
    HVNC_INFO() << "Dimensions:" << width << "x" << height;

    const int expectedFullSize = width * height * 3; // 24-bit RGB
    HVNC_INFO() << "Expected full size:" << expectedFullSize << "bytes";

    // Since we're getting 7776 bytes for 1536x864, this is likely compressed data
    // Let's try the LZNT1 decompression first
    if (imageData.size() < expectedFullSize) {
        HVNC_INFO() << "Data appears compressed, trying LZNT1 decompression...";

        QImage decompressed = tryLZNT1Decompression(imageData, width, height);
        if (!decompressed.isNull()) {
            HVNC_INFO() << "LZNT1 decompression successful!";
            return decompressed;
        }

        HVNC_INFO() << "LZNT1 failed, trying as raw data...";
    }

    // If decompression failed or data size is close to expected, try as raw data
    if (imageData.size() >= expectedFullSize * 0.8) {
        HVNC_INFO() << "Trying as raw RGB data...";

        // Try direct RGB processing
        QImage rawImage((const uchar*)imageData.data(), width, height, width * 3, QImage::Format_RGB888);
        if (!rawImage.isNull()) {
            HVNC_INFO() << "Raw RGB processing successful!";
            // Convert BGR to RGB and make a copy
            return rawImage.rgbSwapped().copy();
        }
    }

    // Fallback: Show detailed data analysis
    HVNC_WARNING() << "All decompression methods failed, creating detailed analysis...";
    QImage analysisImage(width, height, QImage::Format_RGB888);
    analysisImage.fill(QColor(40, 40, 40));

    QPainter painter(&analysisImage);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 14, QFont::Bold));

    QString analysisText = QString(
        "HVNC DATA ANALYSIS\n\n"
        "Received: %1 bytes\n"
        "Expected 24-bit: %2 bytes\n"
        "Compression ratio: %3%\n\n"
        "First 32 bytes (hex):\n%4\n\n"
        "Data pattern analysis:\n"
        "- Size suggests LZNT1 compression\n"
        "- LZNT1 decompression failed\n"
        "- Raw RGB processing failed\n\n"
        "Check console for detailed logs."
    ).arg(imageData.size())
     .arg(expectedFullSize)
     .arg((imageData.size() * 100) / expectedFullSize)
     .arg(QString(imageData.left(32).toHex(' ')));

    painter.drawText(QRect(20, 20, width-40, height-40), Qt::AlignLeft | Qt::TextWordWrap, analysisText);

    return analysisImage;
}

QImage ServerManager::tryLZNT1Decompression(const QByteArray& compressedData, int width, int height) {
#ifdef _WIN32
    // Fast LZNT1 decompression using Windows API
    typedef NTSTATUS (NTAPI *T_RtlDecompressBuffer)(
        USHORT CompressionFormat,
        PUCHAR UncompressedBuffer,
        ULONG  UncompressedBufferSize,
        PUCHAR CompressedBuffer,
        ULONG  CompressedBufferSize,
        PULONG FinalUncompressedSize
    );

    static T_RtlDecompressBuffer pRtlDecompressBuffer = nullptr;
    static bool initialized = false;

    if (!initialized) {
        HMODULE hNtdll = GetModuleHandleA("ntdll.dll");
        if (hNtdll) {
            pRtlDecompressBuffer = (T_RtlDecompressBuffer)GetProcAddress(hNtdll, "RtlDecompressBuffer");
        }
        initialized = true;
    }

    if (!pRtlDecompressBuffer) {
        return QImage();
    }

    // Try 24-bit RGB decompression (most common)
    DWORD uncompressedSize = width * height * 3;
    QByteArray uncompressedData(uncompressedSize, 0);

    ULONG finalSize = 0;
    NTSTATUS status = pRtlDecompressBuffer(
        COMPRESSION_FORMAT_LZNT1,
        (PUCHAR)uncompressedData.data(),
        uncompressedSize,
        (PUCHAR)compressedData.data(),
        compressedData.size(),
        &finalSize
    );

    HVNC_INFO() << "LZNT1 decompression attempt:";
    HVNC_INFO() << "  - Status:" << status;
    HVNC_INFO() << "  - Expected size:" << uncompressedSize;
    HVNC_INFO() << "  - Final size:" << finalSize;
    HVNC_INFO() << "  - Compressed size:" << compressedData.size();

    if (status == 0 && finalSize > 0) {
        HVNC_INFO() << "LZNT1 decompression successful!";

        // Try different approaches to handle the decompressed data

        // Method 1: Direct QImage with proper stride
        int bytesPerLine = width * 3;
        QImage image1((const uchar*)uncompressedData.data(), width, height, bytesPerLine, QImage::Format_RGB888);

        if (!image1.isNull()) {
            HVNC_INFO() << "Method 1: Direct RGB888 successful";

            // Debug: Check first few pixels to see what we're getting
            const uchar* data = (const uchar*)uncompressedData.data();
            HVNC_INFO() << "First pixel RGB:" << (int)data[0] << (int)data[1] << (int)data[2];
            HVNC_INFO() << "Pixel at (100,100):" << (int)data[(100*width+100)*3] << (int)data[(100*width+100)*3+1] << (int)data[(100*width+100)*3+2];

            QImage result = image1.rgbSwapped().copy(); // BGR to RGB conversion

            // Windows bitmaps with positive height are stored bottom-up, so flip vertically
            result = result.mirrored(false, true);

            HVNC_INFO() << "Applied BGR->RGB conversion and vertical flip";
            return result;
        }

        // Method 2: Manual pixel-by-pixel conversion to ensure correct format
        HVNC_INFO() << "Method 1 failed, trying manual conversion...";
        QImage image2(width, height, QImage::Format_RGB888);

        const uchar* srcData = (const uchar*)uncompressedData.data();
        for (int y = 0; y < height; ++y) {
            for (int x = 0; x < width; ++x) {
                int pixelIndex = (y * width + x) * 3;
                if (pixelIndex + 2 < finalSize) {
                    // Windows bitmap data is BGR, so swap to RGB
                    uchar b = srcData[pixelIndex];
                    uchar g = srcData[pixelIndex + 1];
                    uchar r = srcData[pixelIndex + 2];
                    image2.setPixel(x, y, qRgb(r, g, b));
                }
            }
        }

        if (!image2.isNull()) {
            HVNC_INFO() << "Method 2: Manual conversion successful";
            return image2;
        }

        HVNC_WARNING() << "Both image creation methods failed";
    }

    HVNC_WARNING() << "LZNT1 decompression failed, status:" << status;
#else
    Q_UNUSED(compressedData)
    Q_UNUSED(width)
    Q_UNUSED(height)
#endif
    return QImage();
}

QImage ServerManager::optimizeImageForDisplay(const QImage& image) {
    if (image.isNull()) return image;

    QImage optimized = image;

    // 1. Enhance contrast and brightness for better visibility
    optimized = enhanceImageQuality(optimized);

    // 2. Apply scaling if needed for performance
    optimized = scaleImageForPerformance(optimized);

    return optimized;
}

QImage ServerManager::enhanceImageQuality(const QImage& image) {
    if (image.isNull()) return image;

    QImage enhanced = image;

    // Apply gamma correction for better visibility
    const double gamma = 1.2; // Slightly brighten
    const double invGamma = 1.0 / gamma;

    for (int y = 0; y < enhanced.height(); ++y) {
        QRgb* line = (QRgb*)enhanced.scanLine(y);
        for (int x = 0; x < enhanced.width(); ++x) {
            QRgb pixel = line[x];

            // Apply gamma correction to each channel
            int r = qBound(0, (int)(255 * pow(qRed(pixel) / 255.0, invGamma)), 255);
            int g = qBound(0, (int)(255 * pow(qGreen(pixel) / 255.0, invGamma)), 255);
            int b = qBound(0, (int)(255 * pow(qBlue(pixel) / 255.0, invGamma)), 255);

            line[x] = qRgb(r, g, b);
        }
    }

    return enhanced;
}

QImage ServerManager::scaleImageForPerformance(const QImage& image) {
    if (image.isNull()) return image;

    // Scale down large images for better performance
    const int maxWidth = 1920;
    const int maxHeight = 1080;

    if (image.width() > maxWidth || image.height() > maxHeight) {
        return image.scaled(maxWidth, maxHeight, Qt::KeepAspectRatio, Qt::SmoothTransformation);
    }

    return image;
}



void ServerManager::processWorkingServerData(QTcpSocket* socket, ClientId clientId) {
    // Working Server/ protocol: handle data as it arrives in the correct sequence
    // The working server sends screen size first, then waits for desktop data

    while (socket->bytesAvailable() > 0) {
        // Check if we have enough data for recvPixels flag
        if (socket->bytesAvailable() < sizeof(BOOL)) {
            HVNC_DEBUG() << "Waiting for more data (recvPixels flag)";
            return;
        }

        // Read recvPixels flag
        BOOL recvPixels;
        if (socket->read((char*)&recvPixels, sizeof(recvPixels)) != sizeof(recvPixels)) {
            HVNC_WARNING() << "Failed to read recvPixels flag";
            return;
        }

        if (!recvPixels) {
            HVNC_DEBUG() << "Client has no new pixels to send";
            return;
        }

        // Wait for screen and image dimensions (5 DWORDs)
        while (socket->bytesAvailable() < 5 * sizeof(DWORD)) {
            if (!socket->waitForReadyRead(5000)) {
                HVNC_WARNING() << "Timeout waiting for image dimensions";
                return;
            }
        }

        DWORD screenWidth, screenHeight, width, height, size;
        socket->read((char*)&screenWidth, sizeof(screenWidth));
        socket->read((char*)&screenHeight, sizeof(screenHeight));
        socket->read((char*)&width, sizeof(width));
        socket->read((char*)&height, sizeof(height));
        socket->read((char*)&size, sizeof(size));

        if (size == 0 || size > 50 * 1024 * 1024) { // Sanity check: max 50MB
            HVNC_WARNING() << "Invalid compressed data size:" << size;
            return;
        }

        // Wait for all compressed data
        while (socket->bytesAvailable() < size) {
            if (!socket->waitForReadyRead(10000)) { // Longer timeout for large images
                HVNC_WARNING() << "Timeout waiting for compressed data, expected:" << size << "available:" << socket->bytesAvailable();
                return;
            }
        }

        // Read compressed data
        QByteArray compressedData = socket->read(size);
        if (compressedData.size() != size) {
            HVNC_WARNING() << "Failed to read complete compressed data";
            return;
        }

        HVNC_INFO() << "=== DESKTOP DATA RECEIVED ===";
        HVNC_INFO() << "Client:" << clientId;
        HVNC_INFO() << "Screen size:" << screenWidth << "x" << screenHeight;
        HVNC_INFO() << "Image size:" << width << "x" << height;
        HVNC_INFO() << "Data size:" << size << "bytes";
        HVNC_INFO() << "Raw data first 16 bytes:" << compressedData.left(16).toHex();

        // Simple approach: Just process the image directly
        QImage processedImage = decompressDesktopImage(compressedData, width, height);

        if (!processedImage.isNull()) {
            HVNC_INFO() << "Successfully processed image:" << width << "x" << height;
            emit desktopImageReceived(clientId, processedImage);
        } else {
            HVNC_WARNING() << "Failed to process image, creating debug display";

            // Create a debug image with information
            QImage debugImage(800, 600, QImage::Format_RGB888);
            debugImage.fill(QColor(40, 40, 40));

            QPainter painter(&debugImage);
            painter.setPen(Qt::white);
            painter.setFont(QFont("Arial", 14));

            QString debugText = QString(
                "HVNC DEBUG INFO\n\n"
                "Client ID: %1\n"
                "Screen: %2 x %3\n"
                "Image: %4 x %5\n"
                "Data Size: %6 bytes\n"
                "Expected 24-bit: %7 bytes\n\n"
                "First 32 bytes (hex):\n%8"
            ).arg(clientId)
             .arg(screenWidth).arg(screenHeight)
             .arg(width).arg(height)
             .arg(size)
             .arg(width * height * 3)
             .arg(QString(compressedData.left(32).toHex()));

            painter.drawText(QRect(20, 20, 760, 560), Qt::AlignLeft | Qt::TextWordWrap, debugText);

            emit desktopImageReceived(clientId, debugImage);
        }

        updateClientStatistics(clientId, sizeof(recvPixels) + 5 * sizeof(DWORD) + size, 0);

        // Continue processing if there's more data
    }
}

void ServerManager::processClientData(ClientId clientId, const QByteArray& data) {
    if (data.isEmpty()) return;

    // Working Server/ protocol: raw binary data
    // Protocol: BOOL recvPixels, DWORD screenWidth, DWORD screenHeight, DWORD width, DWORD height, DWORD size, BYTE[] compressedData

    const char* dataPtr = data.constData();
    int dataSize = data.size();
    int offset = 0;

    if (dataSize < sizeof(BOOL)) {
        HVNC_DEBUG() << "Insufficient data for recvPixels flag";
        return;
    }

    // Read recvPixels flag
    BOOL recvPixels;
    memcpy(&recvPixels, dataPtr + offset, sizeof(recvPixels));
    offset += sizeof(recvPixels);

    if (!recvPixels) {
        HVNC_DEBUG() << "Client has no new pixels to send";
        return;
    }

    // Read screen and image dimensions
    if (dataSize < offset + 5 * sizeof(DWORD)) {
        HVNC_DEBUG() << "Insufficient data for image dimensions";
        return;
    }

    DWORD screenWidth, screenHeight, width, height, size;
    memcpy(&screenWidth, dataPtr + offset, sizeof(screenWidth)); offset += sizeof(screenWidth);
    memcpy(&screenHeight, dataPtr + offset, sizeof(screenHeight)); offset += sizeof(screenHeight);
    memcpy(&width, dataPtr + offset, sizeof(width)); offset += sizeof(width);
    memcpy(&height, dataPtr + offset, sizeof(height)); offset += sizeof(height);
    memcpy(&size, dataPtr + offset, sizeof(size)); offset += sizeof(size);

    if (size == 0 || size > 50 * 1024 * 1024) { // Sanity check: max 50MB
        HVNC_WARNING() << "Invalid compressed data size:" << size;
        return;
    }

    if (dataSize < offset + size) {
        HVNC_DEBUG() << "Insufficient data for compressed image, expected:" << size << "available:" << (dataSize - offset);
        return;
    }

    // Extract compressed data
    QByteArray compressedData(dataPtr + offset, size);

    HVNC_INFO() << "Received desktop data from client" << clientId
                << "Screen:" << screenWidth << "x" << screenHeight
                << "Image:" << width << "x" << height
                << "Compressed size:" << size;

    // For now, create a placeholder image (we'll add decompression later)
    QImage image(width, height, QImage::Format_RGB888);
    image.fill(QColor(64, 128, 255)); // Blue placeholder

    // Add some text to show it's working
    QPainter painter(&image);
    painter.setPen(Qt::white);
    painter.setFont(QFont("Arial", 24));
    painter.drawText(image.rect(), Qt::AlignCenter,
                    QString("HVNC Desktop\nClient %1\n%2x%3")
                    .arg(clientId).arg(width).arg(height));

    emit desktopImageReceived(clientId, image);
    updateClientStatistics(clientId, data.size(), 0);
}

void ServerManager::processDesktopImage(ClientId clientId, const QByteArray& data) {
    if (data.size() < 16) return; // Minimum header size

    QDataStream stream(data);
    quint32 messageType, width, height, imageSize;

    stream >> messageType >> width >> height >> imageSize;

    if (stream.atEnd() || imageSize == 0) return;

    QByteArray imageData(imageSize, 0);
    stream.readRawData(imageData.data(), imageSize);

    // Process image data using ImageProcessor
    if (m_imageProcessor) {
        QImage image = m_imageProcessor->processImageData(imageData, width, height);
        if (!image.isNull()) {
            emit desktopImageReceived(clientId, image);

            // Update client info
            auto it = m_clients.find(clientId);
            if (it != m_clients.end()) {
                it.value().desktopSize = QSize(width, height);
            }
        }
    }
}

void ServerManager::processDesktopRegion(ClientId clientId, const QByteArray& data) {
    if (data.size() < 20) return; // Minimum header size

    QDataStream stream(data);
    quint32 messageType, x, y, width, height, imageSize;

    stream >> messageType >> x >> y >> width >> height >> imageSize;

    if (stream.atEnd() || imageSize == 0) return;

    QByteArray imageData(imageSize, 0);
    stream.readRawData(imageData.data(), imageSize);

    // Process region image data
    if (m_imageProcessor) {
        QImage image = m_imageProcessor->processImageData(imageData, width, height);
        if (!image.isNull()) {
            emit desktopRegionUpdated(clientId, image, x, y);
        }
    }
}

ClientId ServerManager::addClient(QTcpSocket* socket) {
    ClientId clientId = generateClientId();

    // Create client info
    ClientInfo info;
    info.id = clientId;
    info.ipAddress = socket->peerAddress().toString();
    info.port = socket->peerPort();
    info.connectTime = QDateTime::currentDateTime();
    info.userAgent = "HVNC Client"; // Could be extracted from handshake
    info.desktopSize = QSize(0, 0);
    info.isActive = true;
    info.bytesReceived = 0;
    info.bytesSent = 0;

    // Store client data
    m_clients[clientId] = info;
    m_clientSockets[clientId] = socket;
    m_socketToClient[socket] = clientId;

    // Setup socket connections
    connect(socket, &QTcpSocket::disconnected, this, &ServerManager::onClientDisconnected);
    connect(socket, &QTcpSocket::readyRead, this, &ServerManager::onClientDataReady);
    connect(socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &ServerManager::onServerError);

    m_totalConnections++;

    QString clientInfo = QString("%1:%2").arg(info.ipAddress).arg(info.port);
    emit clientConnected(clientId, clientInfo);

    return clientId;
}

void ServerManager::removeClient(ClientId clientId) {
    auto clientIt = m_clients.find(clientId);
    if (clientIt == m_clients.end()) {
        return;
    }

    // Remove from socket mappings
    auto socketIt = m_clientSockets.find(clientId);
    if (socketIt != m_clientSockets.end()) {
        QTcpSocket* socket = socketIt.value();
        m_socketToClient.remove(socket);
        m_clientSockets.erase(socketIt);
    }

    // Remove client info
    m_clients.erase(clientIt);

    // Clean up desktop images
    m_clientDesktops.remove(clientId);
    m_clientScreenSizes.remove(clientId);

    emit clientDisconnected(clientId);

    HVNC_INFO() << "Client removed and desktop cleaned up:" << clientId;
}

QTcpSocket* ServerManager::getClientSocket(ClientId clientId) const {
    auto it = m_clientSockets.find(clientId);
    return (it != m_clientSockets.end()) ? it.value() : nullptr;
}

void ServerManager::updateClientStatistics(ClientId clientId, qint64 bytesReceived, qint64 bytesSent) {
    auto it = m_clients.find(clientId);
    if (it != m_clients.end()) {
        it.value().bytesReceived += bytesReceived;
        it.value().bytesSent += bytesSent;

        m_totalBytesReceived += bytesReceived;
        m_totalBytesSent += bytesSent;
    }
}

void ServerManager::setServerState(ServerState state) {
    if (m_serverState != state) {
        m_serverState = state;
        emit serverStateChanged(state);
    }
}

ClientId ServerManager::generateClientId() {
    return m_nextClientId++;
}
