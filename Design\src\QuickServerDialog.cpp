/**
 * @file QuickServerDialog.cpp
 * @brief Implementation of Quick Server Configuration Dialog
 */

#include "QuickServerDialog.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QCheckBox>
#include <QMessageBox>
#include <QSettings>

QuickServerDialog::QuickServerDialog(QWidget* parent)
    : QDialog(parent)
    , m_portSpinBox(nullptr)
    , m_passwordLineEdit(nullptr)
    , m_maxConnectionsSpinBox(nullptr)
    , m_autoStartCheckBox(nullptr)
    , m_testButton(nullptr)
    , m_okButton(nullptr)
    , m_cancelButton(nullptr)
{
    setWindowTitle("Quick Server Configuration");
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setModal(true);
    setFixedSize(400, 300);
    
    initializeUI();
    applyDarkTheme();
    
    // Load current settings
    QSettings settings;
    setServerPort(settings.value("server/port", 4444).toInt());
    setServerPassword(settings.value("server/password", "admin").toString());
    setMaxConnections(settings.value("server/maxConnections", 10).toInt());
    setAutoStartEnabled(settings.value("server/autoStart", false).toBool());
}

QuickServerDialog::~QuickServerDialog() = default;

void QuickServerDialog::initializeUI()
{
    auto* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(20, 20, 20, 20);
    mainLayout->setSpacing(15);

    // Title
    auto* titleLabel = new QLabel("Quick Server Setup");
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);

    // Server configuration group
    auto* configGroup = new QGroupBox("Essential Server Settings");
    auto* configLayout = new QGridLayout(configGroup);
    configLayout->setSpacing(12);

    // Port
    auto* portLabel = new QLabel("Server Port:");
    m_portSpinBox = new QSpinBox();
    m_portSpinBox->setRange(1024, 65535);
    m_portSpinBox->setValue(4444);
    m_portSpinBox->setSuffix(" (TCP)");
    configLayout->addWidget(portLabel, 0, 0);
    configLayout->addWidget(m_portSpinBox, 0, 1);

    // Password
    auto* passwordLabel = new QLabel("Password:");
    m_passwordLineEdit = new QLineEdit();
    m_passwordLineEdit->setEchoMode(QLineEdit::Password);
    m_passwordLineEdit->setText("admin");
    m_passwordLineEdit->setPlaceholderText("Enter server password");
    configLayout->addWidget(passwordLabel, 1, 0);
    configLayout->addWidget(m_passwordLineEdit, 1, 1);

    // Max connections
    auto* maxConnLabel = new QLabel("Max Clients:");
    m_maxConnectionsSpinBox = new QSpinBox();
    m_maxConnectionsSpinBox->setRange(1, 100);
    m_maxConnectionsSpinBox->setValue(10);
    m_maxConnectionsSpinBox->setSuffix(" clients");
    configLayout->addWidget(maxConnLabel, 2, 0);
    configLayout->addWidget(m_maxConnectionsSpinBox, 2, 1);

    // Auto-start
    m_autoStartCheckBox = new QCheckBox("Auto-start server with application");
    configLayout->addWidget(m_autoStartCheckBox, 3, 0, 1, 2);

    mainLayout->addWidget(configGroup);

    // Test button
    m_testButton = new QPushButton("Test Configuration");
    m_testButton->setObjectName("testButton");
    connect(m_testButton, &QPushButton::clicked, this, &QuickServerDialog::onTestConnection);
    mainLayout->addWidget(m_testButton);

    mainLayout->addStretch();

    // Button layout
    auto* buttonLayout = new QHBoxLayout();
    buttonLayout->addStretch();

    m_okButton = new QPushButton("Start Server");
    m_okButton->setObjectName("primaryButton");
    m_okButton->setDefault(true);
    connect(m_okButton, &QPushButton::clicked, this, &QuickServerDialog::onOkClicked);

    m_cancelButton = new QPushButton("Cancel");
    m_cancelButton->setObjectName("secondaryButton");
    connect(m_cancelButton, &QPushButton::clicked, this, &QuickServerDialog::onCancelClicked);

    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_cancelButton);

    mainLayout->addLayout(buttonLayout);
}

void QuickServerDialog::applyDarkTheme()
{
    setStyleSheet(R"(
        QDialog {
            background-color: #2b2b2b;
            color: #ffffff;
            border: none;
        }
        
        QLabel#titleLabel {
            font-size: 18px;
            font-weight: bold;
            color: #4fc3f7;
            margin-bottom: 10px;
        }
        
        QGroupBox {
            font-weight: bold;
            border: 2px solid #404040;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 15px;
            background-color: #353535;
            color: #ffffff;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            color: #4fc3f7;
            background-color: #353535;
        }
        
        QLabel {
            color: #ffffff;
            font-weight: 500;
        }
        
        QSpinBox, QLineEdit {
            background-color: #404040;
            border: 2px solid #555555;
            border-radius: 6px;
            padding: 8px;
            color: #ffffff;
            font-size: 12px;
            min-height: 20px;
        }
        
        QSpinBox:focus, QLineEdit:focus {
            border-color: #4fc3f7;
            background-color: #454545;
        }
        
        QSpinBox::up-button, QSpinBox::down-button {
            background-color: #555555;
            border: 1px solid #666666;
            border-radius: 3px;
            width: 16px;
        }
        
        QSpinBox::up-button:hover, QSpinBox::down-button:hover {
            background-color: #4fc3f7;
        }
        
        QCheckBox {
            color: #ffffff;
            spacing: 8px;
        }
        
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #555555;
            border-radius: 4px;
            background-color: #404040;
        }
        
        QCheckBox::indicator:checked {
            background-color: #4fc3f7;
            border-color: #4fc3f7;
        }
        
        QCheckBox::indicator:checked::after {
            content: "✓";
            color: white;
            font-weight: bold;
        }
        
        QPushButton {
            background-color: #555555;
            border: none;
            color: #ffffff;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            font-size: 12px;
            min-width: 100px;
            min-height: 35px;
        }
        
        QPushButton:hover {
            background-color: #666666;
        }
        
        QPushButton:pressed {
            background-color: #444444;
        }
        
        QPushButton#primaryButton {
            background-color: #4fc3f7;
            color: #000000;
        }
        
        QPushButton#primaryButton:hover {
            background-color: #29b6f6;
        }
        
        QPushButton#primaryButton:pressed {
            background-color: #0288d1;
        }
        
        QPushButton#testButton {
            background-color: #66bb6a;
            color: #000000;
        }
        
        QPushButton#testButton:hover {
            background-color: #4caf50;
        }
        
        QPushButton#secondaryButton {
            background-color: #757575;
        }
        
        QPushButton#secondaryButton:hover {
            background-color: #616161;
        }
    )");
}

// Getters and Setters
int QuickServerDialog::getServerPort() const
{
    return m_portSpinBox ? m_portSpinBox->value() : 4444;
}

void QuickServerDialog::setServerPort(int port)
{
    if (m_portSpinBox) {
        m_portSpinBox->setValue(port);
    }
}

QString QuickServerDialog::getServerPassword() const
{
    return m_passwordLineEdit ? m_passwordLineEdit->text() : "admin";
}

void QuickServerDialog::setServerPassword(const QString& password)
{
    if (m_passwordLineEdit) {
        m_passwordLineEdit->setText(password);
    }
}

int QuickServerDialog::getMaxConnections() const
{
    return m_maxConnectionsSpinBox ? m_maxConnectionsSpinBox->value() : 10;
}

void QuickServerDialog::setMaxConnections(int maxConnections)
{
    if (m_maxConnectionsSpinBox) {
        m_maxConnectionsSpinBox->setValue(maxConnections);
    }
}

bool QuickServerDialog::isAutoStartEnabled() const
{
    return m_autoStartCheckBox ? m_autoStartCheckBox->isChecked() : false;
}

void QuickServerDialog::setAutoStartEnabled(bool enabled)
{
    if (m_autoStartCheckBox) {
        m_autoStartCheckBox->setChecked(enabled);
    }
}

// Slots
void QuickServerDialog::onOkClicked()
{
    if (validateInput()) {
        // Save settings
        QSettings settings;
        settings.setValue("server/port", getServerPort());
        settings.setValue("server/password", getServerPassword());
        settings.setValue("server/maxConnections", getMaxConnections());
        settings.setValue("server/autoStart", isAutoStartEnabled());
        
        emit serverConfigChanged(getServerPort(), getServerPassword(), 
                                getMaxConnections(), isAutoStartEnabled());
        accept();
    }
}

void QuickServerDialog::onCancelClicked()
{
    reject();
}

void QuickServerDialog::onTestConnection()
{
    if (validateInput()) {
        QMessageBox msgBox(this);
        msgBox.setWindowTitle("Configuration Test");
        msgBox.setText(QString("Server Configuration Test\n\n"
                               "Port: %1\n"
                               "Password: %2\n"
                               "Max Clients: %3\n"
                               "Auto-start: %4\n\n"
                               "Configuration appears valid!")
                               .arg(getServerPort())
                               .arg(getServerPassword().isEmpty() ? "(No password)" : "***")
                               .arg(getMaxConnections())
                               .arg(isAutoStartEnabled() ? "Yes" : "No"));
        msgBox.setIcon(QMessageBox::Information);
        msgBox.exec();
    }
}

bool QuickServerDialog::validateInput()
{
    // Validate port
    int port = getServerPort();
    if (port < 1024 || port > 65535) {
        QMessageBox::warning(this, "Invalid Port", 
            "Server port must be between 1024 and 65535.");
        m_portSpinBox->setFocus();
        return false;
    }
    
    // Validate password
    QString password = getServerPassword();
    if (password.length() < 3) {
        QMessageBox::warning(this, "Invalid Password", 
            "Server password must be at least 3 characters long.");
        m_passwordLineEdit->setFocus();
        return false;
    }
    
    return true;
}
