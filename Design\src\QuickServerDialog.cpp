/**
 * @file QuickServerDialog.cpp
 * @brief Implementation of Quick Server Configuration Dialog
 */

#include "QuickServerDialog.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QGroupBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QCheckBox>
#include <QMessageBox>
#include <QSettings>

QuickServerDialog::QuickServerDialog(QWidget* parent)
    : QDialog(parent)
    , m_portSpinBox(nullptr)
    , m_passwordLineEdit(nullptr)
    , m_maxConnectionsSpinBox(nullptr)
    , m_autoStartCheckBox(nullptr)
    , m_testButton(nullptr)
    , m_okButton(nullptr)
    , m_cancelButton(nullptr)
{
    setWindowTitle("Quick Server Configuration");
    setWindowFlags(windowFlags() & ~Qt::WindowContextHelpButtonHint);
    setModal(true);
    setFixedSize(350, 280);
    
    initializeUI();
    applyDarkTheme();
    
    // Load current settings
    QSettings settings;
    setServerPort(settings.value("server/port", 4444).toInt());
    setServerPassword(settings.value("server/password", "admin").toString());
    setMaxConnections(settings.value("server/maxConnections", 10).toInt());
    setAutoStartEnabled(settings.value("server/autoStart", false).toBool());
}

QuickServerDialog::~QuickServerDialog() = default;

void QuickServerDialog::initializeUI()
{
    auto* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(15, 15, 15, 15);
    mainLayout->setSpacing(10);

    // Title
    auto* titleLabel = new QLabel("Quick Server Setup");
    titleLabel->setObjectName("titleLabel");
    titleLabel->setAlignment(Qt::AlignCenter);
    mainLayout->addWidget(titleLabel);

    // Server configuration group
    auto* configGroup = new QGroupBox("Server Settings");
    auto* configLayout = new QGridLayout(configGroup);
    configLayout->setSpacing(8);
    configLayout->setContentsMargins(12, 15, 12, 12);

    // Port
    auto* portLabel = new QLabel("Server Port:");
    m_portSpinBox = new QSpinBox();
    m_portSpinBox->setRange(1024, 65535);
    m_portSpinBox->setValue(4444);
    m_portSpinBox->setSuffix(" (TCP)");
    configLayout->addWidget(portLabel, 0, 0);
    configLayout->addWidget(m_portSpinBox, 0, 1);

    // Password
    auto* passwordLabel = new QLabel("Password:");
    m_passwordLineEdit = new QLineEdit();
    m_passwordLineEdit->setEchoMode(QLineEdit::Password);
    m_passwordLineEdit->setText("admin");
    m_passwordLineEdit->setPlaceholderText("Enter server password");
    configLayout->addWidget(passwordLabel, 1, 0);
    configLayout->addWidget(m_passwordLineEdit, 1, 1);

    // Max connections
    auto* maxConnLabel = new QLabel("Max Clients:");
    m_maxConnectionsSpinBox = new QSpinBox();
    m_maxConnectionsSpinBox->setRange(1, 100);
    m_maxConnectionsSpinBox->setValue(10);
    m_maxConnectionsSpinBox->setSuffix(" clients");
    configLayout->addWidget(maxConnLabel, 2, 0);
    configLayout->addWidget(m_maxConnectionsSpinBox, 2, 1);

    // Auto-start
    m_autoStartCheckBox = new QCheckBox("Auto-start server with application");
    configLayout->addWidget(m_autoStartCheckBox, 3, 0, 1, 2);

    mainLayout->addWidget(configGroup);

    // Test button
    m_testButton = new QPushButton("Test");
    m_testButton->setObjectName("testButton");
    m_testButton->setFixedSize(60, 26);
    connect(m_testButton, &QPushButton::clicked, this, &QuickServerDialog::onTestConnection);

    auto* testLayout = new QHBoxLayout();
    testLayout->addStretch();
    testLayout->addWidget(m_testButton);
    mainLayout->addLayout(testLayout);

    mainLayout->addStretch();

    // Button layout
    auto* buttonLayout = new QHBoxLayout();
    buttonLayout->setSpacing(8);
    buttonLayout->addStretch();

    m_okButton = new QPushButton("Start Server");
    m_okButton->setObjectName("primaryButton");
    m_okButton->setFixedSize(85, 28);
    m_okButton->setDefault(true);
    connect(m_okButton, &QPushButton::clicked, this, &QuickServerDialog::onOkClicked);

    m_cancelButton = new QPushButton("Cancel");
    m_cancelButton->setObjectName("secondaryButton");
    m_cancelButton->setFixedSize(65, 28);
    connect(m_cancelButton, &QPushButton::clicked, this, &QuickServerDialog::onCancelClicked);

    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_cancelButton);

    mainLayout->addLayout(buttonLayout);
}

void QuickServerDialog::applyDarkTheme()
{
    setStyleSheet(R"(
        QDialog {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #3a3a3a, stop:1 #2a2a2a);
            color: #e0e0e0;
            border: 1px solid #555555;
        }

        QLabel#titleLabel {
            font-size: 14px;
            font-weight: 600;
            color: #c0c0c0;
            margin-bottom: 8px;
        }

        QGroupBox {
            font-weight: 500;
            font-size: 11px;
            border: 1px solid #555555;
            border-radius: 3px;
            margin-top: 8px;
            padding-top: 12px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #404040, stop:1 #353535);
            color: #d0d0d0;
        }

        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
            color: #b0b0b0;
            background-color: transparent;
        }
        
        QLabel {
            color: #d0d0d0;
            font-weight: 400;
            font-size: 11px;
        }

        QSpinBox, QLineEdit {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4a4a4a, stop:1 #3a3a3a);
            border: 1px solid #666666;
            border-radius: 2px;
            padding: 4px 6px;
            color: #e0e0e0;
            font-size: 11px;
            min-height: 16px;
            max-height: 22px;
        }

        QSpinBox:focus, QLineEdit:focus {
            border-color: #888888;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #505050, stop:1 #404040);
        }

        QSpinBox::up-button, QSpinBox::down-button {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #606060, stop:1 #505050);
            border: 1px solid #707070;
            border-radius: 1px;
            width: 14px;
        }

        QSpinBox::up-button:hover, QSpinBox::down-button:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #707070, stop:1 #606060);
        }
        
        QCheckBox {
            color: #d0d0d0;
            spacing: 6px;
            font-size: 11px;
        }

        QCheckBox::indicator {
            width: 14px;
            height: 14px;
            border: 1px solid #666666;
            border-radius: 2px;
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #4a4a4a, stop:1 #3a3a3a);
        }

        QCheckBox::indicator:checked {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #707070, stop:1 #606060);
            border-color: #888888;
        }

        QCheckBox::indicator:checked::after {
            content: "✓";
            color: #e0e0e0;
            font-weight: bold;
            font-size: 10px;
        }
        
        QPushButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #606060, stop:1 #505050);
            border: 1px solid #707070;
            color: #e0e0e0;
            padding: 4px 8px;
            border-radius: 2px;
            font-weight: 500;
            font-size: 11px;
        }

        QPushButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #707070, stop:1 #606060);
            border-color: #808080;
        }

        QPushButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #404040, stop:1 #505050);
            border-color: #606060;
        }

        QPushButton#primaryButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #707070, stop:1 #606060);
            border: 1px solid #808080;
            color: #ffffff;
        }

        QPushButton#primaryButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #808080, stop:1 #707070);
        }

        QPushButton#primaryButton:pressed {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #505050, stop:1 #606060);
        }

        QPushButton#testButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #656565, stop:1 #555555);
            border: 1px solid #757575;
        }

        QPushButton#testButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #757575, stop:1 #656565);
        }

        QPushButton#secondaryButton {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #585858, stop:1 #484848);
            border: 1px solid #686868;
        }

        QPushButton#secondaryButton:hover {
            background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                stop:0 #686868, stop:1 #585858);
        }
    )");
}

// Getters and Setters
int QuickServerDialog::getServerPort() const
{
    return m_portSpinBox ? m_portSpinBox->value() : 4444;
}

void QuickServerDialog::setServerPort(int port)
{
    if (m_portSpinBox) {
        m_portSpinBox->setValue(port);
    }
}

QString QuickServerDialog::getServerPassword() const
{
    return m_passwordLineEdit ? m_passwordLineEdit->text() : "admin";
}

void QuickServerDialog::setServerPassword(const QString& password)
{
    if (m_passwordLineEdit) {
        m_passwordLineEdit->setText(password);
    }
}

int QuickServerDialog::getMaxConnections() const
{
    return m_maxConnectionsSpinBox ? m_maxConnectionsSpinBox->value() : 10;
}

void QuickServerDialog::setMaxConnections(int maxConnections)
{
    if (m_maxConnectionsSpinBox) {
        m_maxConnectionsSpinBox->setValue(maxConnections);
    }
}

bool QuickServerDialog::isAutoStartEnabled() const
{
    return m_autoStartCheckBox ? m_autoStartCheckBox->isChecked() : false;
}

void QuickServerDialog::setAutoStartEnabled(bool enabled)
{
    if (m_autoStartCheckBox) {
        m_autoStartCheckBox->setChecked(enabled);
    }
}

// Slots
void QuickServerDialog::onOkClicked()
{
    if (validateInput()) {
        // Save settings
        QSettings settings;
        settings.setValue("server/port", getServerPort());
        settings.setValue("server/password", getServerPassword());
        settings.setValue("server/maxConnections", getMaxConnections());
        settings.setValue("server/autoStart", isAutoStartEnabled());
        
        emit serverConfigChanged(getServerPort(), getServerPassword(), 
                                getMaxConnections(), isAutoStartEnabled());
        accept();
    }
}

void QuickServerDialog::onCancelClicked()
{
    reject();
}

void QuickServerDialog::onTestConnection()
{
    if (validateInput()) {
        QMessageBox msgBox(this);
        msgBox.setWindowTitle("Configuration Test");
        msgBox.setText(QString("Server Configuration Test\n\n"
                               "Port: %1\n"
                               "Password: %2\n"
                               "Max Clients: %3\n"
                               "Auto-start: %4\n\n"
                               "Configuration appears valid!")
                               .arg(getServerPort())
                               .arg(getServerPassword().isEmpty() ? "(No password)" : "***")
                               .arg(getMaxConnections())
                               .arg(isAutoStartEnabled() ? "Yes" : "No"));
        msgBox.setIcon(QMessageBox::Information);
        msgBox.exec();
    }
}

bool QuickServerDialog::validateInput()
{
    // Validate port
    int port = getServerPort();
    if (port < 1024 || port > 65535) {
        QMessageBox::warning(this, "Invalid Port", 
            "Server port must be between 1024 and 65535.");
        m_portSpinBox->setFocus();
        return false;
    }
    
    // Validate password
    QString password = getServerPassword();
    if (password.length() < 3) {
        QMessageBox::warning(this, "Invalid Password", 
            "Server password must be at least 3 characters long.");
        m_passwordLineEdit->setFocus();
        return false;
    }
    
    return true;
}
