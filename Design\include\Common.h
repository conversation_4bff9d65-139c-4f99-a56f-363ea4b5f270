/**
 * @file Common.h
 * @brief Common definitions and includes for HVNC GUI Design
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This file contains common definitions, includes, and utility functions
 * used throughout the HVNC GUI application. It provides a centralized
 * location for shared constants, types, and helper functions.
 */

#pragma once

// Platform-specific includes
#ifdef WINDOWS_PLATFORM
    #ifndef WIN32_LEAN_AND_MEAN
        #define WIN32_LEAN_AND_MEAN
    #endif
    #ifndef NOMINMAX
        #define NOMINMAX
    #endif
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #include <iphlpapi.h>
    // Network API functions (if needed later)
    // #include <lmcons.h>
#endif

// Standard C++ includes
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <unordered_map>
#include <queue>
#include <thread>
#include <mutex>
#include <atomic>
#include <chrono>
#include <functional>
#include <algorithm>
#include <iostream>
#include <fstream>
#include <sstream>
#include <exception>
#include <stdexcept>
#include <cstdint>
#include <cstring>
#include <cassert>

// Qt6 Core includes
#include <QtCore/QObject>
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QByteArray>
#include <QtCore/QTimer>
#include <QtCore/QThread>
#include <QtCore/QMutex>
#include <QtCore/QMutexLocker>
#include <QtCore/QWaitCondition>
#include <QtCore/QQueue>
#include <QtCore/QSettings>
#include <QtCore/QStandardPaths>
#include <QtCore/QDir>
#include <QtCore/QFile>
#include <QtCore/QFileInfo>
#include <QtCore/QDateTime>
#include <QtCore/QDebug>
#include <QtCore/QLoggingCategory>

// Qt6 GUI includes
#include <QtGui/QPixmap>
#include <QtGui/QImage>
#include <QtGui/QPainter>
#include <QtGui/QPalette>
#include <QtGui/QFont>
#include <QtGui/QFontMetrics>
#include <QtGui/QIcon>
#include <QtGui/QKeyEvent>
#include <QtGui/QMouseEvent>
#include <QtGui/QWheelEvent>
#include <QtGui/QResizeEvent>
#include <QtGui/QCloseEvent>
#include <QtGui/QShowEvent>
#include <QtGui/QHideEvent>

// Qt6 Widgets includes
#include <QtWidgets/QApplication>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QWidget>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QSplitter>
#include <QtWidgets/QTabWidget>
#include <QtWidgets/QStackedWidget>
#include <QtWidgets/QScrollArea>
#include <QtWidgets/QLabel>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QToolButton>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QPlainTextEdit>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QCheckBox>
#include <QtWidgets/QRadioButton>
#include <QtWidgets/QSlider>
#include <QtWidgets/QProgressBar>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QMenu>
#include <QtGui/QAction>
#include <QtWidgets/QDialog>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QFileDialog>
#include <QtWidgets/QColorDialog>
#include <QtWidgets/QFontDialog>
#include <QtWidgets/QInputDialog>
#include <QtWidgets/QProgressDialog>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QFrame>
#include <QtWidgets/QSizePolicy>
#include <QtWidgets/QGraphicsView>
#include <QtWidgets/QGraphicsScene>
#include <QtWidgets/QGraphicsPixmapItem>

// Qt6 Network includes
#include <QtNetwork/QTcpSocket>
#include <QtNetwork/QTcpServer>
#include <QtNetwork/QHostAddress>
#include <QtNetwork/QNetworkInterface>

// Windows specific includes
#ifdef _WIN32
    #include <windows.h>
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#endif

// Application constants
namespace HVNCDesign {
    
    // Application information
    constexpr const char* APP_NAME = "HVNC Design";
    constexpr const char* APP_VERSION = "1.0.0";
    constexpr const char* APP_ORGANIZATION = "HVNC Team";
    constexpr const char* APP_DOMAIN = "hvnc.local";
    
    // Network constants
    constexpr int DEFAULT_SERVER_PORT = 4043;
    constexpr int MAX_CLIENTS = 256;
    constexpr int BUFFER_SIZE = 8192;
    constexpr int IMAGE_BUFFER_SIZE = 1024 * 1024; // 1MB
    
    // UI constants
    constexpr int WINDOW_MIN_WIDTH = 1200;
    constexpr int WINDOW_MIN_HEIGHT = 800;
    constexpr int DESKTOP_MIN_WIDTH = 800;
    constexpr int DESKTOP_MIN_HEIGHT = 600;
    constexpr int CONTROL_PANEL_WIDTH = 300;
    constexpr int STATUS_BAR_HEIGHT = 25;
    
    // Timing constants
    constexpr int FRAME_RATE_MS = 33; // ~30 FPS
    constexpr int CONNECTION_TIMEOUT_MS = 5000;
    constexpr int HEARTBEAT_INTERVAL_MS = 1000;
    
    // Color scheme (Dark theme)
    namespace Colors {
        constexpr const char* BACKGROUND_PRIMARY = "#1e1e1e";
        constexpr const char* BACKGROUND_SECONDARY = "#2d2d2d";
        constexpr const char* BACKGROUND_TERTIARY = "#3c3c3c";
        constexpr const char* TEXT_PRIMARY = "#ffffff";
        constexpr const char* TEXT_SECONDARY = "#cccccc";
        constexpr const char* TEXT_DISABLED = "#666666";
        constexpr const char* ACCENT_PRIMARY = "#0078d4";
        constexpr const char* ACCENT_HOVER = "#106ebe";
        constexpr const char* ACCENT_PRESSED = "#005a9e";
        constexpr const char* SUCCESS = "#107c10";
        constexpr const char* WARNING = "#ff8c00";
        constexpr const char* ERROR_COLOR = "#d13438";
        constexpr const char* BORDER = "#484848";
        constexpr const char* SEPARATOR = "#404040";
    }
    
    // Font settings
    namespace Fonts {
        constexpr const char* PRIMARY_FAMILY = "Segoe UI";
        constexpr const char* MONOSPACE_FAMILY = "Consolas";
        constexpr int SIZE_SMALL = 9;
        constexpr int SIZE_NORMAL = 10;
        constexpr int SIZE_MEDIUM = 12;
        constexpr int SIZE_LARGE = 14;
        constexpr int SIZE_XLARGE = 16;
    }
    
    // Spacing and sizing
    namespace Spacing {
        constexpr int TINY = 2;
        constexpr int SMALL = 4;
        constexpr int NORMAL = 8;
        constexpr int MEDIUM = 12;
        constexpr int LARGE = 16;
        constexpr int XLARGE = 24;
        constexpr int XXLARGE = 32;
    }
}

// Forward declarations
class MainWindow;
class DesktopViewer;
class ServerManager;
class ControlPanel;
class ConnectionManager;
class EventHandler;
class StyleManager;
class NetworkThread;
class ImageProcessor;

// Type aliases for convenience
using ClientId = uint32_t;
using PortNumber = uint16_t;
using ImageData = QByteArray;
using PixelBuffer = std::vector<uint8_t>;

// Enumerations
enum class ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Error
};

enum class ServerState {
    Stopped,
    Starting,
    Running,
    Stopping,
    Error
};

enum class MouseButton {
    None = 0,
    Left = 1,
    Right = 2,
    Middle = 4
};

enum class KeyModifier {
    None = 0,
    Ctrl = 1,
    Alt = 2,
    Shift = 4,
    Meta = 8
};

// Utility functions
namespace Utils {
    
    /**
     * @brief Get current timestamp as string
     * @return Formatted timestamp string
     */
    inline QString getCurrentTimestamp() {
        return QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss.zzz");
    }
    
    /**
     * @brief Convert bytes to human readable format
     * @param bytes Number of bytes
     * @return Formatted string (e.g., "1.5 MB")
     */
    QString formatBytes(qint64 bytes);
    
    /**
     * @brief Validate IP address format
     * @param ip IP address string
     * @return True if valid IP address
     */
    bool isValidIpAddress(const QString& ip);
    
    /**
     * @brief Validate port number
     * @param port Port number
     * @return True if valid port (1-65535)
     */
    bool isValidPort(int port);
    
    /**
     * @brief Create directory if it doesn't exist
     * @param path Directory path
     * @return True if directory exists or was created successfully
     */
    bool ensureDirectoryExists(const QString& path);
    
    /**
     * @brief Load image from byte array
     * @param data Image data
     * @return QImage object
     */
    QImage loadImageFromData(const QByteArray& data);
    
    /**
     * @brief Scale image maintaining aspect ratio
     * @param image Source image
     * @param size Target size
     * @return Scaled image
     */
    QImage scaleImageKeepAspect(const QImage& image, const QSize& size);
}

// Logging macros
Q_DECLARE_LOGGING_CATEGORY(hvncDesign)

#define HVNC_DEBUG() qCDebug(hvncDesign)
#define HVNC_INFO() qCInfo(hvncDesign)
#define HVNC_WARNING() qCWarning(hvncDesign)
#define HVNC_CRITICAL() qCCritical(hvncDesign)

// Smart pointer aliases
template<typename T>
using UniquePtr = std::unique_ptr<T>;

template<typename T>
using SharedPtr = std::shared_ptr<T>;

template<typename T>
using WeakPtr = std::weak_ptr<T>;

// Make unique helper for C++11 compatibility
template<typename T, typename... Args>
UniquePtr<T> makeUnique(Args&&... args) {
    return std::make_unique<T>(std::forward<Args>(args)...);
}
