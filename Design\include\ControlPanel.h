/**
 * @file ControlPanel.h
 * @brief Professional HVNC Control Panel with comprehensive server controls
 * <AUTHOR> Design Team
 * @version 2.0.0
 * 
 * This control panel provides professional-grade controls for HVNC server
 * management including port configuration, authentication, connection monitoring,
 * and advanced server settings.
 */

#pragma once

#include "Common.h"

// Forward declarations
class QGroupBox;
class QSpinBox;
class QLineEdit;
class QComboBox;
class QCheckBox;
class QPushButton;
class QLabel;
class QProgressBar;
class QTableWidget;
class QTextEdit;
class QSlider;
class QTabWidget;

/**
 * @class ControlPanel
 * @brief Professional HVNC control panel with comprehensive server management
 * 
 * The ControlPanel provides a professional interface for managing all aspects
 * of the HVNC server including network configuration, security settings,
 * connection monitoring, and performance tuning.
 */
class ControlPanel : public QWidget {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit ControlPanel(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~ControlPanel() override;

    /**
     * @brief Get current server port
     * @return Server port number
     */
    int getServerPort() const;

    /**
     * @brief Set server port
     * @param port Port number
     */
    void setServerPort(int port);

    /**
     * @brief Get server password
     * @return Server password
     */
    QString getServerPassword() const;

    /**
     * @brief Set server password
     * @param password Server password
     */
    void setServerPassword(const QString& password);

    /**
     * @brief Get maximum connections
     * @return Maximum number of connections
     */
    int getMaxConnections() const;

    /**
     * @brief Get compression level
     * @return Compression level (0-9)
     */
    int getCompressionLevel() const;

    /**
     * @brief Get update interval
     * @return Update interval in milliseconds
     */
    int getUpdateInterval() const;

    /**
     * @brief Check if server is running
     * @return True if server is running
     */
    bool isServerRunning() const;

public slots:
    /**
     * @brief Update server status
     * @param running True if server is running
     * @param port Server port
     * @param connections Number of active connections
     */
    void updateServerStatus(bool running, int port, int connections);

    /**
     * @brief Add connection to monitor
     * @param clientId Client ID
     * @param clientIP Client IP address
     * @param connectTime Connection time
     */
    void addConnection(const QString& clientId, const QString& clientIP, const QString& connectTime);

    /**
     * @brief Remove connection from monitor
     * @param clientId Client ID
     */
    void removeConnection(const QString& clientId);

    /**
     * @brief Update connection statistics
     * @param clientId Client ID
     * @param bytesReceived Bytes received
     * @param bytesSent Bytes sent
     */
    void updateConnectionStats(const QString& clientId, qint64 bytesReceived, qint64 bytesSent);

    /**
     * @brief Add log message
     * @param level Log level (INFO, WARNING, ERROR)
     * @param message Log message
     */
    void addLogMessage(const QString& level, const QString& message);

signals:
    /**
     * @brief Server start requested
     * @param port Server port
     * @param password Server password
     * @param maxConnections Maximum connections
     */
    void startServerRequested(int port, const QString& password, int maxConnections);

    /**
     * @brief Server stop requested
     */
    void stopServerRequested();

    /**
     * @brief Settings changed
     */
    void settingsChanged();

    /**
     * @brief Connection disconnect requested
     * @param clientId Client ID to disconnect
     */
    void disconnectClientRequested(const QString& clientId);

    /**
     * @brief Clear logs requested
     */
    void clearLogsRequested();

private slots:
    /**
     * @brief Handle start server button click
     */
    void onStartServer();

    /**
     * @brief Handle stop server button click
     */
    void onStopServer();

    /**
     * @brief Handle settings change
     */
    void onSettingsChanged();

    /**
     * @brief Handle disconnect client
     */
    void onDisconnectClient();

    /**
     * @brief Handle clear logs
     */
    void onClearLogs();

    /**
     * @brief Handle connection table context menu
     * @param pos Context menu position
     */
    void onConnectionTableContextMenu(const QPoint& pos);

private:
    /**
     * @brief Initialize UI components
     */
    void initializeUI();

    /**
     * @brief Create server configuration tab
     * @return Server configuration widget
     */
    QWidget* createServerConfigTab();

    /**
     * @brief Create connection monitor tab
     * @return Connection monitor widget
     */
    QWidget* createConnectionMonitorTab();

    /**
     * @brief Create advanced settings tab
     * @return Advanced settings widget
     */
    QWidget* createAdvancedSettingsTab();

    /**
     * @brief Create logs tab
     * @return Logs widget
     */
    QWidget* createLogsTab();

    /**
     * @brief Apply professional styling
     */
    void applyProfessionalStyling();

    /**
     * @brief Update UI state based on server status
     */
    void updateUIState();

private:
    // Main layout
    QTabWidget* m_tabWidget;

    // Server Configuration
    QGroupBox* m_serverConfigGroup;
    QSpinBox* m_portSpinBox;
    QLineEdit* m_passwordLineEdit;
    QSpinBox* m_maxConnectionsSpinBox;
    QPushButton* m_startServerButton;
    QPushButton* m_stopServerButton;
    QLabel* m_serverStatusLabel;
    QProgressBar* m_serverStatusProgress;

    // Connection Monitor
    QTableWidget* m_connectionTable;
    QLabel* m_activeConnectionsLabel;
    QLabel* m_totalBytesLabel;
    QPushButton* m_disconnectClientButton;
    QPushButton* m_refreshConnectionsButton;

    // Advanced Settings
    QSlider* m_compressionSlider;
    QLabel* m_compressionLabel;
    QSpinBox* m_updateIntervalSpinBox;
    QCheckBox* m_enableLoggingCheckBox;
    QCheckBox* m_enableEncryptionCheckBox;
    QComboBox* m_qualityComboBox;
    QCheckBox* m_autoStartCheckBox;

    // Logs
    QTextEdit* m_logsTextEdit;
    QPushButton* m_clearLogsButton;
    QCheckBox* m_autoScrollLogsCheckBox;

    // State
    bool m_serverRunning;
    int m_activeConnections;
    qint64 m_totalBytesReceived;
    qint64 m_totalBytesSent;
};
