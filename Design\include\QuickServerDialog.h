/**
 * @file QuickServerDialog.h
 * @brief Quick Server Configuration Dialog for HVNC
 * <AUTHOR> Design Team
 * @version 2.0.0
 * 
 * This dialog provides quick access to essential server configuration
 * options without the complexity of the full settings dialog.
 */

#pragma once

#include "Common.h"
#include <QDialog>

// Forward declarations
class QSpinBox;
class QLineEdit;
class QPushButton;
class QLabel;
class QCheckBox;
class QGroupBox;

/**
 * @class QuickServerDialog
 * @brief Quick server configuration dialog
 * 
 * Provides a streamlined interface for configuring essential server
 * settings like port, password, and connection limits.
 */
class QuickServerDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit QuickServerDialog(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~QuickServerDialog() override;

    /**
     * @brief Get server port
     * @return Server port number
     */
    int getServerPort() const;

    /**
     * @brief Set server port
     * @param port Server port number
     */
    void setServerPort(int port);

    /**
     * @brief Get server password
     * @return Server password
     */
    QString getServerPassword() const;

    /**
     * @brief Set server password
     * @param password Server password
     */
    void setServerPassword(const QString& password);

    /**
     * @brief Get maximum connections
     * @return Maximum number of connections
     */
    int getMaxConnections() const;

    /**
     * @brief Set maximum connections
     * @param maxConnections Maximum number of connections
     */
    void setMaxConnections(int maxConnections);

    /**
     * @brief Check if auto-start is enabled
     * @return True if auto-start is enabled
     */
    bool isAutoStartEnabled() const;

    /**
     * @brief Set auto-start enabled
     * @param enabled Auto-start enabled
     */
    void setAutoStartEnabled(bool enabled);

signals:
    /**
     * @brief Server configuration changed
     * @param port Server port
     * @param password Server password
     * @param maxConnections Maximum connections
     * @param autoStart Auto-start enabled
     */
    void serverConfigChanged(int port, const QString& password, int maxConnections, bool autoStart);

private slots:
    /**
     * @brief Handle OK button click
     */
    void onOkClicked();

    /**
     * @brief Handle Cancel button click
     */
    void onCancelClicked();

    /**
     * @brief Handle test connection button click
     */
    void onTestConnection();

private:
    /**
     * @brief Initialize UI components
     */
    void initializeUI();

    /**
     * @brief Apply dark theme styling
     */
    void applyDarkTheme();

    /**
     * @brief Validate input
     * @return True if input is valid
     */
    bool validateInput();

private:
    // UI components
    QSpinBox* m_portSpinBox;
    QLineEdit* m_passwordLineEdit;
    QSpinBox* m_maxConnectionsSpinBox;
    QCheckBox* m_autoStartCheckBox;
    QPushButton* m_testButton;
    QPushButton* m_okButton;
    QPushButton* m_cancelButton;
};
