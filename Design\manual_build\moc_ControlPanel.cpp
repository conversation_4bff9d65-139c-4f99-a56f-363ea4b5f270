/****************************************************************************
** Meta object code from reading C++ file 'ControlPanel.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../include/ControlPanel.h"
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ControlPanel.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSControlPanelENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSControlPanelENDCLASS = QtMocHelpers::stringData(
    "ControlPanel",
    "startServerRequested",
    "",
    "port",
    "password",
    "maxConnections",
    "stopServerRequested",
    "settingsChanged",
    "disconnectClientRequested",
    "clientId",
    "clearLogsRequested",
    "updateServerStatus",
    "running",
    "connections",
    "addConnection",
    "clientIP",
    "connectTime",
    "removeConnection",
    "updateConnectionStats",
    "bytesReceived",
    "bytesSent",
    "addLogMessage",
    "level",
    "message",
    "onStartServer",
    "onStopServer",
    "onSettingsChanged",
    "onDisconnectClient",
    "onClearLogs",
    "onConnectionTableContextMenu",
    "pos"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSControlPanelENDCLASS_t {
    uint offsetsAndSizes[62];
    char stringdata0[13];
    char stringdata1[21];
    char stringdata2[1];
    char stringdata3[5];
    char stringdata4[9];
    char stringdata5[15];
    char stringdata6[20];
    char stringdata7[16];
    char stringdata8[26];
    char stringdata9[9];
    char stringdata10[19];
    char stringdata11[19];
    char stringdata12[8];
    char stringdata13[12];
    char stringdata14[14];
    char stringdata15[9];
    char stringdata16[12];
    char stringdata17[17];
    char stringdata18[22];
    char stringdata19[14];
    char stringdata20[10];
    char stringdata21[14];
    char stringdata22[6];
    char stringdata23[8];
    char stringdata24[14];
    char stringdata25[13];
    char stringdata26[18];
    char stringdata27[19];
    char stringdata28[12];
    char stringdata29[29];
    char stringdata30[4];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSControlPanelENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSControlPanelENDCLASS_t qt_meta_stringdata_CLASSControlPanelENDCLASS = {
    {
        QT_MOC_LITERAL(0, 12),  // "ControlPanel"
        QT_MOC_LITERAL(13, 20),  // "startServerRequested"
        QT_MOC_LITERAL(34, 0),  // ""
        QT_MOC_LITERAL(35, 4),  // "port"
        QT_MOC_LITERAL(40, 8),  // "password"
        QT_MOC_LITERAL(49, 14),  // "maxConnections"
        QT_MOC_LITERAL(64, 19),  // "stopServerRequested"
        QT_MOC_LITERAL(84, 15),  // "settingsChanged"
        QT_MOC_LITERAL(100, 25),  // "disconnectClientRequested"
        QT_MOC_LITERAL(126, 8),  // "clientId"
        QT_MOC_LITERAL(135, 18),  // "clearLogsRequested"
        QT_MOC_LITERAL(154, 18),  // "updateServerStatus"
        QT_MOC_LITERAL(173, 7),  // "running"
        QT_MOC_LITERAL(181, 11),  // "connections"
        QT_MOC_LITERAL(193, 13),  // "addConnection"
        QT_MOC_LITERAL(207, 8),  // "clientIP"
        QT_MOC_LITERAL(216, 11),  // "connectTime"
        QT_MOC_LITERAL(228, 16),  // "removeConnection"
        QT_MOC_LITERAL(245, 21),  // "updateConnectionStats"
        QT_MOC_LITERAL(267, 13),  // "bytesReceived"
        QT_MOC_LITERAL(281, 9),  // "bytesSent"
        QT_MOC_LITERAL(291, 13),  // "addLogMessage"
        QT_MOC_LITERAL(305, 5),  // "level"
        QT_MOC_LITERAL(311, 7),  // "message"
        QT_MOC_LITERAL(319, 13),  // "onStartServer"
        QT_MOC_LITERAL(333, 12),  // "onStopServer"
        QT_MOC_LITERAL(346, 17),  // "onSettingsChanged"
        QT_MOC_LITERAL(364, 18),  // "onDisconnectClient"
        QT_MOC_LITERAL(383, 11),  // "onClearLogs"
        QT_MOC_LITERAL(395, 28),  // "onConnectionTableContextMenu"
        QT_MOC_LITERAL(424, 3)   // "pos"
    },
    "ControlPanel",
    "startServerRequested",
    "",
    "port",
    "password",
    "maxConnections",
    "stopServerRequested",
    "settingsChanged",
    "disconnectClientRequested",
    "clientId",
    "clearLogsRequested",
    "updateServerStatus",
    "running",
    "connections",
    "addConnection",
    "clientIP",
    "connectTime",
    "removeConnection",
    "updateConnectionStats",
    "bytesReceived",
    "bytesSent",
    "addLogMessage",
    "level",
    "message",
    "onStartServer",
    "onStopServer",
    "onSettingsChanged",
    "onDisconnectClient",
    "onClearLogs",
    "onConnectionTableContextMenu",
    "pos"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSControlPanelENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       5,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    3,  110,    2, 0x06,    1 /* Public */,
       6,    0,  117,    2, 0x06,    5 /* Public */,
       7,    0,  118,    2, 0x06,    6 /* Public */,
       8,    1,  119,    2, 0x06,    7 /* Public */,
      10,    0,  122,    2, 0x06,    9 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      11,    3,  123,    2, 0x0a,   10 /* Public */,
      14,    3,  130,    2, 0x0a,   14 /* Public */,
      17,    1,  137,    2, 0x0a,   18 /* Public */,
      18,    3,  140,    2, 0x0a,   20 /* Public */,
      21,    2,  147,    2, 0x0a,   24 /* Public */,
      24,    0,  152,    2, 0x08,   27 /* Private */,
      25,    0,  153,    2, 0x08,   28 /* Private */,
      26,    0,  154,    2, 0x08,   29 /* Private */,
      27,    0,  155,    2, 0x08,   30 /* Private */,
      28,    0,  156,    2, 0x08,   31 /* Private */,
      29,    1,  157,    2, 0x08,   32 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int,    3,    4,    5,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void,

 // slots: parameters
    QMetaType::Void, QMetaType::Bool, QMetaType::Int, QMetaType::Int,   12,    3,   13,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString,    9,   15,   16,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void, QMetaType::QString, QMetaType::LongLong, QMetaType::LongLong,    9,   19,   20,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   22,   23,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   30,

       0        // eod
};

Q_CONSTINIT const QMetaObject ControlPanel::staticMetaObject = { {
    QMetaObject::SuperData::link<QWidget::staticMetaObject>(),
    qt_meta_stringdata_CLASSControlPanelENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSControlPanelENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSControlPanelENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ControlPanel, std::true_type>,
        // method 'startServerRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'stopServerRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'settingsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'disconnectClientRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'clearLogsRequested'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'updateServerStatus'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'addConnection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'removeConnection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'updateConnectionStats'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<qint64, std::false_type>,
        QtPrivate::TypeAndForceComplete<qint64, std::false_type>,
        // method 'addLogMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onStartServer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStopServer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSettingsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDisconnectClient'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onClearLogs'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConnectionTableContextMenu'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>
    >,
    nullptr
} };

void ControlPanel::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ControlPanel *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->startServerRequested((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 1: _t->stopServerRequested(); break;
        case 2: _t->settingsChanged(); break;
        case 3: _t->disconnectClientRequested((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 4: _t->clearLogsRequested(); break;
        case 5: _t->updateServerStatus((*reinterpret_cast< std::add_pointer_t<bool>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 6: _t->addConnection((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[3]))); break;
        case 7: _t->removeConnection((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 8: _t->updateConnectionStats((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[3]))); break;
        case 9: _t->addLogMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 10: _t->onStartServer(); break;
        case 11: _t->onStopServer(); break;
        case 12: _t->onSettingsChanged(); break;
        case 13: _t->onDisconnectClient(); break;
        case 14: _t->onClearLogs(); break;
        case 15: _t->onConnectionTableContextMenu((*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[1]))); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ControlPanel::*)(int , const QString & , int );
            if (_t _q_method = &ControlPanel::startServerRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ControlPanel::*)();
            if (_t _q_method = &ControlPanel::stopServerRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ControlPanel::*)();
            if (_t _q_method = &ControlPanel::settingsChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ControlPanel::*)(const QString & );
            if (_t _q_method = &ControlPanel::disconnectClientRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ControlPanel::*)();
            if (_t _q_method = &ControlPanel::clearLogsRequested; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
    }
}

const QMetaObject *ControlPanel::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ControlPanel::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSControlPanelENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ControlPanel::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void ControlPanel::startServerRequested(int _t1, const QString & _t2, int _t3)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t3))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ControlPanel::stopServerRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 1, nullptr);
}

// SIGNAL 2
void ControlPanel::settingsChanged()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ControlPanel::disconnectClientRequested(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ControlPanel::clearLogsRequested()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}
QT_WARNING_POP
