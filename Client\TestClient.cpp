#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>

#pragma comment(lib, "ws2_32.lib")

// Simple test client that connects and stays connected
class TestHVNCClient {
private:
    SOCKET clientSocket;
    std::string serverHost;
    int serverPort;
    bool connected;

public:
    TestHVNCClient(const std::string& host, int port) 
        : serverHost(host), serverPort(port), connected(false), clientSocket(INVALID_SOCKET) {
    }

    ~TestHVNCClient() {
        Disconnect();
        WSACleanup();
    }

    bool Initialize() {
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            std::cout << "WSAStartup failed: " << result << std::endl;
            return false;
        }
        std::cout << "Winsock initialized" << std::endl;
        return true;
    }

    bool Connect() {
        std::cout << "Attempting to connect to " << serverHost << ":" << serverPort << std::endl;
        
        clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (clientSocket == INVALID_SOCKET) {
            std::cout << "Socket creation failed: " << WSAGetLastError() << std::endl;
            return false;
        }

        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(serverPort);
        
        // Convert IP address
        if (inet_pton(AF_INET, serverHost.c_str(), &serverAddr.sin_addr) <= 0) {
            std::cout << "Invalid IP address: " << serverHost << std::endl;
            closesocket(clientSocket);
            return false;
        }

        // Set socket timeout
        DWORD timeout = 5000; // 5 seconds
        setsockopt(clientSocket, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout));
        setsockopt(clientSocket, SOL_SOCKET, SO_SNDTIMEO, (char*)&timeout, sizeof(timeout));

        // Connect to server
        if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            int error = WSAGetLastError();
            std::cout << "Connection failed: " << error << std::endl;
            closesocket(clientSocket);
            return false;
        }

        connected = true;
        std::cout << "Connected successfully!" << std::endl;
        return true;
    }

    void SendDesktopImage() {
        if (!connected) return;

        // Get desktop dimensions
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);

        std::cout << "Capturing desktop: " << screenWidth << "x" << screenHeight << std::endl;

        // Create device contexts
        HDC hdcScreen = GetDC(NULL);
        HDC hdcMem = CreateCompatibleDC(hdcScreen);

        // Create bitmap
        HBITMAP hBitmap = CreateCompatibleBitmap(hdcScreen, screenWidth, screenHeight);
        HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

        // Copy screen to bitmap
        BitBlt(hdcMem, 0, 0, screenWidth, screenHeight, hdcScreen, 0, 0, SRCCOPY);

        // Get bitmap data
        BITMAPINFOHEADER bi;
        bi.biSize = sizeof(BITMAPINFOHEADER);
        bi.biWidth = screenWidth;
        bi.biHeight = -screenHeight; // Top-down DIB
        bi.biPlanes = 1;
        bi.biBitCount = 24; // 24-bit RGB
        bi.biCompression = BI_RGB;
        bi.biSizeImage = 0;
        bi.biXPelsPerMeter = 0;
        bi.biYPelsPerMeter = 0;
        bi.biClrUsed = 0;
        bi.biClrImportant = 0;

        // Calculate image size
        int imageSize = screenWidth * screenHeight * 3; // 24-bit RGB
        std::vector<char> imageData(imageSize);

        // Get bitmap bits
        GetDIBits(hdcScreen, hBitmap, 0, screenHeight, imageData.data(), (BITMAPINFO*)&bi, DIB_RGB_COLORS);

        // Create Qt-compatible message (QDataStream format)
        std::vector<char> message;

        // Add message type (0x2001 for desktop image)
        uint32_t messageType = 0x2001;
        message.insert(message.end(), (char*)&messageType, (char*)&messageType + 4);

        // Add image dimensions
        uint32_t width = screenWidth;
        uint32_t height = screenHeight;
        message.insert(message.end(), (char*)&width, (char*)&width + 4);
        message.insert(message.end(), (char*)&height, (char*)&height + 4);

        // Add image size
        uint32_t imgSize = imageSize;
        message.insert(message.end(), (char*)&imgSize, (char*)&imgSize + 4);

        // Add image data (first 1KB only for testing)
        int testDataSize = (1024 < imageSize) ? 1024 : imageSize;
        message.insert(message.end(), imageData.begin(), imageData.begin() + testDataSize);

        // Send message
        int result = send(clientSocket, message.data(), (int)message.size(), 0);
        if (result == SOCKET_ERROR) {
            std::cout << "Desktop image send failed: " << WSAGetLastError() << std::endl;
        } else {
            std::cout << "Sent desktop image: " << screenWidth << "x" << screenHeight
                      << " (" << message.size() << " bytes)" << std::endl;
        }

        // Cleanup
        SelectObject(hdcMem, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hdcMem);
        ReleaseDC(NULL, hdcScreen);
    }

    void RunClientLoop() {
        if (!connected) return;

        std::cout << "Starting client loop..." << std::endl;

        // Send initial desktop image
        SendDesktopImage();

        // Keep-alive loop
        char buffer[1024];
        int loopCount = 0;
        
        while (connected && loopCount < 300) { // Run for 5 minutes max
            // Check for server messages
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(clientSocket, &readSet);
            
            timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int result = select(0, &readSet, nullptr, nullptr, &timeout);
            if (result > 0) {
                int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
                if (bytesReceived <= 0) {
                    std::cout << "Connection lost (recv returned " << bytesReceived << ")" << std::endl;
                    break;
                }
                buffer[bytesReceived] = '\0';
                std::cout << "Received: " << buffer << std::endl;
                
                // Process server commands
                if (strstr(buffer, "DISCONNECT")) {
                    std::cout << "Server requested disconnect" << std::endl;
                    break;
                }
            } else if (result == SOCKET_ERROR) {
                std::cout << "Select error: " << WSAGetLastError() << std::endl;
                break;
            }
            
            // Send desktop image every 5 seconds (for testing)
            if (++loopCount % 5 == 0) {
                SendDesktopImage();
            }
            
            Sleep(1000); // Wait 1 second
        }
        
        std::cout << "Client loop ended" << std::endl;
    }

    void Disconnect() {
        if (connected) {
            connected = false;
            if (clientSocket != INVALID_SOCKET) {
                closesocket(clientSocket);
                clientSocket = INVALID_SOCKET;
            }
            std::cout << "Disconnected" << std::endl;
        }
    }
};

int main() {
    // Show console for debugging
    AllocConsole();
    freopen_s((FILE**)stdout, "CONOUT$", "w", stdout);
    freopen_s((FILE**)stderr, "CONOUT$", "w", stderr);
    freopen_s((FILE**)stdin, "CONIN$", "r", stdin);
    
    std::cout << "HVNC Test Client Starting..." << std::endl;
    
    // Configuration - use localhost for testing
    const std::string host = "127.0.0.1";  // localhost
    const int port = 8000;
    
    std::cout << "Target: " << host << ":" << port << std::endl;
    
    // Create and run client
    TestHVNCClient client(host, port);
    
    if (!client.Initialize()) {
        std::cout << "Failed to initialize client" << std::endl;
        std::cout << "Press Enter to exit..." << std::endl;
        std::cin.get();
        return 1;
    }
    
    // Retry connection loop
    int retryCount = 0;
    const int maxRetries = 5;
    
    while (retryCount < maxRetries) {
        std::cout << "Connection attempt " << (retryCount + 1) << "/" << maxRetries << std::endl;
        
        if (client.Connect()) {
            // Connected successfully, run client loop
            client.RunClientLoop();
            break;
        } else {
            // Connection failed, wait and retry
            retryCount++;
            if (retryCount < maxRetries) {
                std::cout << "Retrying in 3 seconds..." << std::endl;
                Sleep(3000);
            }
        }
    }
    
    if (retryCount >= maxRetries) {
        std::cout << "Failed to connect after " << maxRetries << " attempts" << std::endl;
    }
    
    std::cout << "Press Enter to exit..." << std::endl;
    std::cin.get();
    return 0;
}
