@echo off
:: ============================================================================
:: HVNC Complete Build System - Full Qt6 Windows Build
:: ============================================================================
:: This script builds the complete HVNC system with Qt6 Design/ GUI for Windows
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC Complete Build System - Full Qt6                    ║%NC%
echo %BLUE%║                    Building Windows .exe with Design/ GUI                   ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Check for Qt6 installation
echo %CYAN%[STEP 1]%NC% Checking for Qt6 installation...

set "QT6_FOUND=false"
set "QT6_PATH="

:: Check for Qt6 in common locations
if exist "C:\Qt\6.5.3\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.5.3 MSVC 2019 64-bit
    goto :check_vs
)

if exist "C:\Qt\6.5.0\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.5.0\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.5.0 MSVC 2019 64-bit
    goto :check_vs
)

if exist "C:\Qt\6.4.0\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.4.0\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.4.0 MSVC 2019 64-bit
    goto :check_vs
)

if exist "C:\Qt\6.6.0\msvc2019_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.6.0\msvc2019_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.6.0 MSVC 2019 64-bit
    goto :check_vs
)

if exist "C:\Qt\6.5.0\msvc2022_64\bin\qmake.exe" (
    set "QT6_PATH=C:\Qt\6.5.0\msvc2022_64\bin"
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6.5.0 MSVC 2022 64-bit
    goto :check_vs
)

:: Check if qmake is in PATH
where qmake >nul 2>&1
if %errorlevel% equ 0 (
    set "QT6_FOUND=true"
    echo %GREEN%[FOUND]%NC% Qt6 found in system PATH
    goto :check_vs
)

if "%QT6_FOUND%"=="false" (
    echo %RED%[ERROR]%NC% Qt6 not found!
    echo.
    echo %YELLOW%[REQUIRED]%NC% Please install Qt6 for Windows:
    echo   1. Download Qt6 from: https://www.qt.io/download
    echo   2. Install Qt6 with MSVC 2019 or 2022 64-bit compiler
    echo   3. Make sure to install Qt6 Core, Widgets, and Network modules
    echo   4. Add Qt6 bin directory to your PATH environment variable
    echo.
    echo %CYAN%[HINT]%NC% Recommended installation path: C:\Qt\6.5.0\msvc2019_64\
    pause
    exit /b 1
)

:check_vs
:: Step 2: Check for Visual Studio
echo %CYAN%[STEP 2]%NC% Checking Visual Studio installation...

set "VS_FOUND=false"
set "VCVARS_PATH="

:: Check for Visual Studio 2022
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=true"
    echo %GREEN%[FOUND]%NC% Visual Studio 2022 Community
    goto :setup_env
)

if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=true"
    echo %GREEN%[FOUND]%NC% Visual Studio 2022 Professional
    goto :setup_env
)

:: Check for Visual Studio 2019
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=true"
    echo %GREEN%[FOUND]%NC% Visual Studio 2019 Community
    goto :setup_env
)

if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat" (
    set "VCVARS_PATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\Professional\VC\Auxiliary\Build\vcvars64.bat"
    set "VS_FOUND=true"
    echo %GREEN%[FOUND]%NC% Visual Studio 2019 Professional
    goto :setup_env
)

if "%VS_FOUND%"=="false" (
    echo %RED%[ERROR]%NC% Visual Studio not found!
    echo.
    echo %YELLOW%[REQUIRED]%NC% Please install Visual Studio:
    echo   1. Download Visual Studio Community (free) from: https://visualstudio.microsoft.com/
    echo   2. Install with "Desktop development with C++" workload
    echo   3. Make sure MSVC compiler and Windows SDK are included
    echo.
    pause
    exit /b 1
)

:setup_env
:: Step 3: Setup build environment
echo %CYAN%[STEP 3]%NC% Setting up build environment...

:: Add Qt6 to PATH if found
if defined QT6_PATH (
    set "PATH=%QT6_PATH%;%PATH%"
    echo %GREEN%[ENV]%NC% Qt6 added to PATH: %QT6_PATH%
)

:: Setup Visual Studio environment
echo %GREEN%[ENV]%NC% Setting up Visual Studio environment...
call "%VCVARS_PATH%" >nul 2>&1

:: Verify tools are available
where qmake >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% qmake not found in PATH after setup
    pause
    exit /b 1
)

where cl >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% MSVC compiler (cl) not found in PATH after setup
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Build environment ready

:: Step 4: Build Design/ Qt6 GUI
echo.
echo %CYAN%[STEP 4]%NC% Building Design/ Qt6 GUI for Windows...

:: Navigate to Design directory
cd /d "%~dp0Design"

if not exist "HVNCDesign.pro" (
    echo %RED%[ERROR]%NC% HVNCDesign.pro not found in Design/ directory!
    pause
    exit /b 1
)

:: Clean previous builds
echo %CYAN%[CLEAN]%NC% Cleaning previous builds...
if exist "build" rmdir /s /q "build" 2>nul
if exist "Makefile*" del /q "Makefile*" 2>nul
if exist "*.exe" del /q "*.exe" 2>nul

:: Create build directories
mkdir "build\release" 2>nul
mkdir "build\debug" 2>nul

:: Generate Makefile for release build
echo %BLUE%[QMAKE]%NC% Generating Windows Makefile for release build...
qmake HVNCDesign.pro -spec win32-msvc "CONFIG+=release" "CONFIG-=debug"

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Failed to generate Makefile with qmake
    echo %YELLOW%[DEBUG]%NC% qmake output above may contain error details
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Makefile generated successfully

:: Build the application
echo %BLUE%[BUILD]%NC% Building HVNC Design GUI for Windows (Release)...
nmake release

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Failed to build HVNC Design GUI
    echo %YELLOW%[DEBUG]%NC% Build output above may contain error details
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Build completed successfully

:: Step 5: Verify and deploy
echo.
echo %CYAN%[STEP 5]%NC% Verifying build output...

if exist "build\release\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe built successfully!
    
    :: Copy to root directory for easy access
    copy "build\release\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
    )
    
    :: Get file size and version info
    for %%F in ("build\release\HVNCDesign.exe") do (
        echo %CYAN%[INFO]%NC% File size: %%~zF bytes
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found after build
    echo %YELLOW%[DEBUG]%NC% Checking build directory contents...
    dir "build\release\" 2>nul
    pause
    exit /b 1
)

:: Step 6: Build Client (if needed)
echo.
echo %CYAN%[STEP 6]%NC% Building HVNC Client...

cd /d "%~dp0"

if not exist "Client.exe" (
    echo %BLUE%[BUILD]%NC% Building HVNC Client...
    cl /EHsc /std:c++20 /Fe:Client.exe ^
       Client/Main.cpp ^
       common/Api.cpp ^
       common/Utils.cpp ^
       Client/HiddenDesktop.cpp ^
       /I. /Icommon /IClient ^
       /link ws2_32.lib user32.lib kernel32.lib gdi32.lib advapi32.lib shell32.lib ^
       /SUBSYSTEM:CONSOLE

    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% Failed to build Client.exe, but continuing...
    ) else (
        echo %GREEN%[SUCCESS]%NC% Client.exe built successfully
    )
) else (
    echo %GREEN%[EXISTS]%NC% Client.exe already exists
)

:: Success summary
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        COMPLETE BUILD SUCCESSFUL!                           ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.
echo %CYAN%Built Applications:%NC%
if exist "HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for HVNC control
)
if exist "Design\build\release\HVNCDesign.exe" (
    echo   %GREEN%✓%NC% Design\build\release\HVNCDesign.exe - Original build location
)
if exist "Client.exe" (
    echo   %GREEN%✓%NC% Client.exe - Hidden agent (captures desktop, sends to server)
)

echo.
echo %CYAN%Usage Instructions:%NC%
echo   %WHITE%1.%NC% Run HVNCDesign.exe to open the professional Qt6 GUI
echo   %WHITE%2.%NC% Use the HVNC Server menu to configure and start the server
echo   %WHITE%3.%NC% Deploy Client.exe to target computers (runs hidden)
echo   %WHITE%4.%NC% Clients will connect and send desktop images to the GUI
echo   %WHITE%5.%NC% Control remote computers through the Qt6 interface!

echo.
echo %YELLOW%[ARCHITECTURE]%NC% 
echo   • HVNCDesign.exe: Professional Qt6 controller (YOUR computer)
echo   • Client.exe: Hidden agent (TARGET computer - captures desktop)
echo   • Data flow: Client captures → GUI displays (CORRECT!)
echo.

echo %GREEN%[COMPLETE]%NC% Full Qt6 Windows build completed successfully!
echo.
pause
