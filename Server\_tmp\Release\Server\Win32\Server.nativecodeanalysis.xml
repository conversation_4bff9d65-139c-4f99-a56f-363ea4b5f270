<?xml version="1.0" encoding="UTF-8"?>
<DEFECTS>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>519</LINE>
      <COLUMN>19</COLUMN>
    </SFA>
    <DEFECTCODE>6340</DEFECTCODE>
    <DESCRIPTION>Mismatch on sign: 'const unsigned long' passed as _Param_(3) when some signed type is required in call to 'wprintf'.</DESCRIPTION>
    <FUNCTION>ClientThread</FUNCTION>
    <DECORATED>?ClientThread@@YGKPAX@Z</DECORATED>
    <FUNCLINE>323</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>443</LINE>
      <COLUMN>40</COLUMN>
    </SFA>
    <DEFECTCODE>6385</DEFECTCODE>
    <DESCRIPTION>Reading invalid data from 'newPixels':  the readable size is 'newPixelsSize' bytes, but '2' bytes may be read.</DESCRIPTION>
    <FUNCTION>ClientThread</FUNCTION>
    <DECORATED>?ClientThread@@YGKPAX@Z</DECORATED>
    <FUNCLINE>323</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>4</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>325</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>326</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>327</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>328</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>329</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>331</LINE>
        <COLUMN>49</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>336</LINE>
        <COLUMN>12</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>341</LINE>
        <COLUMN>59</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>347</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>348</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>349</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>350</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>351</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>353</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>355</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>356</LINE>
        <COLUMN>9</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>361</LINE>
        <COLUMN>47</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>363</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>364</LINE>
        <COLUMN>31</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>365</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>366</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>367</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>368</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'newPixels' may be NULL (Enter this branch)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>434</LINE>
        <COLUMN>25</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'i' is NULL</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>434</LINE>
        <COLUMN>34</COLUMN>
        <KEYEVENT>
          <ID>3</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this loop, (assume 'i&lt;newPixelsSize')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>437</LINE>
        <COLUMN>61</COLUMN>
        <KEYEVENT>
          <ID>4</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>442</LINE>
        <COLUMN>36</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>443</LINE>
        <COLUMN>40</COLUMN>
        <KEYEVENT>
          <ID>5</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>Invalid read from 'newPixels', (outside its readable range)</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>443</LINE>
      <COLUMN>40</COLUMN>
    </SFA>
    <DEFECTCODE>6386</DEFECTCODE>
    <DESCRIPTION>Buffer overrun while writing to 'client-&gt;pixels':  the writable size is 'newPixelsSize' bytes, but '2' bytes might be written.</DESCRIPTION>
    <FUNCTION>ClientThread</FUNCTION>
    <DECORATED>?ClientThread@@YGKPAX@Z</DECORATED>
    <FUNCLINE>323</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>4</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>325</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>326</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>327</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>328</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>329</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>331</LINE>
        <COLUMN>49</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>336</LINE>
        <COLUMN>12</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>341</LINE>
        <COLUMN>59</COLUMN>
        <KEYEVENT>
          <ID>3</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>347</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>348</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>349</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>350</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>351</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>353</LINE>
        <COLUMN>17</COLUMN>
        <KEYEVENT>
          <ID>4</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this branch, (assume 'connection==desktop')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>355</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>356</LINE>
        <COLUMN>9</COLUMN>
        <KEYEVENT>
          <ID>5</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '!client' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>361</LINE>
        <COLUMN>47</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>363</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>364</LINE>
        <COLUMN>31</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>365</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>366</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>367</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>368</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
        <KEYEVENT>
          <ID>6</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this loop, (assume '1')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
        <KEYEVENT>
          <ID>7</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'rect.right==0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
        <KEYEVENT>
          <ID>8</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '((realRight*3))%4' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
        <KEYEVENT>
          <ID>9</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realRight)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
        <KEYEVENT>
          <ID>10</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realBottom)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
        <KEYEVENT>
          <ID>11</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
        <KEYEVENT>
          <ID>12</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '!recvPixels' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
        <KEYEVENT>
          <ID>13</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
        <KEYEVENT>
          <ID>14</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
        <KEYEVENT>
          <ID>15</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
        <KEYEVENT>
          <ID>16</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
        <KEYEVENT>
          <ID>17</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
        <KEYEVENT>
          <ID>18</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'read&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
        <KEYEVENT>
          <ID>19</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this loop, (assume 'totalRead!=size' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
        <KEYEVENT>
          <ID>20</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>450</LINE>
        <COLUMN>19</COLUMN>
        <KEYEVENT>
          <ID>21</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'pixels' is an Input to 'free' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\ucrt\corecrt_malloc.h:89)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>451</LINE>
        <COLUMN>30</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>454</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>455</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>456</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>458</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>459</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>461</LINE>
        <COLUMN>42</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>462</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>463</LINE>
        <COLUMN>39</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>464</LINE>
        <COLUMN>21</COLUMN>
        <KEYEVENT>
          <ID>22</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'pixels' is an Input to 'SetDIBits' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\um\wingdi.h:4682)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>472</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>473</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>474</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>475</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>477</LINE>
        <COLUMN>25</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>479</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>480</LINE>
        <COLUMN>21</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>482</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>484</LINE>
        <COLUMN>26</COLUMN>
        <KEYEVENT>
          <ID>23</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, 0)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
        <KEYEVENT>
          <ID>24</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Continue this loop, (assume '1')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
        <KEYEVENT>
          <ID>25</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'rect.right==0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
        <KEYEVENT>
          <ID>26</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '((realRight*3))%4' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
        <KEYEVENT>
          <ID>27</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realRight)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
        <KEYEVENT>
          <ID>28</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realBottom)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
        <KEYEVENT>
          <ID>29</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
        <KEYEVENT>
          <ID>30</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '!recvPixels' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
        <KEYEVENT>
          <ID>31</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
        <KEYEVENT>
          <ID>32</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
        <KEYEVENT>
          <ID>33</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
        <KEYEVENT>
          <ID>34</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
        <KEYEVENT>
          <ID>35</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
        <KEYEVENT>
          <ID>36</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'read&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
        <KEYEVENT>
          <ID>37</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this loop, (assume 'totalRead!=size' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
        <KEYEVENT>
          <ID>38</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this branch, (assume '&lt;branch condition&gt;')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>434</LINE>
        <COLUMN>25</COLUMN>
        <KEYEVENT>
          <ID>39</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'i' is NULL</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>434</LINE>
        <COLUMN>34</COLUMN>
        <KEYEVENT>
          <ID>40</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this loop, (assume 'i&lt;newPixelsSize')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>437</LINE>
        <COLUMN>61</COLUMN>
        <KEYEVENT>
          <ID>41</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>442</LINE>
        <COLUMN>36</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>443</LINE>
        <COLUMN>40</COLUMN>
        <KEYEVENT>
          <ID>42</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>Invalid write to 'client-&gt;pixels', (outside its writable range)</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>464</LINE>
      <COLUMN>21</COLUMN>
    </SFA>
    <DEFECTCODE>6387</DEFECTCODE>
    <DESCRIPTION>'client-&gt;pixels' could be '0':  this does not adhere to the specification for the function 'SetDIBits'. </DESCRIPTION>
    <FUNCTION>ClientThread</FUNCTION>
    <DECORATED>?ClientThread@@YGKPAX@Z</DECORATED>
    <FUNCLINE>323</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>4</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>325</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>326</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>327</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>328</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>329</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>331</LINE>
        <COLUMN>49</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>336</LINE>
        <COLUMN>12</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>341</LINE>
        <COLUMN>59</COLUMN>
        <KEYEVENT>
          <ID>3</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>347</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>348</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>349</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>350</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>351</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>353</LINE>
        <COLUMN>17</COLUMN>
        <KEYEVENT>
          <ID>4</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this branch, (assume 'connection==desktop')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>355</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>356</LINE>
        <COLUMN>9</COLUMN>
        <KEYEVENT>
          <ID>5</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '!client' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>361</LINE>
        <COLUMN>47</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>363</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>364</LINE>
        <COLUMN>31</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>365</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>366</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>367</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>368</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
        <KEYEVENT>
          <ID>6</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Enter this loop, (assume '1')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
        <KEYEVENT>
          <ID>7</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'rect.right==0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
        <KEYEVENT>
          <ID>8</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '((realRight*3))%4' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
        <KEYEVENT>
          <ID>9</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realRight)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
        <KEYEVENT>
          <ID>10</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realBottom)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
        <KEYEVENT>
          <ID>11</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
        <KEYEVENT>
          <ID>12</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '!recvPixels' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
        <KEYEVENT>
          <ID>13</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
        <KEYEVENT>
          <ID>14</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
        <KEYEVENT>
          <ID>15</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
        <KEYEVENT>
          <ID>16</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
        <KEYEVENT>
          <ID>17</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
        <KEYEVENT>
          <ID>18</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'read&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
        <KEYEVENT>
          <ID>19</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this loop, (assume 'totalRead!=size' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
        <KEYEVENT>
          <ID>20</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>450</LINE>
        <COLUMN>19</COLUMN>
        <KEYEVENT>
          <ID>21</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'pixels' is an Input to 'free' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\ucrt\corecrt_malloc.h:89)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>451</LINE>
        <COLUMN>30</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>454</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>455</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>456</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>458</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>459</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>461</LINE>
        <COLUMN>42</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>462</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>463</LINE>
        <COLUMN>39</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>464</LINE>
        <COLUMN>21</COLUMN>
        <KEYEVENT>
          <ID>22</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'pixels' is an Input to 'SetDIBits' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\um\wingdi.h:4682)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>472</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>473</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>474</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>475</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>477</LINE>
        <COLUMN>25</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>479</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>480</LINE>
        <COLUMN>21</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>482</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>484</LINE>
        <COLUMN>26</COLUMN>
        <KEYEVENT>
          <ID>23</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, 0)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
        <KEYEVENT>
          <ID>24</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Continue this loop, (assume '1')</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
        <KEYEVENT>
          <ID>25</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'rect.right==0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
        <KEYEVENT>
          <ID>26</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '((realRight*3))%4' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
        <KEYEVENT>
          <ID>27</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realRight)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
        <KEYEVENT>
          <ID>28</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'SendInt(s, realBottom)&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
        <KEYEVENT>
          <ID>29</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
        <KEYEVENT>
          <ID>30</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '!recvPixels' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
        <KEYEVENT>
          <ID>31</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
        <KEYEVENT>
          <ID>32</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
        <KEYEVENT>
          <ID>33</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
        <KEYEVENT>
          <ID>34</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
        <KEYEVENT>
          <ID>35</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
        <KEYEVENT>
          <ID>36</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume 'read&lt;=0' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
        <KEYEVENT>
          <ID>37</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this loop, (assume 'totalRead!=size' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
        <KEYEVENT>
          <ID>38</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '&lt;branch condition&gt;' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>450</LINE>
        <COLUMN>19</COLUMN>
        <KEYEVENT>
          <ID>39</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'pixels' is an Input to 'free' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\ucrt\corecrt_malloc.h:89)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>451</LINE>
        <COLUMN>30</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>454</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>455</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>456</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>458</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>459</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>461</LINE>
        <COLUMN>42</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>462</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>463</LINE>
        <COLUMN>39</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>464</LINE>
        <COLUMN>21</COLUMN>
        <KEYEVENT>
          <ID>40</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'pixels' is an Input to 'SetDIBits' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\um\wingdi.h:4682)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>464</LINE>
        <COLUMN>21</COLUMN>
        <KEYEVENT>
          <ID>41</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'client-&gt;pixels' should not be NULL, because this is not consistent with the SAL annotation on 'SetDIBits'</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>468</LINE>
      <COLUMN>21</COLUMN>
    </SFA>
    <DEFECTCODE>6001</DEFECTCODE>
    <DESCRIPTION>Using uninitialized memory '*newPixels'.</DESCRIPTION>
    <FUNCTION>ClientThread</FUNCTION>
    <DECORATED>?ClientThread@@YGKPAX@Z</DECORATED>
    <FUNCLINE>323</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>3</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>325</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>326</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>327</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>328</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>329</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>331</LINE>
        <COLUMN>49</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>336</LINE>
        <COLUMN>12</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>341</LINE>
        <COLUMN>59</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>347</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>348</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>349</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>350</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>351</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>353</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>355</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>356</LINE>
        <COLUMN>9</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>361</LINE>
        <COLUMN>47</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>363</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>364</LINE>
        <COLUMN>31</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>365</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>366</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>367</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>368</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>450</LINE>
        <COLUMN>19</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>451</LINE>
        <COLUMN>30</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>454</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>455</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>456</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>458</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>459</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>461</LINE>
        <COLUMN>42</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>462</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>463</LINE>
        <COLUMN>39</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>464</LINE>
        <COLUMN>21</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>472</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>473</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>474</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>475</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>477</LINE>
        <COLUMN>25</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>479</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>480</LINE>
        <COLUMN>21</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>482</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>484</LINE>
        <COLUMN>26</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>370</LINE>
        <COLUMN>6</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>372</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>373</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>375</LINE>
        <COLUMN>23</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>382</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>383</LINE>
        <COLUMN>13</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>385</LINE>
        <COLUMN>28</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>388</LINE>
        <COLUMN>34</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>390</LINE>
        <COLUMN>35</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>393</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>394</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>395</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>396</LINE>
        <COLUMN>14</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>397</LINE>
        <COLUMN>65</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>399</LINE>
        <COLUMN>12</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>404</LINE>
        <COLUMN>83</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>406</LINE>
        <COLUMN>85</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>408</LINE>
        <COLUMN>55</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>410</LINE>
        <COLUMN>57</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>412</LINE>
        <COLUMN>53</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>415</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>416</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>419</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>420</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>422</LINE>
        <COLUMN>22</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>423</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>425</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>427</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>428</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>429</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>430</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>432</LINE>
        <COLUMN>62</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>450</LINE>
        <COLUMN>19</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'*newPixels' is not initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>451</LINE>
        <COLUMN>30</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>454</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>455</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>456</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>458</LINE>
        <COLUMN>17</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>459</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>461</LINE>
        <COLUMN>42</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>462</LINE>
        <COLUMN>38</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>463</LINE>
        <COLUMN>39</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>464</LINE>
        <COLUMN>21</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>472</LINE>
        <COLUMN>20</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>473</LINE>
        <COLUMN>32</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>474</LINE>
        <COLUMN>33</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>475</LINE>
        <COLUMN>27</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>477</LINE>
        <COLUMN>25</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>479</LINE>
        <COLUMN>24</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>480</LINE>
        <COLUMN>21</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>482</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>468</LINE>
        <COLUMN>21</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'*newPixels' is used, but may not have been initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>590</LINE>
      <COLUMN>18</COLUMN>
    </SFA>
    <DEFECTCODE>6246</DEFECTCODE>
    <DESCRIPTION>Local declaration of 'addr' hides declaration of the same name in outer scope. For additional information, see previous declaration at line '561' of 'c:\users\<USER>\onedrive\desktop\hvnc\server\server.cpp'.</DESCRIPTION>
    <FUNCTION>StartServer</FUNCTION>
    <DECORATED>?StartServer@@YAHH@Z</DECORATED>
    <FUNCLINE>557</FUNCLINE>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>561</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Server.cpp</FILENAME>
      <LINE>564</LINE>
      <COLUMN>24</COLUMN>
    </SFA>
    <DEFECTCODE>6387</DEFECTCODE>
    <DESCRIPTION>'ntdll' could be '0':  this does not adhere to the specification for the function 'GetProcAddress'. </DESCRIPTION>
    <FUNCTION>StartServer</FUNCTION>
    <DECORATED>?StartServer@@YAHH@Z</DECORATED>
    <FUNCLINE>557</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>4</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>559</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>560</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>561</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>562</LINE>
        <COLUMN>15</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'ntdll' may be NULL</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>564</LINE>
        <COLUMN>24</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>'ntdll' is an In/Out argument to 'GetProcAddress' (declared at c:\program files (x86)\windows kits\10\include\10.0.26100.0\um\libloaderapi.h:312)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
        <FILENAME>Server.cpp</FILENAME>
        <LINE>564</LINE>
        <COLUMN>24</COLUMN>
        <KEYEVENT>
          <ID>3</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'ntdll' should not be NULL, because this is not consistent with the SAL annotation on 'GetProcAddress'</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
</DEFECTS>