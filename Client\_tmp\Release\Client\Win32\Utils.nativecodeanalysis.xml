<?xml version="1.0" encoding="UTF-8"?>
<DEFECTS>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
      <FILENAME>Utils.cpp</FILENAME>
      <LINE>6</LINE>
      <COLUMN>17</COLUMN>
    </SFA>
    <DEFECTCODE>28182</DEFECTCODE>
    <DESCRIPTION>Dereferencing NULL pointer. 'unEnc' contains the same NULL value as 'LocalAlloc()`5' did. </DESCRIPTION>
    <FUNCTION>UnEnc</FUNCTION>
    <DECORATED>?UnEnc@@YAPADPAD0K@Z</DECORATED>
    <FUNCLINE>3</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>3</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>5</LINE>
        <COLUMN>9</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'unEnc' may be NULL</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>6</LINE>
        <COLUMN>17</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'unEnc' is dereferenced, but may still be NULL</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
      <FILENAME>Utils.cpp</FILENAME>
      <LINE>120</LINE>
      <COLUMN>16</COLUMN>
    </SFA>
    <DEFECTCODE>6001</DEFECTCODE>
    <DESCRIPTION>Using uninitialized memory 'refDomainName'.</DESCRIPTION>
    <FUNCTION>GetUserSidStr</FUNCTION>
    <DECORATED>?GetUserSidStr@@YAHPAPAD@Z</DECORATED>
    <FUNCLINE>93</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>3</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>95</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>96</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>97</LINE>
        <COLUMN>25</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>99</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>100</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>101</LINE>
        <COLUMN>16</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'refDomainName' is not initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>102</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>103</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>104</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>106</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>107</LINE>
        <COLUMN>29</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '*pGetLastError()==122' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>120</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>121</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>122</LINE>
        <COLUMN>3</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>120</LINE>
        <COLUMN>16</COLUMN>
        <KEYEVENT>
          <ID>3</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'refDomainName' is used, but may not have been initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
      <FILENAME>Utils.cpp</FILENAME>
      <LINE>121</LINE>
      <COLUMN>16</COLUMN>
    </SFA>
    <DEFECTCODE>6001</DEFECTCODE>
    <DESCRIPTION>Using uninitialized memory 'sid'.</DESCRIPTION>
    <FUNCTION>GetUserSidStr</FUNCTION>
    <DECORATED>?GetUserSidStr@@YAHPAPAD@Z</DECORATED>
    <FUNCLINE>93</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>3</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>95</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>96</LINE>
        <COLUMN>11</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>97</LINE>
        <COLUMN>25</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>99</LINE>
        <COLUMN>16</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'sid' is not initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>100</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>101</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>102</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>103</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>104</LINE>
        <COLUMN>16</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>106</LINE>
        <COLUMN>29</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>107</LINE>
        <COLUMN>29</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>branch</KIND>
          <IMPORTANCE>Full</IMPORTANCE>
          <MESSAGE>Skip this branch, (assume '*pGetLastError()==122' is false)</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>120</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>121</LINE>
        <COLUMN>15</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>122</LINE>
        <COLUMN>3</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>121</LINE>
        <COLUMN>16</COLUMN>
        <KEYEVENT>
          <ID>3</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'sid' is used, but may not have been initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
      <FILENAME>Utils.cpp</FILENAME>
      <LINE>231</LINE>
      <COLUMN>15</COLUMN>
    </SFA>
    <DEFECTCODE>28251</DEFECTCODE>
    <DESCRIPTION>Inconsistent annotation for 'memset': this instance has no annotations. See c:\program files\microsoft visual studio\2022\community\vc\tools\msvc\14.44.35207\include\vcruntime_string.h(63). </DESCRIPTION>
    <FUNCTION>memset</FUNCTION>
    <DECORATED>memset</DECORATED>
    <FUNCLINE>231</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
      <FILENAME>Utils.cpp</FILENAME>
      <LINE>233</LINE>
      <COLUMN>51</COLUMN>
    </SFA>
    <DEFECTCODE>6001</DEFECTCODE>
    <DESCRIPTION>Using uninitialized memory 'pTarget'.</DESCRIPTION>
    <FUNCTION>memset</FUNCTION>
    <DECORATED>memset</DECORATED>
    <FUNCLINE>231</FUNCLINE>
    <PROBABILITY>1</PROBABILITY>
    <RANK>3</RANK>
    <CATEGORY>
      <RULECATEGORY>mspft</RULECATEGORY>
    </CATEGORY>
    <PATH>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>231</LINE>
        <COLUMN>15</COLUMN>
        <KEYEVENT>
          <ID>1</ID>
          <KIND>declaration</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'pTarget' is not initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>233</LINE>
        <COLUMN>18</COLUMN>
      </SFA>
      <SFA>
        <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\common\</FILEPATH>
        <FILENAME>Utils.cpp</FILENAME>
        <LINE>233</LINE>
        <COLUMN>51</COLUMN>
        <KEYEVENT>
          <ID>2</ID>
          <KIND>usage</KIND>
          <IMPORTANCE>Essential</IMPORTANCE>
          <MESSAGE>'pTarget' is used, but may not have been initialized</MESSAGE>
        </KEYEVENT>
      </SFA>
    </PATH>
  </DEFECT>
</DEFECTS>