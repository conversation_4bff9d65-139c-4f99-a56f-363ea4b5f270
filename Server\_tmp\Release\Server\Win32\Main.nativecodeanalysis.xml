<?xml version="1.0" encoding="UTF-8"?>
<DEFECTS>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Main.cpp</FILENAME>
      <LINE>11</LINE>
      <COLUMN>13</COLUMN>
    </SFA>
    <DEFECTCODE>28251</DEFECTCODE>
    <DESCRIPTION>Inconsistent annotation for 'WinMain': this instance has no annotations. See c:\program files (x86)\windows kits\10\include\10.0.26100.0\um\winbase.h(1062). </DESCRIPTION>
    <FUNCTION>WinMain</FUNCTION>
    <DECORATED>WinMain@16</DECORATED>
    <FUNCLINE>11</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Main.cpp</FILENAME>
      <LINE>18</LINE>
      <COLUMN>10</COLUMN>
    </SFA>
    <DEFECTCODE>6031</DEFECTCODE>
    <DESCRIPTION>Return value ignored: 'freopen'.</DESCRIPTION>
    <FUNCTION>WinMain</FUNCTION>
    <DECORATED>WinMain@16</DECORATED>
    <FUNCLINE>11</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Main.cpp</FILENAME>
      <LINE>19</LINE>
      <COLUMN>10</COLUMN>
    </SFA>
    <DEFECTCODE>6031</DEFECTCODE>
    <DESCRIPTION>Return value ignored: 'freopen'.</DESCRIPTION>
    <FUNCTION>WinMain</FUNCTION>
    <DECORATED>WinMain@16</DECORATED>
    <FUNCLINE>11</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Main.cpp</FILENAME>
      <LINE>20</LINE>
      <COLUMN>10</COLUMN>
    </SFA>
    <DEFECTCODE>6031</DEFECTCODE>
    <DESCRIPTION>Return value ignored: 'freopen'.</DESCRIPTION>
    <FUNCTION>WinMain</FUNCTION>
    <DECORATED>WinMain@16</DECORATED>
    <FUNCLINE>11</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
  <DEFECT>
    <SFA>
      <FILEPATH>C:\Users\<USER>\OneDrive\Desktop\HVNC\Server\</FILEPATH>
      <FILENAME>Main.cpp</FILENAME>
      <LINE>38</LINE>
      <COLUMN>13</COLUMN>
    </SFA>
    <DEFECTCODE>6031</DEFECTCODE>
    <DESCRIPTION>Return value ignored: 'getchar'.</DESCRIPTION>
    <FUNCTION>WinMain</FUNCTION>
    <DECORATED>WinMain@16</DECORATED>
    <FUNCLINE>11</FUNCLINE>
    <PATH></PATH>
  </DEFECT>
</DEFECTS>