@echo off
echo Compiling TestBMPClient...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

cl.exe /EHsc TestRawClient.cpp /Fe:TestBMPClient.exe ws2_32.lib gdi32.lib user32.lib >compile.log 2>&1

if exist TestBMPClient.exe (
    echo TestBMPClient compiled successfully!
    echo.
    echo To run the test client:
    echo   TestBMPClient.exe
    echo.
    echo Make sure HVNCDesign.exe is running first!
) else (
    echo Compilation failed. Check compile.log for details.
    type compile.log
)
