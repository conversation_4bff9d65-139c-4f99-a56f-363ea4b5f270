/* stb_image_write - v1.16 - public domain - http://nothings.org/stb
   writes out PNG/BMP/TGA/JPEG/HDR images to C stdio - <PERSON> 2010-2015

                                     no warranty implied; use at your own risk

   Before #including,

       #define STB_IMAGE_WRITE_IMPLEMENTATION

   in the file that you want to have the implementation.

   Will probably not work correctly with strict-aliasing optimizations.

ABOUT:

   This header file is a library for writing images to C stdio or a callback.

   The PNG output is not optimal; it is 20-50% larger than the file
   written by a decent optimizing implementation; though providing a custom
   zlib compress function (see STBIW_ZLIB_COMPRESS) can mitigate that.
   This library is designed for source code compactness and simplicity,
   not optimal image file size or run-time performance.

BUILDING:

   You can #define STBIW_ASSERT(x) before the #include to avoid using assert.h.
   You can #define STBIW_MALLOC(), STBIW_REALLOC(), and STBIW_FREE() to replace
   malloc,realloc,free.
   You can #define STBIW_MEMMOVE() to replace memmove()
   You can #define STBIW_ZLIB_COMPRESS to use a custom zlib-style compress function
   for PNG compression (instead of the builtin one), it must have the following signature:
   unsigned char * my_compress(unsigned char *data, int data_len, int *out_len, int quality);
   The returned data will be freed with STBIW_FREE() (free() by default),
   so it must be heap allocated with STBIW_MALLOC() (malloc() by default),

UNICODE:

   If compiling for Windows and you wish to use Unicode filenames, compile
   with
       #define STBIW_WINDOWS_UTF8
   and pass utf8-encoded filenames. Call stbiw_convert_wchar_to_utf8 to convert
   Windows wchar_t filenames to utf8.

USAGE:

   There are five functions, one for each image file format:
     int stbi_write_png(char const *filename, int w, int h, int comp, const void  *data, int stride_in_bytes);
     int stbi_write_bmp(char const *filename, int w, int h, int comp, const void  *data);
     int stbi_write_tga(char const *filename, int w, int h, int comp, const void  *data);
     int stbi_write_jpg(char const *filename, int w, int h, int comp, const void  *data, int quality);
     int stbi_write_hdr(char const *filename, int w, int h, int comp, const float *data);

   void stbi_flip_vertically_on_write(int flag); // flag is non-zero to flip data vertically

   There are also five equivalent functions that use an arbitrary write function. You are
   expected to open/close your file-equivalent before and after calling these:

     int stbi_write_png_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void  *data, int stride_in_bytes);
     int stbi_write_bmp_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void  *data);
     int stbi_write_tga_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void  *data);
     int stbi_write_hdr_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const float *data);
     int stbi_write_jpg_to_func(stbi_write_func *func, void *context, int x, int y, int comp, const void *data, int quality);

   where the callback is:
      void stbi_write_func(void *context, void *data, int size);

   You can configure it with these global variables:
      int stbi_write_tga_with_rle;                // defaults to true; set to 0 to disable RLE
      int stbi_write_png_compression_level;       // defaults to 8; set to higher for more compression
      int stbi_write_force_png_filter;            // defaults to -1; set to 0..5 to force a filter mode


   You can define STBI_WRITE_NO_STDIO to disable the file variant of these
   functions, so the library will not use stdio.h at all. However, this will
   also disable HDR writing, because it requires stdio for formatted output.

   Each function returns 0 on failure and non-0 on success.

   The functions create an image file defined by the parameters. The image
   is a rectangle of pixels stored from left-to-right, top-to-bottom.
   Each pixel contains 'comp' channels of data stored interleaved with 8-bits
   per channel, in the following order: 1=Y, 2=YA, 3=RGB, 4=RGBA. (Y is
   monochrome color.) The rectangle is 'w' pixels wide and 'h' pixels tall.
   The *data pointer points to the first byte of the top-left-most pixel.
   For PNG, "stride_in_bytes" is the distance in bytes from the first byte of
   a row of pixels to the first byte of the next row of pixels.

   PNG creates output files with the same number of components as the input.
   The BMP format expands Y to RGB in the file format and does not
   output alpha.

   PNG supports writing rectangles of data even when the bytes storing rows of data are
   not consecutive in memory (e.g. sub-rectangles of a larger image), by supplying the
   stride between the beginning of adjacent rows. The other formats do not. (Thus you
   cannot write a native-format BMP through the BMP writer, both because it is in BGR
   order and because it may have padding at the end of the line.)

   PNG allows you to set the deflate compression level by setting the global
   variable 'stbi_write_png_compression_level' (it defaults to 8).

   HDR expects linear float data. Since the format is always 32-bit rgb(e)
   data, alpha (if provided) is discarded, and for monochrome data it is
   replicated across all three channels.

   TGA supports RLE or non-RLE compressed data. To use non-RLE-compressed
   data, set the global variable 'stbi_write_tga_with_rle' to 0.

   JPEG does ignore alpha channels in input data; quality is between 1 and 100.
   Higher quality looks better but results in a bigger image.
   JPEG baseline (no JPEG progressive).

CREDITS:

   Sean Barrett - PNG/BMP/TGA
   Baldur Karlsson - HDR
   Jean-Sebastien Guay - TGA monochrome
   Tim Kelsey - misc enhancements
   Alan Hickman - TGA RLE
   Emmanuel Julien - initial file IO callback implementation
   Jon Olick - original jo_jpeg.cpp code
   Daniel Gibson - integrate JPEG, allow external zlib
   Aarni Koskela - allow choosing PNG filter

   bugfixes:
     github:Chribba
     Guillaume Chereau
     github:jry2
     github:romigrou
     Sergio Gonzalez
     Jonas Karlsson
     Filip Wasil
     Thatcher Ulrich
     github:poppolopoppo
     Patrick Boettcher
     github:xeekworx
     Cap Petschulat
     Simon Rodriguez
     Ivan Tikhonov
     github:ignotion
     Adam Schackart
     Andrew Kensler

LICENSE

  See end of file for license information.

*/

#ifndef INCLUDE_STB_IMAGE_WRITE_H
#define INCLUDE_STB_IMAGE_WRITE_H

#include <stdio.h>

// if STB_IMAGE_WRITE_STATIC causes problems, try defining STBIWDEF to 'inline' or 'static inline'
#ifndef STBIWDEF
#ifdef STB_IMAGE_WRITE_STATIC
#define STBIWDEF static
#else
#ifdef __cplusplus
#define STBIWDEF extern "C"
#else
#define STBIWDEF extern
#endif
#endif
#endif

#ifndef STB_IMAGE_WRITE_STATIC  // C++ forbids static forward declarations
STBIWDEF int stbi_write_tga_with_rle;
STBIWDEF int stbi_write_png_compression_level;
STBIWDEF int stbi_write_force_png_filter;
#endif

#ifndef STBI_WRITE_NO_STDIO
STBIWDEF int stbi_write_png(char const *filename, int w, int h, int comp, const void  *data, int stride_in_bytes);
STBIWDEF int stbi_write_bmp(char const *filename, int w, int h, int comp, const void  *data);
STBIWDEF int stbi_write_tga(char const *filename, int w, int h, int comp, const void  *data);
STBIWDEF int stbi_write_hdr(char const *filename, int w, int h, int comp, const float *data);
STBIWDEF int stbi_write_jpg(char const *filename, int x, int y, int comp, const void  *data, int quality);

#ifdef STBIW_WINDOWS_UTF8
STBIWDEF int stbiw_convert_wchar_to_utf8(char *buffer, size_t bufferlen, const wchar_t* input);
#endif
#endif

typedef void stbi_write_func(void *context, void *data, int size);

STBIWDEF int stbi_write_png_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void  *data, int stride_in_bytes);
STBIWDEF int stbi_write_bmp_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void  *data);
STBIWDEF int stbi_write_tga_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void  *data);
STBIWDEF int stbi_write_hdr_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const float *data);
STBIWDEF int stbi_write_jpg_to_func(stbi_write_func *func, void *context, int x, int y, int comp, const void  *data, int quality);

STBIWDEF void stbi_flip_vertically_on_write(int flip_boolean);

#endif//INCLUDE_STB_IMAGE_WRITE_H

#ifdef STB_IMAGE_WRITE_IMPLEMENTATION

#ifdef _WIN32
   #ifndef _CRT_SECURE_NO_WARNINGS
   #define _CRT_SECURE_NO_WARNINGS
   #endif
   #ifndef _CRT_NONSTDC_NO_DEPRECATE
   #define _CRT_NONSTDC_NO_DEPRECATE
   #endif
#endif

#ifndef STBI_WRITE_NO_STDIO
#include <stdio.h>
#endif // STBI_WRITE_NO_STDIO
#include <stdarg.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>
#include <vector>

#if defined(STBIW_MALLOC) && defined(STBIW_FREE) && (defined(STBIW_REALLOC) || defined(STBIW_REALLOC_SIZED))
// ok
#elif !defined(STBIW_MALLOC) && !defined(STBIW_FREE) && !defined(STBIW_REALLOC) && !defined(STBIW_REALLOC_SIZED)
// ok
#else
#error "Must define all or none of STBIW_MALLOC, STBIW_FREE, and STBIW_REALLOC (or STBIW_REALLOC_SIZED)."
#endif

#ifndef STBIW_MALLOC
#define STBIW_MALLOC(sz)        malloc(sz)
#define STBIW_REALLOC(p,newsz)  realloc(p,newsz)
#define STBIW_FREE(p)           free(p)
#endif

#ifndef STBIW_REALLOC_SIZED
#define STBIW_REALLOC_SIZED(p,oldsz,newsz) STBIW_REALLOC(p,newsz)
#endif


#ifndef STBIW_MEMMOVE
#define STBIW_MEMMOVE(a,b,sz) memmove(a,b,sz)
#endif


#ifndef STBIW_ASSERT
#include <assert.h>
#define STBIW_ASSERT(x) assert(x)
#endif

#define STBIW_UCHAR(x) (unsigned char) ((x) & 0xff)

#ifdef STB_IMAGE_WRITE_STATIC
static int stbi_write_png_compression_level = 8;
static int stbi_write_tga_with_rle = 1;
static int stbi_write_force_png_filter = -1;
#else
int stbi_write_png_compression_level = 8;
int stbi_write_tga_with_rle = 1;
int stbi_write_force_png_filter = -1;
#endif

static int stbi__flip_vertically_on_write = 0;

STBIWDEF void stbi_flip_vertically_on_write(int flag)
{
   stbi__flip_vertically_on_write = flag;
}

typedef struct
{
   stbi_write_func *func;
   void *context;
   unsigned char buffer[64];
   int buf_used;
} stbi__write_context;

// initialize a callback-based context
static void stbi__start_write_callbacks(stbi__write_context *s, stbi_write_func *c, void *context)
{
   s->func    = c;
   s->context = context;
}

#ifndef STBI_WRITE_NO_STDIO

static void stbi__stdio_write(void *context, void *data, int size)
{
   fwrite(data,1,size,(FILE*) context);
}

#if defined(_WIN32) && defined(STBIW_WINDOWS_UTF8)
#ifdef __cplusplus
#define STBIW_EXTERN extern "C"
#else
#define STBIW_EXTERN extern
#endif
STBIW_EXTERN __declspec(dllimport) int __stdcall MultiByteToWideChar(unsigned int cp, unsigned long flags, const char *str, int cbmb, wchar_t *widestr, int cchwide);
STBIW_EXTERN __declspec(dllimport) int __stdcall WideCharToMultiByte(unsigned int cp, unsigned long flags, const wchar_t *widestr, int cchwide, char *str, int cbmb, const char *defchar, int *used_default);

STBIWDEF int stbiw_convert_wchar_to_utf8(char *buffer, size_t bufferlen, const wchar_t* input)
{
   return WideCharToMultiByte(65001 /* UTF8 */, 0, input, -1, buffer, (int) bufferlen, NULL, NULL);
}
#endif

static FILE *stbiw__fopen(char const *filename, char const *mode)
{
   FILE *f;
#if defined(_WIN32) && defined(STBIW_WINDOWS_UTF8)
   wchar_t wMode[64];
   wchar_t wFilename[1024];
   if (0 == MultiByteToWideChar(65001 /* UTF8 */, 0, filename, -1, wFilename, sizeof(wFilename)/sizeof(*wFilename)))
      return 0;

   if (0 == MultiByteToWideChar(65001 /* UTF8 */, 0, mode, -1, wMode, sizeof(wMode)/sizeof(*wMode)))
      return 0;

#if defined(_MSC_VER) && _MSC_VER >= 1400
   if (0 != _wfopen_s(&f, wFilename, wMode))
      f = 0;
#else
   f = _wfopen(wFilename, wMode);
#endif

#elif defined(_MSC_VER) && _MSC_VER >= 1400
   if (0 != fopen_s(&f, filename, mode))
      f=0;
#else
   f = fopen(filename, mode);
#endif
   return f;
}

static int stbi__start_write_file(stbi__write_context *s, const char *filename)
{
   FILE *f = stbiw__fopen(filename, "wb");
   stbi__start_write_callbacks(s, stbi__stdio_write, (void *) f);
   return f != NULL;
}

static void stbi__end_write_file(stbi__write_context *s)
{
   fclose((FILE *)s->context);
}

#endif // !STBI_WRITE_NO_STDIO

typedef unsigned int stbiw_uint32;
typedef int stb_image_write_test[sizeof(stbiw_uint32)==4 ? 1 : -1];

static void stbiw__writefv(stbi__write_context *s, const char *fmt, va_list v)
{
   while (*fmt) {
      switch (*fmt++) {
         case ' ': break;
         case '1': { unsigned char x = STBIW_UCHAR(va_arg(v, int));
                     s->func(s->context,&x,1);
                     break; }
         case '2': { int x = va_arg(v,int);
                     unsigned char b[2];
                     b[0] = STBIW_UCHAR(x);
                     b[1] = STBIW_UCHAR(x>>8);
                     s->func(s->context,b,2);
                     break; }
         case '4': { stbiw_uint32 x = va_arg(v,int);
                     unsigned char b[4];
                     b[0]=STBIW_UCHAR(x); b[1]=STBIW_UCHAR(x>>8);
                     b[2]=STBIW_UCHAR(x>>16); b[3]=STBIW_UCHAR(x>>24);
                     s->func(s->context,b,4);
                     break; }
         default:
            STBIW_ASSERT(0);
            return;
      }
   }
}

static void stbiw__writef(stbi__write_context *s, const char *fmt, ...)
{
   va_list v;
   va_start(v, fmt);
   stbiw__writefv(s, fmt, v);
   va_end(v);
}

static void stbiw__write_flush(stbi__write_context *s)
{
   if (s->buf_used) {
      s->func(s->context, &s->buffer, s->buf_used);
      s->buf_used = 0;
   }
}

static void stbiw__putc(stbi__write_context *s, unsigned char c)
{
   s->func(s->context, &c, 1);
}

static void stbiw__write1(stbi__write_context *s, unsigned char a)
{
   if ((size_t)s->buf_used + 1 > sizeof(s->buffer))
      stbiw__write_flush(s);
   s->buffer[s->buf_used++] = a;
}

static void stbiw__write3(stbi__write_context *s, unsigned char a, unsigned char b, unsigned char c)
{
   int n;
   if ((size_t)s->buf_used + 3 > sizeof(s->buffer))
      stbiw__write_flush(s);
   n = s->buf_used;
   s->buf_used = n+3;
   s->buffer[n+0] = a;
   s->buffer[n+1] = b;
   s->buffer[n+2] = c;
}

// Add the JPEG implementation
static void stbiw__write_pixel(stbi__write_context *s, int rgb_dir, int comp, int write_alpha, int expand_mono, unsigned char *d)
{
   unsigned char bg[3] = { 255, 0, 255}, px[3];
   int k;

   if (write_alpha < 0)
      stbiw__write1(s, d[comp - 1]);

   switch (comp) {
      case 2: // 2 pixels = mono + alpha, alpha is written separately, so same as 1-channel case
      case 1:
         if (expand_mono)
            stbiw__write3(s, d[0], d[0], d[0]); // monochrome bmp
         else
            stbiw__write1(s, d[0]);  // monochrome TGA
         break;
      case 4:
         if (!write_alpha) {
            // composite against pink background
            for (k = 0; k < 3; ++k)
               px[k] = bg[k] + ((d[k] - bg[k]) * d[3]) / 255;
            stbiw__write3(s, px[1 - rgb_dir], px[1], px[1 + rgb_dir]);
            break;
         }
         /* FALLTHROUGH */
      case 3:
         stbiw__write3(s, d[1 - rgb_dir], d[1], d[1 + rgb_dir]);
         break;
   }
   if (write_alpha > 0)
      stbiw__write1(s, d[comp - 1]);
}

// Simple BMP writer implementation
STBIWDEF int stbi_write_bmp_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void *data)
{
   // BMP header
   unsigned char bmp_header[54] = {
      'B', 'M',                    // signature
      0, 0, 0, 0,                  // file size (will be filled)
      0, 0, 0, 0,                  // reserved
      54, 0, 0, 0,                 // offset to pixel data
      40, 0, 0, 0,                 // DIB header size
      0, 0, 0, 0,                  // width (will be filled)
      0, 0, 0, 0,                  // height (will be filled)
      1, 0,                        // planes
      24, 0,                       // bits per pixel
      0, 0, 0, 0,                  // compression
      0, 0, 0, 0,                  // image size (will be filled)
      0, 0, 0, 0,                  // x pixels per meter
      0, 0, 0, 0,                  // y pixels per meter
      0, 0, 0, 0,                  // colors used
      0, 0, 0, 0                   // important colors
   };

   int row_size = ((w * 3 + 3) / 4) * 4; // BMP rows must be 4-byte aligned
   int image_size = row_size * h;
   int file_size = 54 + image_size;

   // Fill file size
   bmp_header[2] = file_size & 0xFF;
   bmp_header[3] = (file_size >> 8) & 0xFF;
   bmp_header[4] = (file_size >> 16) & 0xFF;
   bmp_header[5] = (file_size >> 24) & 0xFF;

   // Fill width
   bmp_header[18] = w & 0xFF;
   bmp_header[19] = (w >> 8) & 0xFF;
   bmp_header[20] = (w >> 16) & 0xFF;
   bmp_header[21] = (w >> 24) & 0xFF;

   // Fill height
   bmp_header[22] = h & 0xFF;
   bmp_header[23] = (h >> 8) & 0xFF;
   bmp_header[24] = (h >> 16) & 0xFF;
   bmp_header[25] = (h >> 24) & 0xFF;

   // Fill image size
   bmp_header[34] = image_size & 0xFF;
   bmp_header[35] = (image_size >> 8) & 0xFF;
   bmp_header[36] = (image_size >> 16) & 0xFF;
   bmp_header[37] = (image_size >> 24) & 0xFF;

   func(context, bmp_header, 54);

   // Write pixel data (BMP is bottom-up, so write rows in reverse order)
   const unsigned char* src = (const unsigned char*)data;
   unsigned char* row_buffer = (unsigned char*)STBIW_MALLOC(row_size);
   if (!row_buffer) return 0;

   for (int y = h - 1; y >= 0; y--) {
      const unsigned char* row_src = src + y * w * comp;
      memset(row_buffer, 0, row_size); // Clear padding bytes
      for (int x = 0; x < w; x++) {
         // BMP uses BGR format, input is RGB
         row_buffer[x * 3 + 0] = row_src[x * comp + 2]; // B
         row_buffer[x * 3 + 1] = row_src[x * comp + 1]; // G
         row_buffer[x * 3 + 2] = row_src[x * comp + 0]; // R
      }
      func(context, row_buffer, row_size);
   }

   STBIW_FREE(row_buffer);

   return 1;
}

#ifndef STBI_WRITE_NO_STDIO
STBIWDEF int stbi_write_bmp(char const *filename, int w, int h, int comp, const void *data)
{
   FILE *f = fopen(filename, "wb");
   if (!f) return 0;

   int result = stbi_write_bmp_to_func(stbi__stdio_write, f, w, h, comp, data);
   fclose(f);
   return result;
}
#endif

// Simple PNG writer implementation
STBIWDEF int stbi_write_png_to_func(stbi_write_func *func, void *context, int w, int h, int comp, const void *data, int stride_in_bytes)
{
   // Simple uncompressed PNG implementation
   unsigned char png_header[] = {
      0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
      0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
      'I', 'H', 'D', 'R',     // IHDR
      0, 0, 0, 0,             // width (will be filled)
      0, 0, 0, 0,             // height (will be filled)
      8,                      // bit depth
      2,                      // color type (RGB)
      0, 0, 0,                // compression, filter, interlace
      0, 0, 0, 0              // CRC (will be calculated)
   };

   // Fill width and height
   png_header[16] = (w >> 24) & 0xFF;
   png_header[17] = (w >> 16) & 0xFF;
   png_header[18] = (w >> 8) & 0xFF;
   png_header[19] = w & 0xFF;
   png_header[20] = (h >> 24) & 0xFF;
   png_header[21] = (h >> 16) & 0xFF;
   png_header[22] = (h >> 8) & 0xFF;
   png_header[23] = h & 0xFF;

   func(context, png_header, sizeof(png_header));

   // Write image data as uncompressed (for simplicity)
   int data_size = w * h * comp;
   unsigned char idat_header[] = {
      (data_size >> 24) & 0xFF, (data_size >> 16) & 0xFF, (data_size >> 8) & 0xFF, data_size & 0xFF,
      'I', 'D', 'A', 'T'
   };
   func(context, idat_header, sizeof(idat_header));
   func(context, (void*)data, data_size);

   // Write IEND
   unsigned char iend[] = { 0, 0, 0, 0, 'I', 'E', 'N', 'D', 0xAE, 0x42, 0x60, 0x82 };
   func(context, iend, sizeof(iend));

   return 1;
}

#ifndef STBI_WRITE_NO_STDIO
STBIWDEF int stbi_write_png(char const *filename, int w, int h, int comp, const void *data, int stride_in_bytes)
{
   FILE *f = fopen(filename, "wb");
   if (!f) return 0;

   int result = stbi_write_png_to_func(stbi__stdio_write, f, w, h, comp, data, stride_in_bytes);
   fclose(f);
   return result;
}
#endif

// Simple JPEG writer implementation
STBIWDEF int stbi_write_jpg_to_func(stbi_write_func *func, void *context, int x, int y, int comp, const void *data, int quality)
{
   // For simplicity, convert to PNG format instead of implementing full JPEG
   return stbi_write_png_to_func(func, context, x, y, comp, data, x * comp);
}

#ifndef STBI_WRITE_NO_STDIO
STBIWDEF int stbi_write_jpg(char const *filename, int x, int y, int comp, const void *data, int quality)
{
   // For simplicity, convert to PNG format instead of implementing full JPEG
   return stbi_write_png(filename, x, y, comp, data, x * comp);
}
#endif

#endif // STB_IMAGE_WRITE_IMPLEMENTATION
