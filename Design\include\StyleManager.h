/**
 * @file StyleManager.h
 * @brief Style management for modern dark theme
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This class manages the application's visual styling, providing
 * a modern, professional dark theme with proper spacing, colors,
 * and typography. It handles theme switching and custom styling
 * for various UI components.
 */

#pragma once

#include "Common.h"

/**
 * @class StyleManager
 * @brief Manages application styling and themes
 * 
 * The StyleManager class provides comprehensive styling capabilities
 * for the HVNC Design application. It implements a modern dark theme
 * with professional appearance, proper contrast ratios, and consistent
 * visual hierarchy throughout the application.
 */
class StyleManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Theme enumeration
     */
    enum class Theme {
        Dark,
        Light,
        Auto  // Follow system theme
    };

    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit StyleManager(QObject* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~StyleManager() override = default;

    /**
     * @brief Apply dark theme to application
     * 
     * Applies a comprehensive dark theme with modern styling
     * to all application components.
     */
    void applyDarkTheme();

    /**
     * @brief Apply light theme to application
     * 
     * Applies a light theme variant (for future use).
     */
    void applyLightTheme();

    /**
     * @brief Set current theme
     * @param theme Theme to apply
     */
    void setTheme(Theme theme);

    /**
     * @brief Get current theme
     * @return Current theme
     */
    Theme getCurrentTheme() const { return m_currentTheme; }

    /**
     * @brief Get main window stylesheet
     * @return CSS stylesheet for main window
     */
    QString getMainWindowStyleSheet() const;

    /**
     * @brief Get desktop viewer stylesheet
     * @return CSS stylesheet for desktop viewer
     */
    QString getDesktopViewerStyleSheet() const;

    /**
     * @brief Get control panel stylesheet
     * @return CSS stylesheet for control panel
     */
    QString getControlPanelStyleSheet() const;

    /**
     * @brief Get button stylesheet
     * @param primary True for primary button style
     * @return CSS stylesheet for buttons
     */
    QString getButtonStyleSheet(bool primary = false) const;

    /**
     * @brief Get input field stylesheet
     * @return CSS stylesheet for input fields
     */
    QString getInputFieldStyleSheet() const;

    /**
     * @brief Get toolbar stylesheet
     * @return CSS stylesheet for toolbars
     */
    QString getToolbarStyleSheet() const;

    /**
     * @brief Get status bar stylesheet
     * @return CSS stylesheet for status bar
     */
    QString getStatusBarStyleSheet() const;

    /**
     * @brief Get menu stylesheet
     * @return CSS stylesheet for menus
     */
    QString getMenuStyleSheet() const;

    /**
     * @brief Get tab widget stylesheet
     * @return CSS stylesheet for tab widgets
     */
    QString getTabWidgetStyleSheet() const;

    /**
     * @brief Get scroll area stylesheet
     * @return CSS stylesheet for scroll areas
     */
    QString getScrollAreaStyleSheet() const;

    /**
     * @brief Get group box stylesheet
     * @return CSS stylesheet for group boxes
     */
    QString getGroupBoxStyleSheet() const;

    /**
     * @brief Apply custom styling to widget
     * @param widget Widget to style
     * @param styleClass Style class name
     */
    void applyCustomStyle(QWidget* widget, const QString& styleClass);

    /**
     * @brief Create styled separator line
     * @param orientation Line orientation
     * @return Styled separator widget
     */
    QFrame* createSeparator(Qt::Orientation orientation = Qt::Horizontal);

    /**
     * @brief Create styled group box
     * @param title Group box title
     * @return Styled group box
     */
    QGroupBox* createGroupBox(const QString& title);

    /**
     * @brief Create styled push button
     * @param text Button text
     * @param primary True for primary button style
     * @return Styled push button
     */
    QPushButton* createButton(const QString& text, bool primary = false);

    /**
     * @brief Create styled tool button
     * @param icon Button icon
     * @param tooltip Button tooltip
     * @return Styled tool button
     */
    QToolButton* createToolButton(const QIcon& icon, const QString& tooltip);

    /**
     * @brief Create styled line edit
     * @param placeholder Placeholder text
     * @return Styled line edit
     */
    QLineEdit* createLineEdit(const QString& placeholder = QString());

    /**
     * @brief Create styled label
     * @param text Label text
     * @param styleClass Style class (title, subtitle, body, caption)
     * @return Styled label
     */
    QLabel* createLabel(const QString& text, const QString& styleClass = "body");

signals:
    /**
     * @brief Emitted when theme changes
     * @param theme New theme
     */
    void themeChanged(Theme theme);

private slots:
    /**
     * @brief Handle system theme change
     */
    void onSystemThemeChanged();

private:
    /**
     * @brief Initialize style manager
     */
    void initialize();

    /**
     * @brief Setup application palette
     */
    void setupPalette();

    /**
     * @brief Setup application fonts
     */
    void setupFonts();

    /**
     * @brief Get base stylesheet
     * @return Base CSS stylesheet
     */
    QString getBaseStyleSheet() const;

    /**
     * @brief Get color value by name
     * @param colorName Color name from Colors namespace
     * @return Color value as string
     */
    QString getColor(const QString& colorName) const;

    /**
     * @brief Get font specification
     * @param size Font size
     * @param weight Font weight
     * @return Font specification string
     */
    QString getFont(int size, int weight = QFont::Normal) const;

private:
    Theme m_currentTheme;                    ///< Current theme
    QMap<QString, QString> m_colorMap;       ///< Color mapping
    QMap<QString, QString> m_styleCache;     ///< Style cache for performance
    bool m_initialized;                      ///< Initialization flag
};

/**
 * @brief Global style manager instance
 * @return Reference to global StyleManager instance
 */
StyleManager& getStyleManager();
