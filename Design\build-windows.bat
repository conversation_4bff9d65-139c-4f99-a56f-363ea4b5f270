@echo off
:: ============================================================================
:: HVNC Design Windows Build Script
:: ============================================================================
:: Builds and runs the HVNC Design GUI for Windows
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC Design Windows Build Script                         ║%NC%
echo %BLUE%║                    Professional Qt6 GUI for Windows                         ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Setup Qt6 environment
echo %CYAN%[STEP 1]%NC% Setting up Qt6 environment...

set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64\bin"
set "PATH=%QT6_PATH%;%PATH%"

if not exist "%QT6_PATH%\qmake.exe" (
    echo %RED%[ERROR]%NC% Qt6 not found at %QT6_PATH%
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Qt6 found at %QT6_PATH%

:: Step 2: Setup Visual Studio environment
echo %CYAN%[STEP 2]%NC% Setting up Visual Studio environment...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Visual Studio 2022 not found, trying without environment
) else (
    echo %GREEN%[SUCCESS]%NC% Visual Studio 2022 environment loaded
)

:: Step 3: Clean previous builds
echo %CYAN%[STEP 3]%NC% Cleaning previous builds...

if exist "windows" rmdir /s /q "windows" 2>nul
if exist "Makefile*" del /q "Makefile*" 2>nul
if exist "*.exe" del /q "*.exe" 2>nul

mkdir "windows\release" 2>nul
mkdir "windows\debug" 2>nul

echo %GREEN%[CLEAN]%NC% Build directories prepared

:: Step 4: Generate Makefile with qmake
echo %CYAN%[STEP 4]%NC% Generating Makefile with qmake...

:: Set environment to avoid MSVC version issues
set "QMAKE_MSC_VER=1920"

qmake HVNCDesign.pro "CONFIG+=release"

if errorlevel 1 (
    echo %RED%[FAILED]%NC% qmake failed
    echo %YELLOW%[INFO]%NC% This might be due to Qt6/MSVC compatibility issues
    echo %YELLOW%[SUGGESTION]%NC% Try using the CMake build instead
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Makefile generated successfully

:: Step 5: Build the application
echo %CYAN%[STEP 5]%NC% Building HVNC Design GUI...

nmake release

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Build failed
    echo %YELLOW%[DEBUG]%NC% Checking for alternative build methods...
    
    :: Try with make instead of nmake
    make release
    
    if errorlevel 1 (
        echo %RED%[FAILED]%NC% All build methods failed
        pause
        exit /b 1
    )
)

echo %GREEN%[SUCCESS]%NC% Build completed successfully!

:: Step 6: Locate and deploy executable
echo %CYAN%[STEP 6]%NC% Locating executable...

set "EXE_FOUND=false"
set "EXE_PATH="

if exist "windows\release\HVNCDesign.exe" (
    set "EXE_PATH=windows\release\HVNCDesign.exe"
    set "EXE_FOUND=true"
) else if exist "release\HVNCDesign.exe" (
    set "EXE_PATH=release\HVNCDesign.exe"
    set "EXE_FOUND=true"
) else if exist "HVNCDesign.exe" (
    set "EXE_PATH=HVNCDesign.exe"
    set "EXE_FOUND=true"
)

if "%EXE_FOUND%"=="true" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe found at: %EXE_PATH%
    
    :: Copy to root directory for easy access
    copy "%EXE_PATH%" "..\HVNCDesign.exe" >nul
    
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
    )
    
    :: Get file info
    for %%F in ("%EXE_PATH%") do (
        echo %CYAN%[INFO]%NC% File size: %%~zF bytes
        echo %CYAN%[INFO]%NC% Build location: %%~fF
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found after build
    echo %YELLOW%[DEBUG]%NC% Checking all directories for executable...
    dir /s "*.exe" 2>nul | findstr HVNCDesign
    pause
    exit /b 1
)

:: Success summary
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        WINDOWS BUILD SUCCESSFUL!                            ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Application:%NC%
echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for Windows

echo.
echo %CYAN%Usage Instructions:%NC%
echo   %WHITE%1.%NC% Run HVNCDesign.exe to open the Windows Qt6 GUI
echo   %WHITE%2.%NC% Use the interface to configure HVNC server settings
echo   %WHITE%3.%NC% Start the HVNC server from the GUI
echo   %WHITE%4.%NC% Deploy Client.exe to target computers
echo   %WHITE%5.%NC% Remote desktop images will appear in the GUI viewer

echo.
echo %YELLOW%[ARCHITECTURE]%NC% 
echo   • HVNCDesign.exe: Qt6 GUI controller (YOUR computer)
echo   • Client.exe: Hidden agent (TARGET computer - captures desktop)
echo   • Data flow: Client captures → GUI displays (CORRECT!)

echo.
echo %CYAN%[TEST]%NC% Would you like to test launch the GUI now? (y/n)
set /p "test_launch=Enter choice: "

if /i "!test_launch!"=="y" (
    echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe...
    
    if exist "..\HVNCDesign.exe" (
        start "" "..\HVNCDesign.exe"
        echo %GREEN%[SUCCESS]%NC% GUI launched from root directory!
    ) else if exist "%EXE_PATH%" (
        start "" "%EXE_PATH%"
        echo %GREEN%[SUCCESS]%NC% GUI launched from build directory!
    ) else (
        echo %RED%[ERROR]%NC% Executable not found for launch
    )
)

echo.
echo %GREEN%[COMPLETE]%NC% Windows Design build finished!
pause
