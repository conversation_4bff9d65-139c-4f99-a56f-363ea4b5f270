@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    HVNC Remote Connection Test
echo ========================================
echo.

:: Get current client configuration
echo [94m[INFO][0m Current client configuration:
findstr /n "host.*=" Client\Main.cpp
findstr /n "port.*=" Client\Main.cpp
echo.

:: Extract IP and port from client config
for /f "tokens=2 delims==" %%a in ('findstr "host.*=" Client\Main.cpp') do (
    set "host_line=%%a"
    set "host_line=!host_line: =!"
    set "host_line=!host_line:"=!"
    set "host_line=!host_line:;=!"
    set "SERVER_IP=!host_line!"
)

for /f "tokens=2 delims=(" %%a in ('findstr "strtol.*port" Client\Main.cpp') do (
    set "port_line=%%a"
    set "port_line=!port_line:"=!"
    set "port_line=!port_line:,=!"
    set "SERVER_PORT=!port_line!"
)

echo [93mConfigured Server IP:[0m %SERVER_IP%
echo [93mConfigured Server Port:[0m %SERVER_PORT%
echo.

echo ========================================
echo    Network Connectivity Test
echo ========================================

if "%SERVER_IP%"=="127.0.0.1" (
    echo [93m[WARNING][0m Client is configured for localhost only!
    echo To test remote connection, modify Client\Main.cpp with server's IP address.
    echo.
) else (
    echo [94m[INFO][0m Testing connection to %SERVER_IP%:%SERVER_PORT%...
    echo.
    
    :: Test ping
    echo [94m[STEP 1][0m Testing ping to %SERVER_IP%...
    ping -n 2 %SERVER_IP% >nul 2>&1
    if !errorlevel! equ 0 (
        echo [92m[SUCCESS][0m Server is reachable via ping
    ) else (
        echo [91m[ERROR][0m Server is not reachable via ping
        echo Check if the server computer is online and firewall allows ICMP
    )
    echo.
    
    :: Test port connectivity
    echo [94m[STEP 2][0m Testing port %SERVER_PORT% connectivity...
    powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('%SERVER_IP%', %SERVER_PORT%); $tcp.Close(); Write-Host '[SUCCESS] Port %SERVER_PORT% is open and accepting connections' -ForegroundColor Green } catch { Write-Host '[ERROR] Port %SERVER_PORT% is not accessible' -ForegroundColor Red; Write-Host 'Make sure the HVNC server is running on %SERVER_IP%:%SERVER_PORT%' -ForegroundColor Yellow }"
    echo.
)

echo ========================================
echo    Setup Instructions
echo ========================================
echo.
echo [94m[SERVER SETUP][0m (On the server computer):
echo 1. Copy Server.exe to the server computer
echo 2. Run Server.exe and note the port it listens on
echo 3. Configure Windows Firewall to allow the port:
echo    - Open Windows Defender Firewall
echo    - Click "Allow an app or feature through Windows Defender Firewall"
echo    - Click "Allow another app..." and browse to Server.exe
echo    - OR create a new inbound rule for port %SERVER_PORT%
echo.
echo [94m[CLIENT SETUP][0m (On the client computer):
echo 1. Copy Client.exe to the client computer
echo 2. Make sure Client\Main.cpp is configured with correct server IP
echo 3. Rebuild the client if IP was changed
echo 4. Run Client.exe
echo.
echo [94m[NETWORK REQUIREMENTS][0m:
echo - Both computers must be on the same network OR
echo - Router must be configured for port forwarding (for internet connections)
echo - Server firewall must allow incoming connections on port %SERVER_PORT%
echo - Client firewall must allow outgoing connections
echo.

echo ========================================
echo    Quick Commands
echo ========================================
echo.
echo [93mTo rebuild client after IP change:[0m
echo MSBuild Client\HVNC.vcxproj /p:Configuration=Release /p:Platform=Win32
echo.
echo [93mTo start server:[0m
echo _bin\Release\Win32\Server.exe
echo.
echo [93mTo start client:[0m
echo Client\_bin\Release\Win32\Client.exe
echo.

pause
