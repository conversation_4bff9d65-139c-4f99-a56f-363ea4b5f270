/**
 * @file SettingsDialog.cpp
 * @brief Implementation of professional HVNC Settings Dialog
 */

#include "SettingsDialog.h"

#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QTabWidget>
#include <QGroupBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QComboBox>
#include <QCheckBox>
#include <QPushButton>
#include <QLabel>
#include <QSlider>
#include <QButtonGroup>
#include <QRadioButton>
#include <QMessageBox>
#include <QSettings>
#include <QApplication>
#include <QDialogButtonBox>
#include <QFrame>

SettingsDialog::SettingsDialog(QWidget* parent)
    : QDialog(parent)
    , m_tabWidget(nullptr)
    , m_portSpinBox(nullptr)
    , m_passwordLineEdit(nullptr)
    , m_maxConnectionsSpinBox(nullptr)
    , m_autoStartCheckBox(nullptr)
    , m_testConnectionButton(nullptr)
    , m_enableEncryptionCheckBox(nullptr)
    , m_encryptionMethodComboBox(nullptr)
    , m_encryptionKeyLineEdit(nullptr)
    , m_requireAuthCheckBox(nullptr)
    , m_sessionTimeoutSpinBox(nullptr)
    , m_compressionSlider(nullptr)
    , m_compressionLabel(nullptr)
    , m_updateIntervalSpinBox(nullptr)
    , m_imageQualityComboBox(nullptr)
    , m_bufferSizeSpinBox(nullptr)
    , m_enableMultithreadingCheckBox(nullptr)
    , m_enableLoggingCheckBox(nullptr)
    , m_logLevelComboBox(nullptr)
    , m_showControlPanelCheckBox(nullptr)
    , m_showToolbarCheckBox(nullptr)
    , m_showStatusBarCheckBox(nullptr)
    , m_themeComboBox(nullptr)
    , m_okButton(nullptr)
    , m_applyButton(nullptr)
    , m_cancelButton(nullptr)
    , m_resetButton(nullptr)
{
    setWindowTitle("HVNC Settings - Professional Configuration");
    setWindowIcon(QIcon(":/icons/settings.png"));
    setModal(true);
    resize(600, 500);
    
    initializeUI();
    applyProfessionalStyling();
    loadSettings();
}

SettingsDialog::~SettingsDialog() = default;

void SettingsDialog::initializeUI()
{
    auto* mainLayout = new QVBoxLayout(this);
    mainLayout->setContentsMargins(15, 15, 15, 15);
    mainLayout->setSpacing(10);

    // Create tab widget
    m_tabWidget = new QTabWidget(this);
    m_tabWidget->addTab(createServerSettingsTab(), "Server Configuration");
    m_tabWidget->addTab(createSecuritySettingsTab(), "Security & Authentication");
    m_tabWidget->addTab(createPerformanceSettingsTab(), "Performance & Quality");
    m_tabWidget->addTab(createInterfaceSettingsTab(), "Interface & Logging");

    mainLayout->addWidget(m_tabWidget);

    // Create button box
    auto* buttonLayout = new QHBoxLayout();
    
    m_resetButton = new QPushButton("Reset to Defaults");
    m_resetButton->setIcon(QIcon(":/icons/reset.png"));
    
    buttonLayout->addWidget(m_resetButton);
    buttonLayout->addStretch();
    
    m_okButton = new QPushButton("OK");
    m_okButton->setIcon(QIcon(":/icons/ok.png"));
    m_okButton->setDefault(true);
    
    m_applyButton = new QPushButton("Apply");
    m_applyButton->setIcon(QIcon(":/icons/apply.png"));
    
    m_cancelButton = new QPushButton("Cancel");
    m_cancelButton->setIcon(QIcon(":/icons/cancel.png"));
    
    buttonLayout->addWidget(m_okButton);
    buttonLayout->addWidget(m_applyButton);
    buttonLayout->addWidget(m_cancelButton);

    mainLayout->addLayout(buttonLayout);

    // Connect signals
    connect(m_okButton, &QPushButton::clicked, this, &SettingsDialog::onOkClicked);
    connect(m_applyButton, &QPushButton::clicked, this, &SettingsDialog::onApplyClicked);
    connect(m_cancelButton, &QPushButton::clicked, this, &SettingsDialog::onCancelClicked);
    connect(m_resetButton, &QPushButton::clicked, this, &SettingsDialog::onResetClicked);
}

QWidget* SettingsDialog::createServerSettingsTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(20, 20, 20, 20);
    layout->setSpacing(15);

    // Network Configuration Group
    auto* networkGroup = new QGroupBox("Network Configuration");
    auto* networkLayout = new QGridLayout(networkGroup);
    networkLayout->setSpacing(10);

    // Server Port
    auto* portLabel = new QLabel("Server Port:");
    portLabel->setToolTip("TCP port for HVNC server (1024-65535)");
    m_portSpinBox = new QSpinBox();
    m_portSpinBox->setRange(1024, 65535);
    m_portSpinBox->setValue(4444);
    m_portSpinBox->setSuffix(" (TCP)");
    m_portSpinBox->setToolTip("Port number for incoming client connections");
    
    networkLayout->addWidget(portLabel, 0, 0);
    networkLayout->addWidget(m_portSpinBox, 0, 1);

    // Server Password
    auto* passwordLabel = new QLabel("Server Password:");
    passwordLabel->setToolTip("Password required for client connections");
    m_passwordLineEdit = new QLineEdit();
    m_passwordLineEdit->setEchoMode(QLineEdit::Password);
    m_passwordLineEdit->setText("admin");
    m_passwordLineEdit->setPlaceholderText("Enter secure password");
    m_passwordLineEdit->setToolTip("Strong password recommended (8+ characters)");
    
    networkLayout->addWidget(passwordLabel, 1, 0);
    networkLayout->addWidget(m_passwordLineEdit, 1, 1);

    // Max Connections
    auto* maxConnLabel = new QLabel("Max Connections:");
    maxConnLabel->setToolTip("Maximum number of simultaneous client connections");
    m_maxConnectionsSpinBox = new QSpinBox();
    m_maxConnectionsSpinBox->setRange(1, 100);
    m_maxConnectionsSpinBox->setValue(10);
    m_maxConnectionsSpinBox->setSuffix(" clients");
    m_maxConnectionsSpinBox->setToolTip("Higher values may impact performance");
    
    networkLayout->addWidget(maxConnLabel, 2, 0);
    networkLayout->addWidget(m_maxConnectionsSpinBox, 2, 1);

    layout->addWidget(networkGroup);

    // Server Control Group
    auto* controlGroup = new QGroupBox("Server Control");
    auto* controlLayout = new QVBoxLayout(controlGroup);

    m_autoStartCheckBox = new QCheckBox("Auto-start server when application launches");
    m_autoStartCheckBox->setToolTip("Automatically start HVNC server on application startup");
    controlLayout->addWidget(m_autoStartCheckBox);

    // Test Connection Button
    m_testConnectionButton = new QPushButton("Test Server Configuration");
    m_testConnectionButton->setIcon(QIcon(":/icons/test.png"));
    m_testConnectionButton->setToolTip("Test if the server can bind to the specified port");
    connect(m_testConnectionButton, &QPushButton::clicked, this, &SettingsDialog::onTestConnection);
    controlLayout->addWidget(m_testConnectionButton);

    layout->addWidget(controlGroup);

    // Information Group
    auto* infoGroup = new QGroupBox("Server Information");
    auto* infoLayout = new QVBoxLayout(infoGroup);
    
    auto* infoLabel = new QLabel(
        "<b>HVNC Server Architecture:</b><br>"
        "• <b>Server (This Computer):</b> Runs HVNCDesign.exe with GUI interface<br>"
        "• <b>Client (Target Computer):</b> Runs Client.exe hidden in background<br>"
        "• <b>Data Flow:</b> Client captures desktop → Server displays in GUI<br><br>"
        "<b>Security Notes:</b><br>"
        "• Use strong passwords for production environments<br>"
        "• Consider enabling encryption for sensitive data<br>"
        "• Monitor active connections regularly"
    );
    infoLabel->setWordWrap(true);
    infoLabel->setStyleSheet("QLabel { color: #2c3e50; background-color: #ecf0f1; padding: 10px; border-radius: 5px; }");
    infoLayout->addWidget(infoLabel);

    layout->addWidget(infoGroup);
    layout->addStretch();

    return widget;
}

QWidget* SettingsDialog::createSecuritySettingsTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(20, 20, 20, 20);
    layout->setSpacing(15);

    // Encryption Group
    auto* encryptionGroup = new QGroupBox("Data Encryption");
    auto* encryptionLayout = new QGridLayout(encryptionGroup);

    m_enableEncryptionCheckBox = new QCheckBox("Enable AES Encryption");
    m_enableEncryptionCheckBox->setChecked(true);
    m_enableEncryptionCheckBox->setToolTip("Encrypt all data transmitted between server and clients");
    encryptionLayout->addWidget(m_enableEncryptionCheckBox, 0, 0, 1, 2);

    auto* methodLabel = new QLabel("Encryption Method:");
    m_encryptionMethodComboBox = new QComboBox();
    m_encryptionMethodComboBox->addItems({"AES-256-CBC", "AES-192-CBC", "AES-128-CBC"});
    m_encryptionMethodComboBox->setCurrentIndex(0);
    encryptionLayout->addWidget(methodLabel, 1, 0);
    encryptionLayout->addWidget(m_encryptionMethodComboBox, 1, 1);

    layout->addWidget(encryptionGroup);

    // Authentication Group
    auto* authGroup = new QGroupBox("Authentication & Access Control");
    auto* authLayout = new QGridLayout(authGroup);

    m_requireAuthCheckBox = new QCheckBox("Require client authentication");
    m_requireAuthCheckBox->setChecked(true);
    authLayout->addWidget(m_requireAuthCheckBox, 0, 0, 1, 2);

    auto* timeoutLabel = new QLabel("Session Timeout:");
    m_sessionTimeoutSpinBox = new QSpinBox();
    m_sessionTimeoutSpinBox->setRange(5, 1440);
    m_sessionTimeoutSpinBox->setValue(60);
    m_sessionTimeoutSpinBox->setSuffix(" minutes");
    authLayout->addWidget(timeoutLabel, 1, 0);
    authLayout->addWidget(m_sessionTimeoutSpinBox, 1, 1);

    layout->addWidget(authGroup);
    layout->addStretch();

    return widget;
}

QWidget* SettingsDialog::createPerformanceSettingsTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(20, 20, 20, 20);
    layout->setSpacing(15);

    // Compression Group
    auto* compressionGroup = new QGroupBox("Image Compression");
    auto* compressionLayout = new QGridLayout(compressionGroup);

    auto* compLabel = new QLabel("Compression Level:");
    m_compressionSlider = new QSlider(Qt::Horizontal);
    m_compressionSlider->setRange(0, 9);
    m_compressionSlider->setValue(6);
    m_compressionLabel = new QLabel("6 (Balanced)");

    compressionLayout->addWidget(compLabel, 0, 0);
    compressionLayout->addWidget(m_compressionSlider, 0, 1);
    compressionLayout->addWidget(m_compressionLabel, 0, 2);

    connect(m_compressionSlider, &QSlider::valueChanged, this, &SettingsDialog::onCompressionChanged);

    layout->addWidget(compressionGroup);

    // Quality Group
    auto* qualityGroup = new QGroupBox("Image Quality & Performance");
    auto* qualityLayout = new QGridLayout(qualityGroup);

    auto* qualityLabel = new QLabel("Image Quality:");
    m_imageQualityComboBox = new QComboBox();
    m_imageQualityComboBox->addItems({"Low (Fast)", "Medium", "High", "Lossless"});
    m_imageQualityComboBox->setCurrentIndex(1);
    qualityLayout->addWidget(qualityLabel, 0, 0);
    qualityLayout->addWidget(m_imageQualityComboBox, 0, 1);

    auto* intervalLabel = new QLabel("Update Interval:");
    m_updateIntervalSpinBox = new QSpinBox();
    m_updateIntervalSpinBox->setRange(10, 1000);
    m_updateIntervalSpinBox->setValue(50);
    m_updateIntervalSpinBox->setSuffix(" ms");
    qualityLayout->addWidget(intervalLabel, 1, 0);
    qualityLayout->addWidget(m_updateIntervalSpinBox, 1, 1);

    auto* bufferLabel = new QLabel("Buffer Size:");
    m_bufferSizeSpinBox = new QSpinBox();
    m_bufferSizeSpinBox->setRange(1, 64);
    m_bufferSizeSpinBox->setValue(8);
    m_bufferSizeSpinBox->setSuffix(" MB");
    qualityLayout->addWidget(bufferLabel, 2, 0);
    qualityLayout->addWidget(m_bufferSizeSpinBox, 2, 1);

    m_enableMultithreadingCheckBox = new QCheckBox("Enable multi-threading for better performance");
    m_enableMultithreadingCheckBox->setChecked(true);
    qualityLayout->addWidget(m_enableMultithreadingCheckBox, 3, 0, 1, 2);

    layout->addWidget(qualityGroup);
    layout->addStretch();

    return widget;
}

QWidget* SettingsDialog::createInterfaceSettingsTab()
{
    auto* widget = new QWidget();
    auto* layout = new QVBoxLayout(widget);
    layout->setContentsMargins(20, 20, 20, 20);
    layout->setSpacing(15);

    // Logging Group
    auto* loggingGroup = new QGroupBox("Logging & Debugging");
    auto* loggingLayout = new QGridLayout(loggingGroup);

    m_enableLoggingCheckBox = new QCheckBox("Enable detailed logging");
    m_enableLoggingCheckBox->setChecked(true);
    loggingLayout->addWidget(m_enableLoggingCheckBox, 0, 0, 1, 2);

    auto* logLevelLabel = new QLabel("Log Level:");
    m_logLevelComboBox = new QComboBox();
    m_logLevelComboBox->addItems({"ERROR", "WARNING", "INFO", "DEBUG"});
    m_logLevelComboBox->setCurrentIndex(2);
    loggingLayout->addWidget(logLevelLabel, 1, 0);
    loggingLayout->addWidget(m_logLevelComboBox, 1, 1);

    layout->addWidget(loggingGroup);

    // Interface Group
    auto* interfaceGroup = new QGroupBox("User Interface");
    auto* interfaceLayout = new QVBoxLayout(interfaceGroup);

    m_showControlPanelCheckBox = new QCheckBox("Show Control Panel by default");
    m_showControlPanelCheckBox->setChecked(true);
    interfaceLayout->addWidget(m_showControlPanelCheckBox);

    m_showToolbarCheckBox = new QCheckBox("Show Toolbar");
    m_showToolbarCheckBox->setChecked(true);
    interfaceLayout->addWidget(m_showToolbarCheckBox);

    m_showStatusBarCheckBox = new QCheckBox("Show Status Bar");
    m_showStatusBarCheckBox->setChecked(true);
    interfaceLayout->addWidget(m_showStatusBarCheckBox);

    auto* themeLabel = new QLabel("Theme:");
    m_themeComboBox = new QComboBox();
    m_themeComboBox->addItems({"Dark (Professional)", "Light", "System Default"});
    m_themeComboBox->setCurrentIndex(0);

    auto* themeLayout = new QHBoxLayout();
    themeLayout->addWidget(themeLabel);
    themeLayout->addWidget(m_themeComboBox);
    themeLayout->addStretch();
    interfaceLayout->addLayout(themeLayout);

    layout->addWidget(interfaceGroup);
    layout->addStretch();

    return widget;
}

void SettingsDialog::applyProfessionalStyling()
{
    setStyleSheet(R"(
        QDialog {
            background-color: #f8f9fa;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #dee2e6;
            border-radius: 8px;
            margin-top: 1ex;
            padding-top: 15px;
            background-color: white;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 15px;
            padding: 0 8px 0 8px;
            color: #2c3e50;
            background-color: white;
        }
        QPushButton {
            background-color: #3498db;
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            font-weight: bold;
            min-width: 80px;
        }
        QPushButton:hover {
            background-color: #2980b9;
        }
        QPushButton:pressed {
            background-color: #21618c;
        }
        QPushButton:default {
            background-color: #27ae60;
        }
        QPushButton:default:hover {
            background-color: #229954;
        }
        QSpinBox, QLineEdit, QComboBox {
            border: 2px solid #ced4da;
            border-radius: 6px;
            padding: 8px;
            background-color: white;
            min-height: 20px;
        }
        QSpinBox:focus, QLineEdit:focus, QComboBox:focus {
            border-color: #3498db;
        }
        QCheckBox {
            spacing: 8px;
            color: #2c3e50;
        }
        QCheckBox::indicator {
            width: 18px;
            height: 18px;
            border: 2px solid #ced4da;
            border-radius: 4px;
            background-color: white;
        }
        QCheckBox::indicator:checked {
            background-color: #3498db;
            border-color: #2980b9;
        }
        QSlider::groove:horizontal {
            border: 1px solid #ced4da;
            height: 10px;
            background: #ecf0f1;
            border-radius: 5px;
        }
        QSlider::handle:horizontal {
            background: #3498db;
            border: 2px solid #2980b9;
            width: 20px;
            margin: -2px 0;
            border-radius: 10px;
        }
        QTabWidget::pane {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background-color: white;
        }
        QTabBar::tab {
            background-color: #e9ecef;
            border: 1px solid #dee2e6;
            padding: 12px 20px;
            margin-right: 2px;
            border-top-left-radius: 6px;
            border-top-right-radius: 6px;
        }
        QTabBar::tab:selected {
            background-color: #3498db;
            color: white;
            border-bottom-color: #3498db;
        }
        QLabel {
            color: #2c3e50;
        }
    )");
}

// Getters
int SettingsDialog::getServerPort() const
{
    return m_portSpinBox ? m_portSpinBox->value() : 4444;
}

QString SettingsDialog::getServerPassword() const
{
    return m_passwordLineEdit ? m_passwordLineEdit->text() : "admin";
}

int SettingsDialog::getMaxConnections() const
{
    return m_maxConnectionsSpinBox ? m_maxConnectionsSpinBox->value() : 10;
}

int SettingsDialog::getCompressionLevel() const
{
    return m_compressionSlider ? m_compressionSlider->value() : 6;
}

int SettingsDialog::getUpdateInterval() const
{
    return m_updateIntervalSpinBox ? m_updateIntervalSpinBox->value() : 50;
}

int SettingsDialog::getImageQuality() const
{
    return m_imageQualityComboBox ? m_imageQualityComboBox->currentIndex() : 1;
}

bool SettingsDialog::isEncryptionEnabled() const
{
    return m_enableEncryptionCheckBox ? m_enableEncryptionCheckBox->isChecked() : true;
}

bool SettingsDialog::isAutoStartEnabled() const
{
    return m_autoStartCheckBox ? m_autoStartCheckBox->isChecked() : false;
}

bool SettingsDialog::isLoggingEnabled() const
{
    return m_enableLoggingCheckBox ? m_enableLoggingCheckBox->isChecked() : true;
}

// Slots
void SettingsDialog::loadSettings()
{
    QSettings settings;

    // Load server settings
    if (m_portSpinBox) {
        m_portSpinBox->setValue(settings.value("server/port", 4444).toInt());
    }
    if (m_passwordLineEdit) {
        m_passwordLineEdit->setText(settings.value("server/password", "admin").toString());
    }
    if (m_maxConnectionsSpinBox) {
        m_maxConnectionsSpinBox->setValue(settings.value("server/maxConnections", 10).toInt());
    }
    if (m_autoStartCheckBox) {
        m_autoStartCheckBox->setChecked(settings.value("server/autoStart", false).toBool());
    }

    // Load security settings
    if (m_enableEncryptionCheckBox) {
        m_enableEncryptionCheckBox->setChecked(settings.value("security/encryption", true).toBool());
    }
    if (m_requireAuthCheckBox) {
        m_requireAuthCheckBox->setChecked(settings.value("security/requireAuth", true).toBool());
    }
    if (m_sessionTimeoutSpinBox) {
        m_sessionTimeoutSpinBox->setValue(settings.value("security/sessionTimeout", 60).toInt());
    }

    // Load performance settings
    if (m_compressionSlider) {
        int compression = settings.value("performance/compression", 6).toInt();
        m_compressionSlider->setValue(compression);
        onCompressionChanged(compression);
    }
    if (m_updateIntervalSpinBox) {
        m_updateIntervalSpinBox->setValue(settings.value("performance/updateInterval", 50).toInt());
    }
    if (m_imageQualityComboBox) {
        m_imageQualityComboBox->setCurrentIndex(settings.value("performance/imageQuality", 1).toInt());
    }

    // Load interface settings
    if (m_enableLoggingCheckBox) {
        m_enableLoggingCheckBox->setChecked(settings.value("interface/logging", true).toBool());
    }
    if (m_showControlPanelCheckBox) {
        m_showControlPanelCheckBox->setChecked(settings.value("interface/showControlPanel", true).toBool());
    }
    if (m_showToolbarCheckBox) {
        m_showToolbarCheckBox->setChecked(settings.value("interface/showToolbar", true).toBool());
    }
    if (m_showStatusBarCheckBox) {
        m_showStatusBarCheckBox->setChecked(settings.value("interface/showStatusBar", true).toBool());
    }
}

void SettingsDialog::saveSettings()
{
    QSettings settings;

    // Save server settings
    settings.setValue("server/port", getServerPort());
    settings.setValue("server/password", getServerPassword());
    settings.setValue("server/maxConnections", getMaxConnections());
    settings.setValue("server/autoStart", isAutoStartEnabled());

    // Save security settings
    settings.setValue("security/encryption", isEncryptionEnabled());
    settings.setValue("security/requireAuth", m_requireAuthCheckBox ? m_requireAuthCheckBox->isChecked() : true);
    settings.setValue("security/sessionTimeout", m_sessionTimeoutSpinBox ? m_sessionTimeoutSpinBox->value() : 60);

    // Save performance settings
    settings.setValue("performance/compression", getCompressionLevel());
    settings.setValue("performance/updateInterval", getUpdateInterval());
    settings.setValue("performance/imageQuality", getImageQuality());

    // Save interface settings
    settings.setValue("interface/logging", isLoggingEnabled());
    settings.setValue("interface/showControlPanel", m_showControlPanelCheckBox ? m_showControlPanelCheckBox->isChecked() : true);
    settings.setValue("interface/showToolbar", m_showToolbarCheckBox ? m_showToolbarCheckBox->isChecked() : true);
    settings.setValue("interface/showStatusBar", m_showStatusBarCheckBox ? m_showStatusBarCheckBox->isChecked() : true);

    settings.sync();
}

void SettingsDialog::resetToDefaults()
{
    if (m_portSpinBox) m_portSpinBox->setValue(4444);
    if (m_passwordLineEdit) m_passwordLineEdit->setText("admin");
    if (m_maxConnectionsSpinBox) m_maxConnectionsSpinBox->setValue(10);
    if (m_autoStartCheckBox) m_autoStartCheckBox->setChecked(false);
    if (m_enableEncryptionCheckBox) m_enableEncryptionCheckBox->setChecked(true);
    if (m_requireAuthCheckBox) m_requireAuthCheckBox->setChecked(true);
    if (m_sessionTimeoutSpinBox) m_sessionTimeoutSpinBox->setValue(60);
    if (m_compressionSlider) {
        m_compressionSlider->setValue(6);
        onCompressionChanged(6);
    }
    if (m_updateIntervalSpinBox) m_updateIntervalSpinBox->setValue(50);
    if (m_imageQualityComboBox) m_imageQualityComboBox->setCurrentIndex(1);
    if (m_enableLoggingCheckBox) m_enableLoggingCheckBox->setChecked(true);
    if (m_showControlPanelCheckBox) m_showControlPanelCheckBox->setChecked(true);
    if (m_showToolbarCheckBox) m_showToolbarCheckBox->setChecked(true);
    if (m_showStatusBarCheckBox) m_showStatusBarCheckBox->setChecked(true);
}

void SettingsDialog::onOkClicked()
{
    if (validateSettings()) {
        saveSettings();
        emit settingsApplied();
        emit serverConfigChanged(getServerPort(), getServerPassword(), getMaxConnections());
        accept();
    }
}

void SettingsDialog::onApplyClicked()
{
    if (validateSettings()) {
        saveSettings();
        emit settingsApplied();
        emit serverConfigChanged(getServerPort(), getServerPassword(), getMaxConnections());
    }
}

void SettingsDialog::onCancelClicked()
{
    reject();
}

void SettingsDialog::onResetClicked()
{
    int ret = QMessageBox::question(this, "Reset Settings",
        "Are you sure you want to reset all settings to their default values?",
        QMessageBox::Yes | QMessageBox::No, QMessageBox::No);

    if (ret == QMessageBox::Yes) {
        resetToDefaults();
    }
}

void SettingsDialog::onCompressionChanged(int value)
{
    if (!m_compressionLabel) return;

    QString text = QString::number(value);
    if (value == 0) text += " (No compression - Fastest)";
    else if (value <= 3) text += " (Low compression - Fast)";
    else if (value <= 6) text += " (Medium compression - Balanced)";
    else text += " (High compression - Best quality)";

    m_compressionLabel->setText(text);
}

void SettingsDialog::onTestConnection()
{
    int port = getServerPort();

    // Simple port availability test
    QMessageBox::information(this, "Connection Test",
        QString("Testing server configuration on port %1...\n\n"
                "Port: %1\n"
                "Password: %2\n"
                "Max Connections: %3\n\n"
                "Note: Full server testing requires actual server implementation.")
                .arg(port)
                .arg(getServerPassword().isEmpty() ? "(No password)" : "***")
                .arg(getMaxConnections()));
}

bool SettingsDialog::validateSettings()
{
    // Validate port
    int port = getServerPort();
    if (port < 1024 || port > 65535) {
        QMessageBox::warning(this, "Invalid Settings",
            "Server port must be between 1024 and 65535.");
        m_tabWidget->setCurrentIndex(0);
        m_portSpinBox->setFocus();
        return false;
    }

    // Validate password
    QString password = getServerPassword();
    if (password.length() < 4) {
        QMessageBox::warning(this, "Invalid Settings",
            "Server password must be at least 4 characters long.");
        m_tabWidget->setCurrentIndex(0);
        m_passwordLineEdit->setFocus();
        return false;
    }

    return true;
}
