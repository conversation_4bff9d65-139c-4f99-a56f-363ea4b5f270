Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
Copyright (C) Microsoft Corporation.  All rights reserved.

TestRawClient.cpp
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(103): warning C4005: 'AF_IPX': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(457): note: see previous definition of 'AF_IPX'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(147): warning C4005: 'AF_MAX': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(476): note: see previous definition of 'AF_MAX'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(187): warning C4005: 'SO_DONTLINGER': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(399): note: see previous definition of 'SO_DONTLINGER'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(246): error C2011: 'sockaddr': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(482): note: see declaration of 'sockaddr'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(448): error C2143: syntax error: missing '}' before 'constant'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(448): error C2059: syntax error: 'constant'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(502): error C2143: syntax error: missing ';' before '}'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(502): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(502): error C4430: missing type specifier - int assumed. Note: C++ does not support default-int
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(583): warning C4005: 'IN_CLASSA': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(284): note: see previous definition of 'IN_CLASSA'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(589): warning C4005: 'IN_CLASSB': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(290): note: see previous definition of 'IN_CLASSB'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(595): warning C4005: 'IN_CLASSC': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(296): note: see previous definition of 'IN_CLASSC'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(606): warning C4005: 'INADDR_ANY': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(301): note: see previous definition of 'INADDR_ANY'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(608): warning C4005: 'INADDR_BROADCAST': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(303): note: see previous definition of 'INADDR_BROADCAST'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2def.h(644): error C2011: 'sockaddr_in': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(309): note: see declaration of 'sockaddr_in'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(136): error C2011: 'fd_set': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(65): note: see declaration of 'fd_set'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(158): warning C4005: 'FD_SET': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(96): note: see previous definition of 'FD_SET'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(180): error C2011: 'timeval': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(108): note: see declaration of 'timeval'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(236): error C2011: 'hostent': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(164): note: see declaration of 'hostent'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(249): error C2011: 'netent': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(177): note: see declaration of 'netent'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(256): error C2011: 'servent': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(184): note: see declaration of 'servent'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(268): error C2011: 'protoent': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(196): note: see declaration of 'protoent'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(364): error C2011: 'WSAData': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(319): note: see declaration of 'WSAData'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(462): error C2011: 'sockproto': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(491): note: see declaration of 'sockproto'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(504): error C2011: 'linger': 'struct' type redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(528): note: see declaration of 'linger'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(517): warning C4005: 'SOMAXCONN': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(541): note: see previous definition of 'SOMAXCONN'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(551): warning C4005: 'FD_READ': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(559): note: see previous definition of 'FD_READ'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(554): warning C4005: 'FD_WRITE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(560): note: see previous definition of 'FD_WRITE'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(557): warning C4005: 'FD_OOB': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(561): note: see previous definition of 'FD_OOB'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(560): warning C4005: 'FD_ACCEPT': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(562): note: see previous definition of 'FD_ACCEPT'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(563): warning C4005: 'FD_CONNECT': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(563): note: see previous definition of 'FD_CONNECT'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(566): warning C4005: 'FD_CLOSE': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(564): note: see previous definition of 'FD_CLOSE'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1633): error C2375: 'accept': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(739): note: see declaration of 'accept'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1655): error C2375: 'bind': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(744): note: see declaration of 'bind'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1676): error C2375: 'closesocket': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(749): note: see declaration of 'closesocket'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1693): error C2375: 'connect': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(751): note: see declaration of 'connect'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1714): error C2375: 'ioctlsocket': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(756): note: see declaration of 'ioctlsocket'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1737): error C2375: 'getpeername': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(761): note: see declaration of 'getpeername'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1758): error C2375: 'getsockname': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(766): note: see declaration of 'getsockname'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1779): error C2375: 'getsockopt': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(771): note: see declaration of 'getsockopt'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1804): error C2375: 'htonl': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(778): note: see declaration of 'htonl'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1821): error C2375: 'htons': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(780): note: see declaration of 'htons'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1839): error C2375: 'inet_addr': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(782): note: see declaration of 'inet_addr'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1857): error C2375: 'inet_ntoa': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(784): note: see declaration of 'inet_ntoa'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1957): error C2375: 'listen': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(786): note: see declaration of 'listen'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1976): error C2375: 'ntohl': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(790): note: see declaration of 'ntohl'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(1993): error C2375: 'ntohs': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(792): note: see declaration of 'ntohs'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2010): error C2375: 'recv': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(794): note: see declaration of 'recv'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2033): error C2375: 'recvfrom': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(800): note: see declaration of 'recvfrom'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2060): error C2375: 'select': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(808): note: see declaration of 'select'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2085): error C2375: 'send': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(815): note: see declaration of 'send'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2108): error C2375: 'sendto': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(821): note: see declaration of 'sendto'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2135): error C2375: 'setsockopt': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(829): note: see declaration of 'setsockopt'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2160): error C2375: 'shutdown': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(836): note: see declaration of 'shutdown'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2180): error C2375: 'socket': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(840): note: see declaration of 'socket'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2205): error C2375: 'gethostbyaddr': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(847): note: see declaration of 'gethostbyaddr'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2227): error C2375: 'gethostbyname': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(852): note: see declaration of 'gethostbyname'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2244): error C2375: 'gethostname': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(854): note: see declaration of 'gethostname'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2284): error C2375: 'getservbyport': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(858): note: see declaration of 'getservbyport'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2303): error C2375: 'getservbyname': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(862): note: see declaration of 'getservbyname'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2322): error C2375: 'getprotobynumber': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(866): note: see declaration of 'getprotobynumber'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2339): error C2375: 'getprotobyname': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(868): note: see declaration of 'getprotobyname'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2359): error C2375: 'WSAStartup': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(872): note: see declaration of 'WSAStartup'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2379): error C2375: 'WSACleanup': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(876): note: see declaration of 'WSACleanup'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2396): error C2375: 'WSASetLastError': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(878): note: see declaration of 'WSASetLastError'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2413): error C2375: 'WSAGetLastError': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(880): note: see declaration of 'WSAGetLastError'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2434): error C2375: 'WSAIsBlocking': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(882): note: see declaration of 'WSAIsBlocking'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2452): error C2375: 'WSAUnhookBlockingHook': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(884): note: see declaration of 'WSAUnhookBlockingHook'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2470): error C2375: 'WSASetBlockingHook': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(886): note: see declaration of 'WSASetBlockingHook'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2488): error C2375: 'WSACancelBlockingCall': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(888): note: see declaration of 'WSACancelBlockingCall'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2506): error C2375: 'WSAAsyncGetServByName': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(890): note: see declaration of 'WSAAsyncGetServByName'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2534): error C2375: 'WSAAsyncGetServByPort': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(898): note: see declaration of 'WSAAsyncGetServByPort'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2562): error C2375: 'WSAAsyncGetProtoByName': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(906): note: see declaration of 'WSAAsyncGetProtoByName'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2588): error C2375: 'WSAAsyncGetProtoByNumber': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(913): note: see declaration of 'WSAAsyncGetProtoByNumber'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2614): error C2375: 'WSAAsyncGetHostByName': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(920): note: see declaration of 'WSAAsyncGetHostByName'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2640): error C2375: 'WSAAsyncGetHostByAddr': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(927): note: see declaration of 'WSAAsyncGetHostByAddr'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2670): error C2375: 'WSACancelAsyncRequest': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(936): note: see declaration of 'WSACancelAsyncRequest'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(2688): error C2375: 'WSAAsyncSelect': redefinition; different linkage
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(938): note: see declaration of 'WSAAsyncSelect'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(4320): error C2059: syntax error: '}'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock2.h(4320): error C2143: syntax error: missing ';' before '}'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(42): error C2143: syntax error: missing ';' before '{'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(42): error C2447: '{': missing function header (old-style formal list?)
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(135): warning C4005: 'IP_TOS': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(349): note: see previous definition of 'IP_TOS'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(136): warning C4005: 'IP_TTL': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(348): note: see previous definition of 'IP_TTL'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(137): warning C4005: 'IP_MULTICAST_IF': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(343): note: see previous definition of 'IP_MULTICAST_IF'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(138): warning C4005: 'IP_MULTICAST_TTL': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(344): note: see previous definition of 'IP_MULTICAST_TTL'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(139): warning C4005: 'IP_MULTICAST_LOOP': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(345): note: see previous definition of 'IP_MULTICAST_LOOP'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(140): warning C4005: 'IP_ADD_MEMBERSHIP': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(346): note: see previous definition of 'IP_ADD_MEMBERSHIP'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(141): warning C4005: 'IP_DROP_MEMBERSHIP': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(347): note: see previous definition of 'IP_DROP_MEMBERSHIP'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\shared\ws2ipdef.h(142): warning C4005: 'IP_DONTFRAGMENT': macro redefinition
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\winsock.h(350): note: see previous definition of 'IP_DONTFRAGMENT'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(733): error C2061: syntax error: identifier 'MULTICAST_MODE_TYPE'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(740): error C2065: 'PIP_MSFILTER': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(740): error C2146: syntax error: missing ';' before identifier 'Filter'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(740): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(742): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(743): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(743): error C2065: 'SourceList': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(748): error C2065: 'IP_MSFILTER': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(748): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(749): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(749): error C2065: 'PIP_MSFILTER': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(749): error C2146: syntax error: missing ';' before identifier 'HeapAlloc'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(750): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(755): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(756): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(757): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(757): error C2065: 'FilterMode': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(758): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(758): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(759): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(760): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(760): error C2065: 'SourceList': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(760): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(760): error C2660: 'memcpy': function does not take 2 arguments
C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\MSVC\14.44.35207\include\vcruntime_string.h(43): note: see declaration of 'memcpy'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(760): note: while trying to match the argument list '()'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(764): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(767): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(779): error C2061: syntax error: identifier 'MULTICAST_MODE_TYPE'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(786): error C2065: 'PIP_MSFILTER': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(786): error C2146: syntax error: missing ';' before identifier 'Filter'
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(786): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(788): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(789): error C2065: 'Filter': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(789): error C2065: 'SourceList': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(794): error C2065: 'IP_MSFILTER': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(794): error C2065: 'SourceCount': undeclared identifier
C:\Program Files (x86)\Windows Kits\10\\include\10.0.26100.0\\um\ws2tcpip.h(794): fatal error C1003: error count exceeds 100; stopping compilation
