/**
 * @file SimpleHVNCServer.cpp
 * @brief Simple HVNC Server that works without Qt6
 * <AUTHOR> Team
 * @version 2.0.0
 * 
 * This is a simplified version that integrates with the existing
 * HVNC system and shows desktop captures in a simple Windows GUI.
 */

#include "common/Common.h"
#include "Server/Server.h"
#include <windows.h>
#include <commctrl.h>
#include <vector>
#include <map>
#include <string>

#pragma comment(lib, "comctl32.lib")

// Window class name
#define WINDOW_CLASS_NAME L"HVNCServerWindow"

// Control IDs
#define ID_START_BUTTON     1001
#define ID_STOP_BUTTON      1002
#define ID_PORT_EDIT        1003
#define ID_PASSWORD_EDIT    1004
#define ID_STATUS_TEXT      1005
#define ID_DESKTOP_VIEW     1006
#define ID_CLIENT_LIST      1007

// Global variables
HWND g_hMainWindow = nullptr;
HWND g_hDesktopView = nullptr;
HWND g_hStatusText = nullptr;
HWND g_hClientList = nullptr;
HWND g_hStartButton = nullptr;
HWND g_hStopButton = nullptr;
HWND g_hPortEdit = nullptr;
HWND g_hPasswordEdit = nullptr;

bool g_serverRunning = false;
int g_serverPort = 4444;
std::string g_serverPassword = "admin";
HBITMAP g_currentDesktop = nullptr;

// Network handling
SOCKET g_serverSocket = INVALID_SOCKET;
std::vector<SOCKET> g_clients;
std::map<SOCKET, std::string> g_clientInfo;

// Function declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void CreateControls(HWND hwnd);
void StartServer();
void StopServer();
void UpdateUI();
void AddLogMessage(const std::string& message);
void HandleNewConnection();
void HandleClientData(SOCKET clientSocket);
void DisplayDesktopImage(const BYTE* imageData, int width, int height);
void SendCommandToClients(const std::string& command);

/**
 * @brief Main window procedure
 */
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_CREATE:
            CreateControls(hwnd);
            UpdateUI();
            break;

        case WM_COMMAND:
            switch (LOWORD(wParam)) {
                case ID_START_BUTTON:
                    StartServer();
                    break;
                case ID_STOP_BUTTON:
                    StopServer();
                    break;
            }
            break;

        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            
            // Paint the desktop view area
            if (g_currentDesktop) {
                RECT rect;
                GetWindowRect(g_hDesktopView, &rect);
                ScreenToClient(hwnd, (POINT*)&rect.left);
                ScreenToClient(hwnd, (POINT*)&rect.right);
                
                HDC hdcMem = CreateCompatibleDC(hdc);
                SelectObject(hdcMem, g_currentDesktop);
                
                BITMAP bm;
                GetObject(g_currentDesktop, sizeof(bm), &bm);
                
                StretchBlt(hdc, rect.left, rect.top, 
                          rect.right - rect.left, rect.bottom - rect.top,
                          hdcMem, 0, 0, bm.bmWidth, bm.bmHeight, SRCCOPY);
                
                DeleteDC(hdcMem);
            }
            
            EndPaint(hwnd, &ps);
            break;
        }

        case WM_SIZE:
            // Resize controls when window is resized
            if (wParam != SIZE_MINIMIZED) {
                RECT clientRect;
                GetClientRect(hwnd, &clientRect);
                
                // Resize desktop view
                if (g_hDesktopView) {
                    SetWindowPos(g_hDesktopView, nullptr, 
                                200, 50, 
                                clientRect.right - 220, clientRect.bottom - 200,
                                SWP_NOZORDER);
                }
                
                // Resize status text
                if (g_hStatusText) {
                    SetWindowPos(g_hStatusText, nullptr,
                                10, clientRect.bottom - 140,
                                clientRect.right - 20, 120,
                                SWP_NOZORDER);
                }
            }
            break;

        case WM_CLOSE:
            if (g_serverRunning) {
                StopServer();
            }
            DestroyWindow(hwnd);
            break;

        case WM_DESTROY:
            PostQuitMessage(0);
            break;

        default:
            return DefWindowProc(hwnd, uMsg, wParam, lParam);
    }
    return 0;
}

/**
 * @brief Create window controls
 */
void CreateControls(HWND hwnd) {
    // Server controls group
    CreateWindow(L"BUTTON", L"Server Control", 
                WS_VISIBLE | WS_CHILD | BS_GROUPBOX,
                10, 10, 180, 120, hwnd, nullptr, GetModuleHandle(nullptr), nullptr);

    CreateWindow(L"STATIC", L"Port:", 
                WS_VISIBLE | WS_CHILD,
                20, 35, 40, 20, hwnd, nullptr, GetModuleHandle(nullptr), nullptr);

    g_hPortEdit = CreateWindow(L"EDIT", L"4444", 
                              WS_VISIBLE | WS_CHILD | WS_BORDER,
                              65, 33, 60, 22, hwnd, (HMENU)ID_PORT_EDIT, GetModuleHandle(nullptr), nullptr);

    CreateWindow(L"STATIC", L"Password:", 
                WS_VISIBLE | WS_CHILD,
                20, 65, 60, 20, hwnd, nullptr, GetModuleHandle(nullptr), nullptr);

    g_hPasswordEdit = CreateWindow(L"EDIT", L"admin", 
                                  WS_VISIBLE | WS_CHILD | WS_BORDER | ES_PASSWORD,
                                  85, 63, 80, 22, hwnd, (HMENU)ID_PASSWORD_EDIT, GetModuleHandle(nullptr), nullptr);

    g_hStartButton = CreateWindow(L"BUTTON", L"Start Server", 
                                 WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                 20, 95, 70, 25, hwnd, (HMENU)ID_START_BUTTON, GetModuleHandle(nullptr), nullptr);

    g_hStopButton = CreateWindow(L"BUTTON", L"Stop Server", 
                                WS_VISIBLE | WS_CHILD | BS_PUSHBUTTON,
                                100, 95, 70, 25, hwnd, (HMENU)ID_STOP_BUTTON, GetModuleHandle(nullptr), nullptr);

    // Desktop view area
    g_hDesktopView = CreateWindow(L"STATIC", L"Desktop View - Waiting for client connection...", 
                                 WS_VISIBLE | WS_CHILD | WS_BORDER | SS_CENTER,
                                 200, 50, 600, 400, hwnd, (HMENU)ID_DESKTOP_VIEW, GetModuleHandle(nullptr), nullptr);

    // Client list
    CreateWindow(L"STATIC", L"Connected Clients:", 
                WS_VISIBLE | WS_CHILD,
                10, 140, 120, 20, hwnd, nullptr, GetModuleHandle(nullptr), nullptr);

    g_hClientList = CreateWindow(L"LISTBOX", nullptr, 
                                WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL,
                                10, 160, 180, 100, hwnd, (HMENU)ID_CLIENT_LIST, GetModuleHandle(nullptr), nullptr);

    // Status text area
    g_hStatusText = CreateWindow(L"EDIT", L"Ready to start server...\r\n", 
                                WS_VISIBLE | WS_CHILD | WS_BORDER | WS_VSCROLL | 
                                ES_MULTILINE | ES_READONLY,
                                10, 270, 780, 120, hwnd, (HMENU)ID_STATUS_TEXT, GetModuleHandle(nullptr), nullptr);
}

/**
 * @brief Start the HVNC server
 */
void StartServer() {
    if (g_serverRunning) return;

    // Get port and password from controls
    wchar_t portText[10];
    GetWindowText(g_hPortEdit, portText, 10);
    g_serverPort = _wtoi(portText);

    wchar_t passwordText[256];
    GetWindowText(g_hPasswordEdit, passwordText, 256);
    
    // Convert to string
    char passwordBuffer[256];
    WideCharToMultiByte(CP_UTF8, 0, passwordText, -1, passwordBuffer, 256, nullptr, nullptr);
    g_serverPassword = passwordBuffer;

    // Initialize Winsock
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0) {
        AddLogMessage("Failed to initialize Winsock");
        return;
    }

    // Create server socket
    g_serverSocket = socket(AF_INET, SOCK_STREAM, 0);
    if (g_serverSocket == INVALID_SOCKET) {
        AddLogMessage("Failed to create server socket");
        WSACleanup();
        return;
    }

    // Bind socket
    sockaddr_in serverAddr;
    serverAddr.sin_family = AF_INET;
    serverAddr.sin_addr.s_addr = INADDR_ANY;
    serverAddr.sin_port = htons(g_serverPort);

    if (bind(g_serverSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
        AddLogMessage("Failed to bind server socket to port " + std::to_string(g_serverPort));
        closesocket(g_serverSocket);
        WSACleanup();
        return;
    }

    // Listen for connections
    if (listen(g_serverSocket, SOMAXCONN) == SOCKET_ERROR) {
        AddLogMessage("Failed to listen on server socket");
        closesocket(g_serverSocket);
        WSACleanup();
        return;
    }

    // Set non-blocking mode
    u_long mode = 1;
    ioctlsocket(g_serverSocket, FIONBIO, &mode);

    g_serverRunning = true;
    UpdateUI();

    AddLogMessage("HVNC Server started on port " + std::to_string(g_serverPort));
    AddLogMessage("Password: " + g_serverPassword);
    AddLogMessage("Waiting for client connections...");

    // Set timer for checking connections
    SetTimer(g_hMainWindow, 1, 100, nullptr); // Check every 100ms
}

/**
 * @brief Stop the HVNC server
 */
void StopServer() {
    if (!g_serverRunning) return;

    g_serverRunning = false;
    KillTimer(g_hMainWindow, 1);

    // Close all client connections
    for (SOCKET client : g_clients) {
        closesocket(client);
    }
    g_clients.clear();
    g_clientInfo.clear();

    // Close server socket
    if (g_serverSocket != INVALID_SOCKET) {
        closesocket(g_serverSocket);
        g_serverSocket = INVALID_SOCKET;
    }

    WSACleanup();
    UpdateUI();

    AddLogMessage("HVNC Server stopped");

    // Clear client list
    SendMessage(g_hClientList, LB_RESETCONTENT, 0, 0);
    
    // Clear desktop view
    SetWindowText(g_hDesktopView, L"Desktop View - Server stopped");
    if (g_currentDesktop) {
        DeleteObject(g_currentDesktop);
        g_currentDesktop = nullptr;
    }
}

/**
 * @brief Update UI controls based on server state
 */
void UpdateUI() {
    EnableWindow(g_hStartButton, !g_serverRunning);
    EnableWindow(g_hStopButton, g_serverRunning);
    EnableWindow(g_hPortEdit, !g_serverRunning);
    EnableWindow(g_hPasswordEdit, !g_serverRunning);
}

/**
 * @brief Add log message to status text
 */
void AddLogMessage(const std::string& message) {
    // Get current time
    SYSTEMTIME st;
    GetLocalTime(&st);

    char timeStr[32];
    sprintf_s(timeStr, "[%02d:%02d:%02d] ", st.wHour, st.wMinute, st.wSecond);

    std::string logEntry = timeStr + message + "\r\n";

    // Convert to wide string
    int len = MultiByteToWideChar(CP_UTF8, 0, logEntry.c_str(), -1, nullptr, 0);
    std::vector<wchar_t> wideStr(len);
    MultiByteToWideChar(CP_UTF8, 0, logEntry.c_str(), -1, wideStr.data(), len);

    // Append to status text
    int textLen = GetWindowTextLength(g_hStatusText);
    SendMessage(g_hStatusText, EM_SETSEL, textLen, textLen);
    SendMessage(g_hStatusText, EM_REPLACESEL, FALSE, (LPARAM)wideStr.data());

    // Scroll to bottom
    SendMessage(g_hStatusText, EM_SCROLLCARET, 0, 0);
}

/**
 * @brief Handle new client connections
 */
void HandleNewConnection() {
    sockaddr_in clientAddr;
    int clientAddrLen = sizeof(clientAddr);

    SOCKET clientSocket = accept(g_serverSocket, (sockaddr*)&clientAddr, &clientAddrLen);
    if (clientSocket != INVALID_SOCKET) {
        // Set non-blocking mode
        u_long mode = 1;
        ioctlsocket(clientSocket, FIONBIO, &mode);

        g_clients.push_back(clientSocket);

        // Get client info
        char clientIP[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &clientAddr.sin_addr, clientIP, INET_ADDRSTRLEN);
        std::string clientInfo = std::string(clientIP) + ":" + std::to_string(ntohs(clientAddr.sin_port));
        g_clientInfo[clientSocket] = clientInfo;

        // Add to client list
        int len = MultiByteToWideChar(CP_UTF8, 0, clientInfo.c_str(), -1, nullptr, 0);
        std::vector<wchar_t> wideStr(len);
        MultiByteToWideChar(CP_UTF8, 0, clientInfo.c_str(), -1, wideStr.data(), len);
        SendMessage(g_hClientList, LB_ADDSTRING, 0, (LPARAM)wideStr.data());

        AddLogMessage("Client connected: " + clientInfo);

        // Send welcome message
        std::string welcome = "WELCOME:HVNC Server v2.0\n";
        send(clientSocket, welcome.c_str(), welcome.length(), 0);
    }
}

/**
 * @brief Handle data from clients
 */
void HandleClientData(SOCKET clientSocket) {
    char buffer[4096];
    int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);

    if (bytesReceived > 0) {
        buffer[bytesReceived] = '\0';
        std::string data(buffer);

        if (data.find("AUTH:") == 0) {
            std::string password = data.substr(5);
            // Remove newline if present
            if (!password.empty() && password.back() == '\n') {
                password.pop_back();
            }

            if (password == g_serverPassword) {
                std::string response = "AUTH:OK\n";
                send(clientSocket, response.c_str(), response.length(), 0);
                AddLogMessage("Client authenticated: " + g_clientInfo[clientSocket]);
            } else {
                std::string response = "AUTH:FAIL\n";
                send(clientSocket, response.c_str(), response.length(), 0);
                AddLogMessage("Authentication failed: " + g_clientInfo[clientSocket]);
            }
        } else if (data.find("IMAGE:") == 0) {
            // Parse image header: IMAGE:width:height:size
            size_t pos1 = data.find(':', 6);
            size_t pos2 = data.find(':', pos1 + 1);
            size_t pos3 = data.find(':', pos2 + 1);

            if (pos1 != std::string::npos && pos2 != std::string::npos && pos3 != std::string::npos) {
                int width = std::stoi(data.substr(6, pos1 - 6));
                int height = std::stoi(data.substr(pos1 + 1, pos2 - pos1 - 1));
                int imageSize = std::stoi(data.substr(pos2 + 1, pos3 - pos2 - 1));

                AddLogMessage("Received desktop image: " + std::to_string(width) + "x" + std::to_string(height));

                // Update desktop view text
                std::string viewText = "Desktop: " + std::to_string(width) + "x" + std::to_string(height) + " (Live)";
                int len = MultiByteToWideChar(CP_UTF8, 0, viewText.c_str(), -1, nullptr, 0);
                std::vector<wchar_t> wideStr(len);
                MultiByteToWideChar(CP_UTF8, 0, viewText.c_str(), -1, wideStr.data(), len);
                SetWindowText(g_hDesktopView, wideStr.data());

                // TODO: Process actual image data here
                // For now, just acknowledge receipt
            }
        } else if (data.find("STATUS:") == 0) {
            std::string status = data.substr(7);
            if (!status.empty() && status.back() == '\n') {
                status.pop_back();
            }
            AddLogMessage("Client status: " + status);
        }
    } else if (bytesReceived == 0 || (bytesReceived == SOCKET_ERROR && WSAGetLastError() != WSAEWOULDBLOCK)) {
        // Client disconnected
        AddLogMessage("Client disconnected: " + g_clientInfo[clientSocket]);

        // Remove from client list
        std::string clientInfo = g_clientInfo[clientSocket];
        int len = MultiByteToWideChar(CP_UTF8, 0, clientInfo.c_str(), -1, nullptr, 0);
        std::vector<wchar_t> wideStr(len);
        MultiByteToWideChar(CP_UTF8, 0, clientInfo.c_str(), -1, wideStr.data(), len);

        int index = SendMessage(g_hClientList, LB_FINDSTRINGEXACT, -1, (LPARAM)wideStr.data());
        if (index != LB_ERR) {
            SendMessage(g_hClientList, LB_DELETESTRING, index, 0);
        }

        // Remove from vectors
        g_clients.erase(std::remove(g_clients.begin(), g_clients.end(), clientSocket), g_clients.end());
        g_clientInfo.erase(clientSocket);

        closesocket(clientSocket);
    }
}

/**
 * @brief Send command to all connected clients
 */
void SendCommandToClients(const std::string& command) {
    for (auto it = g_clients.begin(); it != g_clients.end();) {
        SOCKET client = *it;
        int result = send(client, command.c_str(), command.length(), 0);

        if (result == SOCKET_ERROR) {
            // Client disconnected, remove it
            AddLogMessage("Client disconnected during send: " + g_clientInfo[client]);
            g_clientInfo.erase(client);
            closesocket(client);
            it = g_clients.erase(it);
        } else {
            ++it;
        }
    }
}

/**
 * @brief Main entry point
 */
int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Initialize common controls
    INITCOMMONCONTROLSEX icex;
    icex.dwSize = sizeof(INITCOMMONCONTROLSEX);
    icex.dwICC = ICC_STANDARD_CLASSES;
    InitCommonControlsEx(&icex);

    // Register window class
    WNDCLASSEX wc = {};
    wc.cbSize = sizeof(WNDCLASSEX);
    wc.style = CS_HREDRAW | CS_VREDRAW;
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.hbrBackground = (HBRUSH)(COLOR_WINDOW + 1);
    wc.lpszClassName = WINDOW_CLASS_NAME;
    wc.hIconSm = LoadIcon(nullptr, IDI_APPLICATION);

    if (!RegisterClassEx(&wc)) {
        MessageBox(nullptr, L"Failed to register window class", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Create main window
    g_hMainWindow = CreateWindowEx(
        0,
        WINDOW_CLASS_NAME,
        L"HVNC Server - Professional Remote Desktop Controller",
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT,
        900, 600,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!g_hMainWindow) {
        MessageBox(nullptr, L"Failed to create main window", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    ShowWindow(g_hMainWindow, nCmdShow);
    UpdateWindow(g_hMainWindow);

    // Message loop with network handling
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);

        // Handle server operations
        if (g_serverRunning) {
            // Check for new connections
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(g_serverSocket, &readSet);

            timeval timeout = {0, 0}; // Non-blocking
            if (select(0, &readSet, nullptr, nullptr, &timeout) > 0) {
                if (FD_ISSET(g_serverSocket, &readSet)) {
                    HandleNewConnection();
                }
            }

            // Handle client data
            for (SOCKET client : g_clients) {
                FD_ZERO(&readSet);
                FD_SET(client, &readSet);
                if (select(0, &readSet, nullptr, nullptr, &timeout) > 0) {
                    if (FD_ISSET(client, &readSet)) {
                        HandleClientData(client);
                    }
                }
            }
        }
    }

    return (int)msg.wParam;
}
