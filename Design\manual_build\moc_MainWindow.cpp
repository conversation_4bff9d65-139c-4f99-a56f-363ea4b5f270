/****************************************************************************
** Meta object code from reading C++ file 'MainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../include/MainWindow.h"
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSMainWindowENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSMainWindowENDCLASS = QtMocHelpers::stringData(
    "MainWindow",
    "onNewConnection",
    "",
    "onDisconnect",
    "onStartServer",
    "onStopServer",
    "onSettings",
    "onAbout",
    "onExit",
    "onToggleControlPanel",
    "onToggleStatusBar",
    "onToggleToolBar",
    "onToggleFullScreen",
    "onConnectionStateChanged",
    "ConnectionState",
    "state",
    "onServerStateChanged",
    "ServerState",
    "onClientConnected",
    "ClientId",
    "clientId",
    "clientInfo",
    "onClientDisconnected",
    "onStatusMessage",
    "message",
    "timeout",
    "onProgressUpdate",
    "value",
    "text",
    "onErrorMessage",
    "title",
    "onStartServerFromPanel",
    "port",
    "password",
    "maxConnections",
    "onStopServerFromPanel",
    "onControlPanelSettingsChanged",
    "onDisconnectClientFromPanel",
    "showQuickServerConfig",
    "onSettingsApplied",
    "onServerConfigFromSettings",
    "onQuickServerConfigChanged",
    "autoStart"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSMainWindowENDCLASS_t {
    uint offsetsAndSizes[86];
    char stringdata0[11];
    char stringdata1[16];
    char stringdata2[1];
    char stringdata3[13];
    char stringdata4[14];
    char stringdata5[13];
    char stringdata6[11];
    char stringdata7[8];
    char stringdata8[7];
    char stringdata9[21];
    char stringdata10[18];
    char stringdata11[16];
    char stringdata12[19];
    char stringdata13[25];
    char stringdata14[16];
    char stringdata15[6];
    char stringdata16[21];
    char stringdata17[12];
    char stringdata18[18];
    char stringdata19[9];
    char stringdata20[9];
    char stringdata21[11];
    char stringdata22[21];
    char stringdata23[16];
    char stringdata24[8];
    char stringdata25[8];
    char stringdata26[17];
    char stringdata27[6];
    char stringdata28[5];
    char stringdata29[15];
    char stringdata30[6];
    char stringdata31[23];
    char stringdata32[5];
    char stringdata33[9];
    char stringdata34[15];
    char stringdata35[22];
    char stringdata36[30];
    char stringdata37[28];
    char stringdata38[22];
    char stringdata39[18];
    char stringdata40[27];
    char stringdata41[27];
    char stringdata42[10];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSMainWindowENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSMainWindowENDCLASS_t qt_meta_stringdata_CLASSMainWindowENDCLASS = {
    {
        QT_MOC_LITERAL(0, 10),  // "MainWindow"
        QT_MOC_LITERAL(11, 15),  // "onNewConnection"
        QT_MOC_LITERAL(27, 0),  // ""
        QT_MOC_LITERAL(28, 12),  // "onDisconnect"
        QT_MOC_LITERAL(41, 13),  // "onStartServer"
        QT_MOC_LITERAL(55, 12),  // "onStopServer"
        QT_MOC_LITERAL(68, 10),  // "onSettings"
        QT_MOC_LITERAL(79, 7),  // "onAbout"
        QT_MOC_LITERAL(87, 6),  // "onExit"
        QT_MOC_LITERAL(94, 20),  // "onToggleControlPanel"
        QT_MOC_LITERAL(115, 17),  // "onToggleStatusBar"
        QT_MOC_LITERAL(133, 15),  // "onToggleToolBar"
        QT_MOC_LITERAL(149, 18),  // "onToggleFullScreen"
        QT_MOC_LITERAL(168, 24),  // "onConnectionStateChanged"
        QT_MOC_LITERAL(193, 15),  // "ConnectionState"
        QT_MOC_LITERAL(209, 5),  // "state"
        QT_MOC_LITERAL(215, 20),  // "onServerStateChanged"
        QT_MOC_LITERAL(236, 11),  // "ServerState"
        QT_MOC_LITERAL(248, 17),  // "onClientConnected"
        QT_MOC_LITERAL(266, 8),  // "ClientId"
        QT_MOC_LITERAL(275, 8),  // "clientId"
        QT_MOC_LITERAL(284, 10),  // "clientInfo"
        QT_MOC_LITERAL(295, 20),  // "onClientDisconnected"
        QT_MOC_LITERAL(316, 15),  // "onStatusMessage"
        QT_MOC_LITERAL(332, 7),  // "message"
        QT_MOC_LITERAL(340, 7),  // "timeout"
        QT_MOC_LITERAL(348, 16),  // "onProgressUpdate"
        QT_MOC_LITERAL(365, 5),  // "value"
        QT_MOC_LITERAL(371, 4),  // "text"
        QT_MOC_LITERAL(376, 14),  // "onErrorMessage"
        QT_MOC_LITERAL(391, 5),  // "title"
        QT_MOC_LITERAL(397, 22),  // "onStartServerFromPanel"
        QT_MOC_LITERAL(420, 4),  // "port"
        QT_MOC_LITERAL(425, 8),  // "password"
        QT_MOC_LITERAL(434, 14),  // "maxConnections"
        QT_MOC_LITERAL(449, 21),  // "onStopServerFromPanel"
        QT_MOC_LITERAL(471, 29),  // "onControlPanelSettingsChanged"
        QT_MOC_LITERAL(501, 27),  // "onDisconnectClientFromPanel"
        QT_MOC_LITERAL(529, 21),  // "showQuickServerConfig"
        QT_MOC_LITERAL(551, 17),  // "onSettingsApplied"
        QT_MOC_LITERAL(569, 26),  // "onServerConfigFromSettings"
        QT_MOC_LITERAL(596, 26),  // "onQuickServerConfigChanged"
        QT_MOC_LITERAL(623, 9)   // "autoStart"
    },
    "MainWindow",
    "onNewConnection",
    "",
    "onDisconnect",
    "onStartServer",
    "onStopServer",
    "onSettings",
    "onAbout",
    "onExit",
    "onToggleControlPanel",
    "onToggleStatusBar",
    "onToggleToolBar",
    "onToggleFullScreen",
    "onConnectionStateChanged",
    "ConnectionState",
    "state",
    "onServerStateChanged",
    "ServerState",
    "onClientConnected",
    "ClientId",
    "clientId",
    "clientInfo",
    "onClientDisconnected",
    "onStatusMessage",
    "message",
    "timeout",
    "onProgressUpdate",
    "value",
    "text",
    "onErrorMessage",
    "title",
    "onStartServerFromPanel",
    "port",
    "password",
    "maxConnections",
    "onStopServerFromPanel",
    "onControlPanelSettingsChanged",
    "onDisconnectClientFromPanel",
    "showQuickServerConfig",
    "onSettingsApplied",
    "onServerConfigFromSettings",
    "onQuickServerConfigChanged",
    "autoStart"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSMainWindowENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      28,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  182,    2, 0x08,    1 /* Private */,
       3,    0,  183,    2, 0x08,    2 /* Private */,
       4,    0,  184,    2, 0x08,    3 /* Private */,
       5,    0,  185,    2, 0x08,    4 /* Private */,
       6,    0,  186,    2, 0x08,    5 /* Private */,
       7,    0,  187,    2, 0x08,    6 /* Private */,
       8,    0,  188,    2, 0x08,    7 /* Private */,
       9,    0,  189,    2, 0x08,    8 /* Private */,
      10,    0,  190,    2, 0x08,    9 /* Private */,
      11,    0,  191,    2, 0x08,   10 /* Private */,
      12,    0,  192,    2, 0x08,   11 /* Private */,
      13,    1,  193,    2, 0x08,   12 /* Private */,
      16,    1,  196,    2, 0x08,   14 /* Private */,
      18,    2,  199,    2, 0x08,   16 /* Private */,
      22,    1,  204,    2, 0x08,   19 /* Private */,
      23,    2,  207,    2, 0x08,   21 /* Private */,
      23,    1,  212,    2, 0x28,   24 /* Private | MethodCloned */,
      26,    2,  215,    2, 0x08,   26 /* Private */,
      26,    1,  220,    2, 0x28,   29 /* Private | MethodCloned */,
      29,    2,  223,    2, 0x08,   31 /* Private */,
      31,    3,  228,    2, 0x08,   34 /* Private */,
      35,    0,  235,    2, 0x08,   38 /* Private */,
      36,    0,  236,    2, 0x08,   39 /* Private */,
      37,    1,  237,    2, 0x08,   40 /* Private */,
      38,    0,  240,    2, 0x08,   42 /* Private */,
      39,    0,  241,    2, 0x08,   43 /* Private */,
      40,    3,  242,    2, 0x08,   44 /* Private */,
      41,    4,  249,    2, 0x08,   48 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 14,   15,
    QMetaType::Void, 0x80000000 | 17,   15,
    QMetaType::Void, 0x80000000 | 19, QMetaType::QString,   20,   21,
    QMetaType::Void, 0x80000000 | 19,   20,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   24,   25,
    QMetaType::Void, QMetaType::QString,   24,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,   27,   28,
    QMetaType::Void, QMetaType::Int,   27,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   30,   24,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int,   32,   33,   34,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   20,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int,   32,   33,   34,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int, QMetaType::Bool,   32,   33,   34,   42,

       0        // eod
};

Q_CONSTINIT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_CLASSMainWindowENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSMainWindowENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSMainWindowENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<MainWindow, std::true_type>,
        // method 'onNewConnection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDisconnect'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStartServer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStopServer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAbout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleControlPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleStatusBar'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleToolBar'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleFullScreen'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConnectionStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ConnectionState, std::false_type>,
        // method 'onServerStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ServerState, std::false_type>,
        // method 'onClientConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onClientDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        // method 'onStatusMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onStatusMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onProgressUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onProgressUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onErrorMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onStartServerFromPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onStopServerFromPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onControlPanelSettingsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDisconnectClientFromPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'showQuickServerConfig'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSettingsApplied'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onServerConfigFromSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onQuickServerConfigChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>
    >,
    nullptr
} };

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onNewConnection(); break;
        case 1: _t->onDisconnect(); break;
        case 2: _t->onStartServer(); break;
        case 3: _t->onStopServer(); break;
        case 4: _t->onSettings(); break;
        case 5: _t->onAbout(); break;
        case 6: _t->onExit(); break;
        case 7: _t->onToggleControlPanel(); break;
        case 8: _t->onToggleStatusBar(); break;
        case 9: _t->onToggleToolBar(); break;
        case 10: _t->onToggleFullScreen(); break;
        case 11: _t->onConnectionStateChanged((*reinterpret_cast< std::add_pointer_t<ConnectionState>>(_a[1]))); break;
        case 12: _t->onServerStateChanged((*reinterpret_cast< std::add_pointer_t<ServerState>>(_a[1]))); break;
        case 13: _t->onClientConnected((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 14: _t->onClientDisconnected((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1]))); break;
        case 15: _t->onStatusMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 16: _t->onStatusMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 17: _t->onProgressUpdate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 18: _t->onProgressUpdate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 19: _t->onErrorMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 20: _t->onStartServerFromPanel((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 21: _t->onStopServerFromPanel(); break;
        case 22: _t->onControlPanelSettingsChanged(); break;
        case 23: _t->onDisconnectClientFromPanel((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 24: _t->showQuickServerConfig(); break;
        case 25: _t->onSettingsApplied(); break;
        case 26: _t->onServerConfigFromSettings((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 27: _t->onQuickServerConfigChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[4]))); break;
        default: ;
        }
    }
}

const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSMainWindowENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 28)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 28;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 28)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 28;
    }
    return _id;
}
QT_WARNING_POP
