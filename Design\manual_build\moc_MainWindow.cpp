/****************************************************************************
** Meta object code from reading C++ file 'MainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../include/MainWindow.h"
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSMainWindowENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSMainWindowENDCLASS = QtMocHelpers::stringData(
    "MainWindow",
    "onNewConnection",
    "",
    "onDisconnect",
    "onStartServer",
    "onStopServer",
    "onSettings",
    "onAbout",
    "onExit",
    "onToggleControlPanel",
    "onToggleStatusBar",
    "onToggleToolBar",
    "onToggleFullScreen",
    "onConnectionStateChanged",
    "ConnectionState",
    "state",
    "onServerStateChanged",
    "ServerState",
    "onClientConnected",
    "ClientId",
    "clientId",
    "clientInfo",
    "onClientDisconnected",
    "onDesktopImageReceived",
    "image",
    "onDesktopMouseEvent",
    "QMouseEvent*",
    "event",
    "desktopPos",
    "onDesktopKeyEvent",
    "QKeyEvent*",
    "onDesktopWheelEvent",
    "QWheelEvent*",
    "onStatusMessage",
    "message",
    "timeout",
    "onProgressUpdate",
    "value",
    "text",
    "onErrorMessage",
    "title",
    "onStartServerFromPanel",
    "port",
    "password",
    "maxConnections",
    "onStopServerFromPanel",
    "onControlPanelSettingsChanged",
    "onDisconnectClientFromPanel",
    "showQuickServerConfig",
    "onSettingsApplied",
    "onServerConfigFromSettings",
    "onQuickServerConfigChanged",
    "autoStart"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSMainWindowENDCLASS_t {
    uint offsetsAndSizes[106];
    char stringdata0[11];
    char stringdata1[16];
    char stringdata2[1];
    char stringdata3[13];
    char stringdata4[14];
    char stringdata5[13];
    char stringdata6[11];
    char stringdata7[8];
    char stringdata8[7];
    char stringdata9[21];
    char stringdata10[18];
    char stringdata11[16];
    char stringdata12[19];
    char stringdata13[25];
    char stringdata14[16];
    char stringdata15[6];
    char stringdata16[21];
    char stringdata17[12];
    char stringdata18[18];
    char stringdata19[9];
    char stringdata20[9];
    char stringdata21[11];
    char stringdata22[21];
    char stringdata23[23];
    char stringdata24[6];
    char stringdata25[20];
    char stringdata26[13];
    char stringdata27[6];
    char stringdata28[11];
    char stringdata29[18];
    char stringdata30[11];
    char stringdata31[20];
    char stringdata32[13];
    char stringdata33[16];
    char stringdata34[8];
    char stringdata35[8];
    char stringdata36[17];
    char stringdata37[6];
    char stringdata38[5];
    char stringdata39[15];
    char stringdata40[6];
    char stringdata41[23];
    char stringdata42[5];
    char stringdata43[9];
    char stringdata44[15];
    char stringdata45[22];
    char stringdata46[30];
    char stringdata47[28];
    char stringdata48[22];
    char stringdata49[18];
    char stringdata50[27];
    char stringdata51[27];
    char stringdata52[10];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSMainWindowENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSMainWindowENDCLASS_t qt_meta_stringdata_CLASSMainWindowENDCLASS = {
    {
        QT_MOC_LITERAL(0, 10),  // "MainWindow"
        QT_MOC_LITERAL(11, 15),  // "onNewConnection"
        QT_MOC_LITERAL(27, 0),  // ""
        QT_MOC_LITERAL(28, 12),  // "onDisconnect"
        QT_MOC_LITERAL(41, 13),  // "onStartServer"
        QT_MOC_LITERAL(55, 12),  // "onStopServer"
        QT_MOC_LITERAL(68, 10),  // "onSettings"
        QT_MOC_LITERAL(79, 7),  // "onAbout"
        QT_MOC_LITERAL(87, 6),  // "onExit"
        QT_MOC_LITERAL(94, 20),  // "onToggleControlPanel"
        QT_MOC_LITERAL(115, 17),  // "onToggleStatusBar"
        QT_MOC_LITERAL(133, 15),  // "onToggleToolBar"
        QT_MOC_LITERAL(149, 18),  // "onToggleFullScreen"
        QT_MOC_LITERAL(168, 24),  // "onConnectionStateChanged"
        QT_MOC_LITERAL(193, 15),  // "ConnectionState"
        QT_MOC_LITERAL(209, 5),  // "state"
        QT_MOC_LITERAL(215, 20),  // "onServerStateChanged"
        QT_MOC_LITERAL(236, 11),  // "ServerState"
        QT_MOC_LITERAL(248, 17),  // "onClientConnected"
        QT_MOC_LITERAL(266, 8),  // "ClientId"
        QT_MOC_LITERAL(275, 8),  // "clientId"
        QT_MOC_LITERAL(284, 10),  // "clientInfo"
        QT_MOC_LITERAL(295, 20),  // "onClientDisconnected"
        QT_MOC_LITERAL(316, 22),  // "onDesktopImageReceived"
        QT_MOC_LITERAL(339, 5),  // "image"
        QT_MOC_LITERAL(345, 19),  // "onDesktopMouseEvent"
        QT_MOC_LITERAL(365, 12),  // "QMouseEvent*"
        QT_MOC_LITERAL(378, 5),  // "event"
        QT_MOC_LITERAL(384, 10),  // "desktopPos"
        QT_MOC_LITERAL(395, 17),  // "onDesktopKeyEvent"
        QT_MOC_LITERAL(413, 10),  // "QKeyEvent*"
        QT_MOC_LITERAL(424, 19),  // "onDesktopWheelEvent"
        QT_MOC_LITERAL(444, 12),  // "QWheelEvent*"
        QT_MOC_LITERAL(457, 15),  // "onStatusMessage"
        QT_MOC_LITERAL(473, 7),  // "message"
        QT_MOC_LITERAL(481, 7),  // "timeout"
        QT_MOC_LITERAL(489, 16),  // "onProgressUpdate"
        QT_MOC_LITERAL(506, 5),  // "value"
        QT_MOC_LITERAL(512, 4),  // "text"
        QT_MOC_LITERAL(517, 14),  // "onErrorMessage"
        QT_MOC_LITERAL(532, 5),  // "title"
        QT_MOC_LITERAL(538, 22),  // "onStartServerFromPanel"
        QT_MOC_LITERAL(561, 4),  // "port"
        QT_MOC_LITERAL(566, 8),  // "password"
        QT_MOC_LITERAL(575, 14),  // "maxConnections"
        QT_MOC_LITERAL(590, 21),  // "onStopServerFromPanel"
        QT_MOC_LITERAL(612, 29),  // "onControlPanelSettingsChanged"
        QT_MOC_LITERAL(642, 27),  // "onDisconnectClientFromPanel"
        QT_MOC_LITERAL(670, 21),  // "showQuickServerConfig"
        QT_MOC_LITERAL(692, 17),  // "onSettingsApplied"
        QT_MOC_LITERAL(710, 26),  // "onServerConfigFromSettings"
        QT_MOC_LITERAL(737, 26),  // "onQuickServerConfigChanged"
        QT_MOC_LITERAL(764, 9)   // "autoStart"
    },
    "MainWindow",
    "onNewConnection",
    "",
    "onDisconnect",
    "onStartServer",
    "onStopServer",
    "onSettings",
    "onAbout",
    "onExit",
    "onToggleControlPanel",
    "onToggleStatusBar",
    "onToggleToolBar",
    "onToggleFullScreen",
    "onConnectionStateChanged",
    "ConnectionState",
    "state",
    "onServerStateChanged",
    "ServerState",
    "onClientConnected",
    "ClientId",
    "clientId",
    "clientInfo",
    "onClientDisconnected",
    "onDesktopImageReceived",
    "image",
    "onDesktopMouseEvent",
    "QMouseEvent*",
    "event",
    "desktopPos",
    "onDesktopKeyEvent",
    "QKeyEvent*",
    "onDesktopWheelEvent",
    "QWheelEvent*",
    "onStatusMessage",
    "message",
    "timeout",
    "onProgressUpdate",
    "value",
    "text",
    "onErrorMessage",
    "title",
    "onStartServerFromPanel",
    "port",
    "password",
    "maxConnections",
    "onStopServerFromPanel",
    "onControlPanelSettingsChanged",
    "onDisconnectClientFromPanel",
    "showQuickServerConfig",
    "onSettingsApplied",
    "onServerConfigFromSettings",
    "onQuickServerConfigChanged",
    "autoStart"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSMainWindowENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      32,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
       1,    0,  206,    2, 0x08,    1 /* Private */,
       3,    0,  207,    2, 0x08,    2 /* Private */,
       4,    0,  208,    2, 0x08,    3 /* Private */,
       5,    0,  209,    2, 0x08,    4 /* Private */,
       6,    0,  210,    2, 0x08,    5 /* Private */,
       7,    0,  211,    2, 0x08,    6 /* Private */,
       8,    0,  212,    2, 0x08,    7 /* Private */,
       9,    0,  213,    2, 0x08,    8 /* Private */,
      10,    0,  214,    2, 0x08,    9 /* Private */,
      11,    0,  215,    2, 0x08,   10 /* Private */,
      12,    0,  216,    2, 0x08,   11 /* Private */,
      13,    1,  217,    2, 0x08,   12 /* Private */,
      16,    1,  220,    2, 0x08,   14 /* Private */,
      18,    2,  223,    2, 0x08,   16 /* Private */,
      22,    1,  228,    2, 0x08,   19 /* Private */,
      23,    2,  231,    2, 0x08,   21 /* Private */,
      25,    2,  236,    2, 0x08,   24 /* Private */,
      29,    1,  241,    2, 0x08,   27 /* Private */,
      31,    2,  244,    2, 0x08,   29 /* Private */,
      33,    2,  249,    2, 0x08,   32 /* Private */,
      33,    1,  254,    2, 0x28,   35 /* Private | MethodCloned */,
      36,    2,  257,    2, 0x08,   37 /* Private */,
      36,    1,  262,    2, 0x28,   40 /* Private | MethodCloned */,
      39,    2,  265,    2, 0x08,   42 /* Private */,
      41,    3,  270,    2, 0x08,   45 /* Private */,
      45,    0,  277,    2, 0x08,   49 /* Private */,
      46,    0,  278,    2, 0x08,   50 /* Private */,
      47,    1,  279,    2, 0x08,   51 /* Private */,
      48,    0,  282,    2, 0x08,   53 /* Private */,
      49,    0,  283,    2, 0x08,   54 /* Private */,
      50,    3,  284,    2, 0x08,   55 /* Private */,
      51,    4,  291,    2, 0x08,   59 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 14,   15,
    QMetaType::Void, 0x80000000 | 17,   15,
    QMetaType::Void, 0x80000000 | 19, QMetaType::QString,   20,   21,
    QMetaType::Void, 0x80000000 | 19,   20,
    QMetaType::Void, 0x80000000 | 19, QMetaType::QImage,   20,   24,
    QMetaType::Void, 0x80000000 | 26, QMetaType::QPoint,   27,   28,
    QMetaType::Void, 0x80000000 | 30,   27,
    QMetaType::Void, 0x80000000 | 32, QMetaType::QPoint,   27,   28,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   34,   35,
    QMetaType::Void, QMetaType::QString,   34,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,   37,   38,
    QMetaType::Void, QMetaType::Int,   37,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   40,   34,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int,   42,   43,   44,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   20,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int,   42,   43,   44,
    QMetaType::Void, QMetaType::Int, QMetaType::QString, QMetaType::Int, QMetaType::Bool,   42,   43,   44,   52,

       0        // eod
};

Q_CONSTINIT const QMetaObject MainWindow::staticMetaObject = { {
    QMetaObject::SuperData::link<QMainWindow::staticMetaObject>(),
    qt_meta_stringdata_CLASSMainWindowENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSMainWindowENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSMainWindowENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<MainWindow, std::true_type>,
        // method 'onNewConnection'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDisconnect'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStartServer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onStopServer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAbout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onExit'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleControlPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleStatusBar'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleToolBar'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onToggleFullScreen'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onConnectionStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ConnectionState, std::false_type>,
        // method 'onServerStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ServerState, std::false_type>,
        // method 'onClientConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onClientDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        // method 'onDesktopImageReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ClientId, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QImage &, std::false_type>,
        // method 'onDesktopMouseEvent'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QMouseEvent *, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'onDesktopKeyEvent'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QKeyEvent *, std::false_type>,
        // method 'onDesktopWheelEvent'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QWheelEvent *, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'onStatusMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onStatusMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onProgressUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onProgressUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onErrorMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'onStartServerFromPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onStopServerFromPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onControlPanelSettingsChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onDisconnectClientFromPanel'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'showQuickServerConfig'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSettingsApplied'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onServerConfigFromSettings'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        // method 'onQuickServerConfigChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<bool, std::false_type>
    >,
    nullptr
} };

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->onNewConnection(); break;
        case 1: _t->onDisconnect(); break;
        case 2: _t->onStartServer(); break;
        case 3: _t->onStopServer(); break;
        case 4: _t->onSettings(); break;
        case 5: _t->onAbout(); break;
        case 6: _t->onExit(); break;
        case 7: _t->onToggleControlPanel(); break;
        case 8: _t->onToggleStatusBar(); break;
        case 9: _t->onToggleToolBar(); break;
        case 10: _t->onToggleFullScreen(); break;
        case 11: _t->onConnectionStateChanged((*reinterpret_cast< std::add_pointer_t<ConnectionState>>(_a[1]))); break;
        case 12: _t->onServerStateChanged((*reinterpret_cast< std::add_pointer_t<ServerState>>(_a[1]))); break;
        case 13: _t->onClientConnected((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 14: _t->onClientDisconnected((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1]))); break;
        case 15: _t->onDesktopImageReceived((*reinterpret_cast< std::add_pointer_t<ClientId>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QImage>>(_a[2]))); break;
        case 16: _t->onDesktopMouseEvent((*reinterpret_cast< std::add_pointer_t<QMouseEvent*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 17: _t->onDesktopKeyEvent((*reinterpret_cast< std::add_pointer_t<QKeyEvent*>>(_a[1]))); break;
        case 18: _t->onDesktopWheelEvent((*reinterpret_cast< std::add_pointer_t<QWheelEvent*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 19: _t->onStatusMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[2]))); break;
        case 20: _t->onStatusMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 21: _t->onProgressUpdate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 22: _t->onProgressUpdate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1]))); break;
        case 23: _t->onErrorMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 24: _t->onStartServerFromPanel((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 25: _t->onStopServerFromPanel(); break;
        case 26: _t->onControlPanelSettingsChanged(); break;
        case 27: _t->onDisconnectClientFromPanel((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 28: _t->showQuickServerConfig(); break;
        case 29: _t->onSettingsApplied(); break;
        case 30: _t->onServerConfigFromSettings((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3]))); break;
        case 31: _t->onQuickServerConfigChanged((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2])),(*reinterpret_cast< std::add_pointer_t<int>>(_a[3])),(*reinterpret_cast< std::add_pointer_t<bool>>(_a[4]))); break;
        default: ;
        }
    }
}

const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSMainWindowENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 32)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 32;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 32)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 32;
    }
    return _id;
}
QT_WARNING_POP
