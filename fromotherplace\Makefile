# Makefile for Screenshot Transfer System

# Compiler and flags
CXX = g++
CXXFLAGS = -std=c++20 -Wall -Wextra -O2

# Windows specific settings
ifeq ($(OS),Windows_NT)
    LDFLAGS = -lws2_32 -lgdi32 -luser32
    EXT = .exe
    RM = del /Q
    MKDIR = mkdir
else
    # Linux/Unix settings
    LDFLAGS = -lX11
    EXT = 
    RM = rm -f
    MKDIR = mkdir -p
endif

# Directories
BUILD_DIR = build
SRC_DIR = .

# Targets
SERVER_TARGET = $(BUILD_DIR)/server$(EXT)
CLIENT_TARGET = $(BUILD_DIR)/client$(EXT)

# Source files
SERVER_SRC = $(SRC_DIR)/server.cpp
CLIENT_SRC = $(SRC_DIR)/client.cpp

# Default target
all: $(SERVER_TARGET) $(CLIENT_TARGET)

# Create build directory
$(BUILD_DIR):
	$(MKDIR) $(BUILD_DIR)

# Build server
$(SERVER_TARGET): $(SERVER_SRC) | $(BUILD_DIR)
	$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS)

# Build client
$(CLIENT_TARGET): $(CLIENT_SRC) | $(BUILD_DIR)
	$(CXX) $(CXXFLAGS) -o $@ $< $(LDFLAGS)

# Clean build artifacts
clean:
ifeq ($(OS),Windows_NT)
	if exist $(BUILD_DIR) rmdir /S /Q $(BUILD_DIR)
else
	$(RM) -r $(BUILD_DIR)
endif

# Install (copy to system directory)
install: all
ifeq ($(OS),Windows_NT)
	copy $(SERVER_TARGET) C:\Windows\System32\
	copy $(CLIENT_TARGET) C:\Windows\System32\
else
	sudo cp $(SERVER_TARGET) /usr/local/bin/
	sudo cp $(CLIENT_TARGET) /usr/local/bin/
endif

# Test build
test: all
	@echo "Build successful!"
	@echo "Server: $(SERVER_TARGET)"
	@echo "Client: $(CLIENT_TARGET)"

# Help
help:
	@echo "Available targets:"
	@echo "  all      - Build both server and client"
	@echo "  server   - Build server only"
	@echo "  client   - Build client only"
	@echo "  clean    - Remove build artifacts"
	@echo "  install  - Install to system directory"
	@echo "  test     - Test build"
	@echo "  help     - Show this help"

# Individual targets
server: $(SERVER_TARGET)
client: $(CLIENT_TARGET)

# Phony targets
.PHONY: all clean install test help server client
