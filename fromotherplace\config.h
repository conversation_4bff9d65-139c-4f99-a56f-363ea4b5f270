// config.h - Configuration settings for Screenshot Transfer System
#pragma once

namespace Config {
    // Network settings
    constexpr int DEFAULT_PORT = 8888;
    constexpr const char* DEFAULT_IP = "127.0.0.1";
    
    // Image settings
    constexpr int DEFAULT_QUALITY = 90;
    constexpr int MIN_QUALITY = 1;
    constexpr int MAX_QUALITY = 100;
    
    // Timing settings
    constexpr int DEFAULT_INTERVAL = 5; // seconds
    constexpr int MIN_INTERVAL = 1;
    constexpr int MAX_INTERVAL = 3600;
    
    // Buffer sizes
    constexpr size_t SOCKET_BUFFER_SIZE = 8192;
    constexpr size_t MAX_IMAGE_SIZE = 50 * 1024 * 1024; // 50MB
    
    // File settings
    constexpr const char* SCREENSHOT_DIR = "screenshots";
    constexpr const char* FILE_EXTENSION = ".jpg";
    
    // Network timeouts (milliseconds)
    constexpr int CONNECTION_TIMEOUT = 30000;
    constexpr int RECEIVE_TIMEOUT = 60000;
    constexpr int SEND_TIMEOUT = 30000;
}
