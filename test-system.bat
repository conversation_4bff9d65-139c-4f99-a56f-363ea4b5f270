@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    HVNC System Testing Guide
echo ========================================
echo.

:: Set colors for output
set "GREEN=[92m"
set "RED=[91m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "NC=[0m"

echo %CYAN%This script will guide you through testing the HVNC system%NC%
echo %CYAN%with the server and client on different computers.%NC%
echo.

:MAIN_MENU
echo ========================================
echo    Testing Options
echo ========================================
echo.
echo 1. %BLUE%Pre-Test Checklist%NC%
echo 2. %BLUE%Network Connectivity Test%NC%
echo 3. %BLUE%Server Setup Instructions%NC%
echo 4. %BLUE%Client Setup Instructions%NC%
echo 5. %BLUE%Firewall Configuration%NC%
echo 6. %BLUE%Run Complete Test Sequence%NC%
echo 7. %BLUE%Troubleshooting Guide%NC%
echo 8. %BLUE%Exit%NC%
echo.
set /p choice="Select an option (1-8): "

if "%choice%"=="1" goto PRE_TEST
if "%choice%"=="2" goto NETWORK_TEST
if "%choice%"=="3" goto SERVER_SETUP
if "%choice%"=="4" goto CLIENT_SETUP
if "%choice%"=="5" goto FIREWALL_CONFIG
if "%choice%"=="6" goto COMPLETE_TEST
if "%choice%"=="7" goto TROUBLESHOOTING
if "%choice%"=="8" goto EXIT

echo %RED%[ERROR]%NC% Invalid choice!
echo.
goto MAIN_MENU

:PRE_TEST
cls
echo ========================================
echo    Pre-Test Checklist
echo ========================================
echo.
echo %YELLOW%Before testing, ensure you have:%NC%
echo.
echo %GREEN%✓%NC% Built both Server.exe and Client.exe successfully
echo %GREEN%✓%NC% Two computers with network connectivity
echo %GREEN%✓%NC% Administrator privileges on both computers
echo %GREEN%✓%NC% Windows Firewall configured or temporarily disabled
echo %GREEN%✓%NC% Antivirus software configured to allow the executables
echo %GREEN%✓%NC% Client configured with correct server IP address
echo.
echo %BLUE%Required Information:%NC%
echo - Server computer IP address
echo - Port number for communication (e.g., 4043)
echo - Network subnet (e.g., 192.168.1.x)
echo.
echo %YELLOW%Security Warning:%NC%
echo %RED%This software is for educational purposes only!%NC%
echo %RED%Only use in controlled environments with proper authorization!%NC%
echo.
pause
goto MAIN_MENU

:NETWORK_TEST
cls
echo ========================================
echo    Network Connectivity Test
echo ========================================
echo.
echo %BLUE%Step 1: Get Server Computer Information%NC%
echo.
echo On the SERVER computer, run these commands:
echo.
echo %CYAN%ipconfig%NC%
echo   ^(Note the IPv4 Address^)
echo.
echo %CYAN%hostname%NC%
echo   ^(Note the computer name^)
echo.
echo Example output:
echo   IPv4 Address: *************
echo   Computer Name: SERVER-PC
echo.

set /p SERVER_IP="Enter the server IP address: "
if "%SERVER_IP%"=="" (
    echo %RED%[ERROR]%NC% IP address required for testing!
    pause
    goto MAIN_MENU
)

echo.
echo %BLUE%Step 2: Test Connectivity from Client Computer%NC%
echo.
echo On the CLIENT computer, run:
echo.
echo %CYAN%ping %SERVER_IP%%NC%
echo.
echo Expected result: Reply from %SERVER_IP%
echo.
echo %YELLOW%If ping fails:%NC%
echo - Check network cables/WiFi connection
echo - Verify both computers are on same network
echo - Check Windows Firewall settings
echo - Try pinging in reverse direction
echo.

set /p ping_result="Did the ping test succeed? (y/n): "
if /i "%ping_result%"=="y" (
    echo %GREEN%[SUCCESS]%NC% Network connectivity confirmed!
) else (
    echo %RED%[FAILED]%NC% Network connectivity issues detected.
    echo Please resolve network issues before proceeding.
)
echo.
pause
goto MAIN_MENU

:SERVER_SETUP
cls
echo ========================================
echo    Server Setup Instructions
echo ========================================
echo.
echo %BLUE%Step 1: Prepare Server Computer%NC%
echo.
echo 1. Copy Server.exe to the server computer
echo 2. Right-click Server.exe and select "Run as administrator"
echo 3. When prompted, enter the port number (e.g., 4043)
echo.
echo %YELLOW%Expected Server Output:%NC%
echo   [-] Starting HVNC Server...
echo   [+] Server Started!
echo   [+] Listening on Port: 4043
echo.
echo %BLUE%Step 2: Verify Server is Running%NC%
echo.
echo On the server computer, open Command Prompt and run:
echo %CYAN%netstat -an ^| findstr :4043%NC%
echo.
echo Expected output:
echo   TCP    0.0.0.0:4043           0.0.0.0:0              LISTENING
echo.
echo %GREEN%If you see LISTENING status, the server is ready!%NC%
echo.
echo %BLUE%Step 3: Note Server Information%NC%
echo.
echo Write down:
echo - Server IP Address: ________________
echo - Server Port: ________________
echo - Server Status: ________________
echo.
pause
goto MAIN_MENU

:CLIENT_SETUP
cls
echo ========================================
echo    Client Setup Instructions
echo ========================================
echo.
echo %BLUE%Step 1: Configure Client%NC%
echo.
echo Before copying to client computer, ensure:
echo 1. Client source code has correct server IP
echo 2. Client has been rebuilt after IP change
echo 3. Use configure-client.bat if needed
echo.
echo %BLUE%Step 2: Prepare Client Computer%NC%
echo.
echo 1. Copy Client.exe to the client computer
echo 2. Right-click Client.exe and select "Run as administrator"
echo.
echo %YELLOW%Expected Client Behavior:%NC%
echo - Console window appears briefly then hides
echo - No visible interface on client computer
echo - Connection established to server
echo.
echo %BLUE%Step 3: Verify Connection on Server%NC%
echo.
echo On the SERVER computer, you should see:
echo - A new "Hidden Desktop" window appears
echo - Window title shows connection information
echo - Right-click on white bar shows menu options
echo.
echo %GREEN%If Hidden Desktop window appears, connection successful!%NC%
echo.
pause
goto MAIN_MENU

:FIREWALL_CONFIG
cls
echo ========================================
echo    Firewall Configuration
echo ========================================
echo.
echo %BLUE%Windows Firewall Configuration for Server:%NC%
echo.
echo %YELLOW%Method 1: Allow through Windows Firewall%NC%
echo 1. Open Windows Defender Firewall
echo 2. Click "Allow an app or feature through Windows Defender Firewall"
echo 3. Click "Change Settings" then "Allow another app..."
echo 4. Browse and select Server.exe
echo 5. Check both "Private" and "Public" networks
echo 6. Click OK
echo.
echo %YELLOW%Method 2: Create Inbound Rule%NC%
echo 1. Open Windows Defender Firewall with Advanced Security
echo 2. Click "Inbound Rules" then "New Rule..."
echo 3. Select "Port" and click Next
echo 4. Select "TCP" and enter your port number (e.g., 4043)
echo 5. Select "Allow the connection"
echo 6. Apply to all network types
echo 7. Name the rule "HVNC Server"
echo.
echo %YELLOW%Method 3: Temporary Disable (Testing Only)%NC%
echo %RED%WARNING: Only for testing in secure environment!%NC%
echo 1. Open Windows Defender Firewall
echo 2. Click "Turn Windows Defender Firewall on or off"
echo 3. Temporarily turn off for Private and Public networks
echo 4. %RED%Remember to re-enable after testing!%NC%
echo.
echo %BLUE%For Client Computer:%NC%
echo Usually no firewall changes needed for outbound connections.
echo.
pause
goto MAIN_MENU

:COMPLETE_TEST
cls
echo ========================================
echo    Complete Test Sequence
echo ========================================
echo.
echo %CYAN%This will guide you through the complete testing process%NC%
echo.

echo %BLUE%Phase 1: Pre-Test Verification%NC%
echo.
set /p ready1="Are both executables built and ready? (y/n): "
if /i not "%ready1%"=="y" (
    echo Please build the executables first using build.bat
    pause
    goto MAIN_MENU
)

set /p ready2="Are both computers connected to the network? (y/n): "
if /i not "%ready2%"=="y" (
    echo Please ensure network connectivity first
    pause
    goto MAIN_MENU
)

echo.
echo %BLUE%Phase 2: Server Startup%NC%
echo.
echo 1. On SERVER computer: Run Server.exe as administrator
echo 2. Enter port number when prompted (e.g., 4043)
echo 3. Verify "Server Started!" message appears
echo.
set /p server_ready="Is the server running and listening? (y/n): "
if /i not "%server_ready%"=="y" (
    echo Please start the server first
    pause
    goto MAIN_MENU
)

echo.
echo %BLUE%Phase 3: Client Connection%NC%
echo.
echo 1. On CLIENT computer: Run Client.exe as administrator
echo 2. Client console should hide automatically
echo 3. Check server computer for Hidden Desktop window
echo.
set /p client_connected="Did the Hidden Desktop window appear on server? (y/n): "
if /i "%client_connected%"=="y" (
    echo %GREEN%[SUCCESS]%NC% Connection established successfully!
    echo.
    echo %BLUE%Phase 4: Functionality Test%NC%
    echo.
    echo On the server computer:
    echo 1. Right-click the white bar of Hidden Desktop window
    echo 2. Try "Start Explorer" option
    echo 3. Try "Start Chrome" or other browser options
    echo 4. Test various menu options
    echo.
    set /p functions_work="Do the menu functions work correctly? (y/n): "
    if /i "%functions_work%"=="y" (
        echo.
        echo %GREEN%========================================%NC%
        echo %GREEN%    TEST COMPLETED SUCCESSFULLY!%NC%
        echo %GREEN%========================================%NC%
        echo.
        echo %CYAN%Your HVNC system is working correctly!%NC%
        echo.
        echo %YELLOW%Next Steps:%NC%
        echo - Document your configuration
        echo - Test additional features as needed
        echo - Ensure proper security measures
        echo.
    ) else (
        echo %YELLOW%[PARTIAL SUCCESS]%NC% Connection works but some functions may need debugging
    )
) else (
    echo %RED%[FAILED]%NC% Connection not established
    echo.
    echo %YELLOW%Troubleshooting steps:%NC%
    echo 1. Check firewall settings
    echo 2. Verify IP address configuration
    echo 3. Confirm port number matches
    echo 4. Check antivirus software
    echo 5. Try running both as administrator
)

echo.
pause
goto MAIN_MENU

:TROUBLESHOOTING
cls
echo ========================================
echo    Troubleshooting Guide
echo ========================================
echo.
echo %RED%Problem: Server won't start%NC%
echo %YELLOW%Solutions:%NC%
echo - Run as administrator
echo - Check if port is already in use: netstat -an ^| findstr :PORT
echo - Try a different port number
echo - Disable antivirus temporarily
echo.
echo %RED%Problem: Client can't connect%NC%
echo %YELLOW%Solutions:%NC%
echo - Verify server IP address in client code
echo - Check network connectivity with ping
echo - Ensure server is running first
echo - Check Windows Firewall settings
echo - Try telnet SERVER_IP PORT to test connection
echo.
echo %RED%Problem: Hidden Desktop window doesn't appear%NC%
echo %YELLOW%Solutions:%NC%
echo - Check if client process is running in Task Manager
echo - Verify server console shows connection
echo - Try running client as administrator
echo - Check for error messages in server console
echo.
echo %RED%Problem: Menu functions don't work%NC%
echo %YELLOW%Solutions:%NC%
echo - Ensure client has proper permissions
echo - Check if target applications are installed
echo - Try different menu options
echo - Verify Windows version compatibility
echo.
echo %RED%Problem: Connection drops frequently%NC%
echo %YELLOW%Solutions:%NC%
echo - Check network stability
echo - Verify firewall isn't blocking
echo - Try wired connection instead of WiFi
echo - Check for power management settings
echo.
echo %BLUE%Advanced Debugging:%NC%
echo.
echo 1. %CYAN%Check processes:%NC%
echo    Task Manager ^> Details ^> Look for Server.exe and Client.exe
echo.
echo 2. %CYAN%Monitor network traffic:%NC%
echo    netstat -an ^| findstr PORT_NUMBER
echo.
echo 3. %CYAN%Check event logs:%NC%
echo    Event Viewer ^> Windows Logs ^> Application
echo.
pause
goto MAIN_MENU

:EXIT
echo.
echo %GREEN%Thank you for using the HVNC Testing Guide!%NC%
echo.
echo %YELLOW%Remember:%NC%
echo - Use this software responsibly
echo - Only in authorized environments
echo - For educational purposes only
echo.
echo %BLUE%For additional help:%NC%
echo - Check the README.md file
echo - Review the source code comments
echo - Test in a controlled environment first
echo.
pause
exit /b 0
