# HVNC Design - Cross-Platform Remote Desktop GUI

A modern, cross-platform Qt6-based graphical user interface for HVNC (Hidden VNC) remote desktop functionality.

## 🚀 Features

### ✅ **Successfully Implemented**
- **Cross-Platform Support**: Windows, Linux, and macOS
- **Modern Qt6 Interface**: Built with Qt6 and C++20
- **Dark Theme UI**: Professional black color scheme with proper spacing
- **Desktop Viewer**: Real-time remote desktop display with scaling options
- **Server Management**: Start/stop HVNC server with configuration
- **Connection Management**: Handle client connections with authentication
- **Image Processing**: Compression and optimization for remote display
- **Style Management**: Consistent theming across all components
- **Status Monitoring**: Real-time connection and performance statistics

### 🎨 **UI Components**
- **Main Window**: Central interface with menu bar, toolbar, and status bar
- **Desktop Viewer**: Graphics view for remote desktop display
- **Control Panels**: Server and connection configuration
- **Status Indicators**: Connection state and performance metrics
- **Context Menus**: Right-click functionality for quick actions

## 🛠️ Build Instructions

### Prerequisites
- **Qt6** (6.2.4 or later)
- **C++20** compatible compiler (GCC 11+, Clang 12+, MSVC 2019+)
- **CMake** or **qmake6**

### Linux Build
```bash
# Install Qt6 development packages
sudo apt-get install qt6-base-dev qt6-multimedia-dev

# Navigate to Design directory
cd Design/

# Generate Makefile
qmake6 HVNCDesign.pro

# Build the application
make

# Run the GUI
./build/release/HVNCDesign
# or use the launcher script
./run_hvnc_gui.sh
```

### Windows Build
```cmd
# Ensure Qt6 is in PATH
# Open Qt6 command prompt

# Navigate to Design directory
cd Design\

# Generate Makefile
qmake6 HVNCDesign.pro

# Build the application
nmake  # or mingw32-make

# Run the GUI
build\release\HVNCDesign.exe
```

### macOS Build
```bash
# Install Qt6 via Homebrew
brew install qt6

# Navigate to Design directory
cd Design/

# Generate Makefile
qmake6 HVNCDesign.pro

# Build the application
make

# Run the GUI
./build/release/HVNCDesign
```

## 📁 Project Structure

```
Design/
├── include/                 # Header files
│   ├── MainWindow.h        # Main application window
│   ├── DesktopViewer.h     # Remote desktop display
│   ├── ServerManager.h     # HVNC server management
│   ├── ConnectionManager.h # Client connection handling
│   ├── StyleManager.h      # UI theming and styles
│   ├── ImageProcessor.h    # Image compression/processing
│   └── Common.h           # Common definitions and utilities
├── src/                    # Source files
│   ├── main.cpp           # Application entry point
│   ├── MainWindow.cpp     # Main window implementation
│   ├── DesktopViewer.cpp  # Desktop viewer implementation
│   ├── ServerManager.cpp  # Server management logic
│   ├── ConnectionManager.cpp # Connection handling
│   ├── StyleManager.cpp   # Style and theme management
│   └── ImageProcessor.cpp # Image processing algorithms
├── build/                  # Build output directory
│   └── release/
│       └── HVNCDesign     # Final executable
├── HVNCDesign.pro         # Qt project file
├── run_hvnc_gui.sh       # Launch script
└── README.md             # This file
```

## 🎯 Usage

1. **Launch the Application**:
   ```bash
   ./build/release/HVNCDesign
   ```

2. **Start HVNC Server**:
   - Use the Server menu to configure and start the HVNC server
   - Set port and authentication options

3. **View Remote Desktop**:
   - The desktop viewer will display connected client screens
   - Use mouse and keyboard for remote control

4. **Monitor Connections**:
   - Status bar shows connection information
   - View menu provides connection statistics

## 🔧 Configuration

The application supports various configuration options:

- **Server Settings**: Port, authentication, encryption
- **Display Options**: Scaling modes, quality settings
- **Theme Settings**: Dark/light themes, custom colors
- **Performance**: Compression levels, frame rates

## 🌟 Technical Highlights

- **Modern C++20**: Uses latest C++ features for performance
- **Qt6 Framework**: Cross-platform GUI with native look and feel
- **Modular Design**: Clean separation of concerns
- **Memory Safe**: RAII and smart pointers throughout
- **Thread Safe**: Proper synchronization for multi-threading
- **Scalable Architecture**: Easy to extend and maintain

## 📊 Build Status

✅ **Successfully Built and Tested**
- Compilation: PASSED
- Linking: PASSED  
- Runtime: PASSED
- GUI Display: PASSED
- Component Initialization: PASSED

## 🐛 Known Issues

- OpenGL dependencies resolved for Linux builds
- Cross-platform compatibility verified
- All major components implemented and functional

## 📝 License

This project is part of the HVNC suite. Please refer to the main project license.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

---

**Built with ❤️ using Qt6 and C++20**
