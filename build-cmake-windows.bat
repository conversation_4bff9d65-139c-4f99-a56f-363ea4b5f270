@echo off
:: ============================================================================
:: HVNC CMake Windows Build System - Qt6 GUI with CMake
:: ============================================================================
:: Builds the HVNC Design/ GUI for Windows using CMake (bypasses qmake issues)
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC CMake Windows Build System                          ║%NC%
echo %BLUE%║                    Qt6 GUI with CMake (No qmake Issues!)                    ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Check for CMake
echo %CYAN%[STEP 1]%NC% Checking for CMake...

where cmake >nul 2>&1
if errorlevel 1 (
    echo %RED%[ERROR]%NC% CMake not found in PATH
    echo %YELLOW%[INSTALL]%NC% Please install CMake from: https://cmake.org/download/
    pause
    exit /b 1
)

for /f "tokens=3" %%i in ('cmake --version 2^>nul ^| findstr "cmake version"') do set CMAKE_VERSION=%%i
echo %GREEN%[FOUND]%NC% CMake version: %CMAKE_VERSION%

:: Step 2: Setup Qt6 environment
echo %CYAN%[STEP 2]%NC% Setting up Qt6 environment...

set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64"
set "CMAKE_PREFIX_PATH=%QT6_PATH%"
set "PATH=%QT6_PATH%\bin;%PATH%"

echo %GREEN%[CONFIG]%NC% Qt6 path: %QT6_PATH%

if not exist "%QT6_PATH%\bin\qmake.exe" (
    echo %RED%[ERROR]%NC% Qt6 not found at %QT6_PATH%
    pause
    exit /b 1
)

:: Step 3: Setup Visual Studio environment
echo %CYAN%[STEP 3]%NC% Setting up Visual Studio environment...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to setup Visual Studio environment
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Visual Studio 2022 environment loaded

:: Step 4: Navigate to Design directory and prepare build
echo %CYAN%[STEP 4]%NC% Preparing CMake build...

cd /d "%~dp0Design"

:: Clean previous builds
if exist "build" rmdir /s /q "build" 2>nul
mkdir "build" 2>nul
cd "build"

echo %GREEN%[CLEAN]%NC% Build directory prepared

:: Step 5: Configure with CMake
echo %CYAN%[STEP 5]%NC% Configuring with CMake...

cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_PREFIX_PATH="%CMAKE_PREFIX_PATH%" ^
    -DQt6_DIR="%QT6_PATH%\lib\cmake\Qt6"

if errorlevel 1 (
    echo %RED%[FAILED]%NC% CMake configuration failed
    cd /d "%~dp0"
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% CMake configuration completed

:: Step 6: Build with CMake
echo %CYAN%[STEP 6]%NC% Building with CMake...

cmake --build . --config Release --parallel

if errorlevel 1 (
    echo %RED%[FAILED]%NC% CMake build failed
    cd /d "%~dp0"
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% CMake build completed successfully!

:: Step 7: Deploy and verify
echo %CYAN%[STEP 7]%NC% Deploying Windows executable...

:: Check for executable in different possible locations
set "EXE_FOUND=false"
set "EXE_PATH="

if exist "Release\HVNCDesign.exe" (
    set "EXE_PATH=Release\HVNCDesign.exe"
    set "EXE_FOUND=true"
) else if exist "bin\HVNCDesign.exe" (
    set "EXE_PATH=bin\HVNCDesign.exe"
    set "EXE_FOUND=true"
) else if exist "HVNCDesign.exe" (
    set "EXE_PATH=HVNCDesign.exe"
    set "EXE_FOUND=true"
)

if "%EXE_FOUND%"=="true" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe found at: %EXE_PATH%
    
    :: Copy to root directory
    copy "%EXE_PATH%" "..\..\HVNCDesign.exe" >nul
    
    if exist "..\..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
        
        :: Get file info
        for %%F in ("%EXE_PATH%") do (
            echo %CYAN%[INFO]%NC% File size: %%~zF bytes
        )
        
        :: Deploy Qt6 dependencies
        echo %CYAN%[DEPLOY]%NC% Deploying Qt6 dependencies...
        "%QT6_PATH%\bin\windeployqt.exe" "%EXE_PATH%" --release --no-translations --no-system-d3d-compiler --no-opengl-sw
        
        if errorlevel 1 (
            echo %YELLOW%[WARNING]%NC% windeployqt failed, but executable should still work
        ) else (
            echo %GREEN%[SUCCESS]%NC% Qt6 dependencies deployed
        )
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found after build
    echo %YELLOW%[DEBUG]%NC% Checking build directory contents...
    dir /s "*.exe" 2>nul
)

:: Return to root directory
cd /d "%~dp0"

:: Success summary
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        CMAKE WINDOWS BUILD COMPLETED!                       ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Applications:%NC%
if exist "HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for Windows (CMake build)
) else (
    echo   %RED%✗%NC% HVNCDesign.exe - Build failed
)

if exist "Client.exe" (
    echo   %GREEN%✓%NC% Client.exe - Hidden HVNC agent
) else (
    echo   %YELLOW%⚠%NC% Client.exe - Not found (run main build.bat to create)
)

echo.
echo %CYAN%Integration Test:%NC%
if exist "HVNCDesign.exe" (
    echo   %WHITE%1.%NC% Run HVNCDesign.exe to open the Windows Qt6 GUI
    echo   %WHITE%2.%NC% Use "Server" menu to start HVNC server (port 4444)
    echo   %WHITE%3.%NC% Run Client.exe on target computer to connect
    echo   %WHITE%4.%NC% Desktop images will appear in the GUI viewer
    echo   %WHITE%5.%NC% Control remote desktop through the interface!
    
    echo.
    echo %CYAN%[TEST]%NC% Would you like to test launch the Windows GUI? (y/n)
    set /p "test_launch=Enter choice: "
    
    if /i "!test_launch!"=="y" (
        echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe for Windows...
        start "" "HVNCDesign.exe"
        echo %GREEN%[SUCCESS]%NC% Windows Qt6 GUI launched!
    )
)

echo.
echo %YELLOW%[ARCHITECTURE]%NC% 
echo   • HVNCDesign.exe: Qt6 GUI controller (YOUR computer)
echo   • Client.exe: Hidden agent (TARGET computer - captures desktop)
echo   • Data flow: Client captures → GUI displays (CORRECT!)

echo.
echo %GREEN%[COMPLETE]%NC% CMake Windows build finished!
echo %CYAN%[NOTE]%NC% This build bypassed the qmake MSVC version issues using CMake!
pause
