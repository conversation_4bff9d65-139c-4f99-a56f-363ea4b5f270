#pragma once

#include <string>
#include <string_view>
#include <vector>
#include <memory>
#include <concepts>
#include <algorithm>
#include <Windows.h>

namespace hvnc {

// Concepts for string types
template<typename T>
concept StringLike = std::convertible_to<T, std::string_view> || 
                     std::convertible_to<T, std::wstring_view>;

template<typename T>
concept CharType = std::same_as<T, char> || std::same_as<T, wchar_t>;

// Modern string utilities class
class string_utils {
public:
    // Convert between ANSI and Unicode
    [[nodiscard]] static std::wstring to_wide(std::string_view str) {
        if (str.empty()) return {};
        
        int size_needed = MultiByteToWideChar(CP_UTF8, 0, str.data(), 
                                            static_cast<int>(str.size()), nullptr, 0);
        if (size_needed <= 0) return {};
        
        std::wstring result(size_needed, 0);
        MultiByteToWideChar(CP_UTF8, 0, str.data(), static_cast<int>(str.size()), 
                           result.data(), size_needed);
        return result;
    }

    [[nodiscard]] static std::string to_narrow(std::wstring_view wstr) {
        if (wstr.empty()) return {};
        
        int size_needed = WideCharToMultiByte(CP_UTF8, 0, wstr.data(), 
                                            static_cast<int>(wstr.size()), nullptr, 0, nullptr, nullptr);
        if (size_needed <= 0) return {};
        
        std::string result(size_needed, 0);
        WideCharToMultiByte(CP_UTF8, 0, wstr.data(), static_cast<int>(wstr.size()), 
                           result.data(), size_needed, nullptr, nullptr);
        return result;
    }

    // Safe string concatenation
    template<StringLike... Args>
    [[nodiscard]] static std::string concat(Args&&... args) {
        std::string result;
        result.reserve((std::string_view{args}.size() + ...));
        (result.append(std::string_view{args}), ...);
        return result;
    }

    // Safe string formatting (C++20 style)
    template<typename... Args>
    [[nodiscard]] static std::string format(std::string_view fmt, Args&&... args) {
        // For now, use sprintf-style formatting until std::format is widely available
        constexpr size_t buffer_size = 4096;
        auto buffer = std::make_unique<char[]>(buffer_size);
        
        int result = snprintf(buffer.get(), buffer_size, fmt.data(), args...);
        if (result < 0) return {};
        
        if (static_cast<size_t>(result) >= buffer_size) {
            // Buffer too small, allocate larger one
            auto larger_buffer = std::make_unique<char[]>(result + 1);
            snprintf(larger_buffer.get(), result + 1, fmt.data(), args...);
            return std::string{larger_buffer.get(), static_cast<size_t>(result)};
        }
        
        return std::string{buffer.get(), static_cast<size_t>(result)};
    }

    // Case-insensitive comparison
    [[nodiscard]] static bool iequals(std::string_view a, std::string_view b) noexcept {
        return std::equal(a.begin(), a.end(), b.begin(), b.end(),
                         [](char ca, char cb) { return std::tolower(ca) == std::tolower(cb); });
    }

    // String splitting
    [[nodiscard]] static std::vector<std::string> split(std::string_view str, char delimiter) {
        std::vector<std::string> result;
        size_t start = 0;
        size_t end = str.find(delimiter);
        
        while (end != std::string_view::npos) {
            result.emplace_back(str.substr(start, end - start));
            start = end + 1;
            end = str.find(delimiter, start);
        }
        
        result.emplace_back(str.substr(start));
        return result;
    }

    // Trim whitespace
    [[nodiscard]] static std::string_view trim(std::string_view str) noexcept {
        auto start = str.find_first_not_of(" \t\r\n");
        if (start == std::string_view::npos) return {};
        
        auto end = str.find_last_not_of(" \t\r\n");
        return str.substr(start, end - start + 1);
    }

    // Replace all occurrences
    [[nodiscard]] static std::string replace_all(std::string_view str, 
                                                std::string_view from, 
                                                std::string_view to) {
        if (from.empty()) return std::string{str};
        
        std::string result;
        result.reserve(str.size());
        
        size_t start = 0;
        size_t pos = str.find(from);
        
        while (pos != std::string_view::npos) {
            result.append(str.substr(start, pos - start));
            result.append(to);
            start = pos + from.size();
            pos = str.find(from, start);
        }
        
        result.append(str.substr(start));
        return result;
    }

    // Check if string starts with prefix
    [[nodiscard]] static bool starts_with(std::string_view str, std::string_view prefix) noexcept {
        return str.size() >= prefix.size() && str.substr(0, prefix.size()) == prefix;
    }

    // Check if string ends with suffix
    [[nodiscard]] static bool ends_with(std::string_view str, std::string_view suffix) noexcept {
        return str.size() >= suffix.size() && 
               str.substr(str.size() - suffix.size()) == suffix;
    }

    // Convert to uppercase
    [[nodiscard]] static std::string to_upper(std::string_view str) {
        std::string result{str};
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](char c) { return static_cast<char>(std::toupper(c)); });
        return result;
    }

    // Convert to lowercase
    [[nodiscard]] static std::string to_lower(std::string_view str) {
        std::string result{str};
        std::transform(result.begin(), result.end(), result.begin(), 
                      [](char c) { return static_cast<char>(std::tolower(c)); });
        return result;
    }
};

// RAII wrapper for Windows strings
class windows_string {
private:
    std::unique_ptr<char[]> buffer_;
    size_t size_;

public:
    explicit windows_string(size_t size) 
        : buffer_(std::make_unique<char[]>(size + 1)), size_(size) {
        buffer_[0] = '\0';
    }

    windows_string(const windows_string&) = delete;
    windows_string& operator=(const windows_string&) = delete;

    windows_string(windows_string&&) = default;
    windows_string& operator=(windows_string&&) = default;

    [[nodiscard]] char* data() noexcept { return buffer_.get(); }
    [[nodiscard]] const char* data() const noexcept { return buffer_.get(); }
    [[nodiscard]] size_t capacity() const noexcept { return size_; }
    [[nodiscard]] size_t length() const noexcept { return strlen(buffer_.get()); }
    
    [[nodiscard]] std::string_view view() const noexcept { 
        return std::string_view{buffer_.get(), length()}; 
    }
    
    [[nodiscard]] std::string str() const { 
        return std::string{buffer_.get(), length()}; 
    }

    // Implicit conversion to const char* for Windows API calls
    operator const char*() const noexcept { return buffer_.get(); }
    operator char*() noexcept { return buffer_.get(); }
};

// Factory function for creating Windows-compatible string buffers
[[nodiscard]] inline windows_string make_windows_string(size_t size) {
    return windows_string{size};
}

// Path manipulation utilities
class path_utils {
public:
    [[nodiscard]] static std::string combine(std::string_view path1, std::string_view path2) {
        if (path1.empty()) return std::string{path2};
        if (path2.empty()) return std::string{path1};
        
        std::string result{path1};
        if (result.back() != '\\' && result.back() != '/') {
            result += '\\';
        }
        
        // Skip leading separators in path2
        size_t start = 0;
        while (start < path2.size() && (path2[start] == '\\' || path2[start] == '/')) {
            ++start;
        }
        
        result.append(path2.substr(start));
        return result;
    }

    [[nodiscard]] static std::string get_directory(std::string_view path) {
        auto pos = path.find_last_of("\\/");
        if (pos == std::string_view::npos) return {};
        return std::string{path.substr(0, pos)};
    }

    [[nodiscard]] static std::string get_filename(std::string_view path) {
        auto pos = path.find_last_of("\\/");
        if (pos == std::string_view::npos) return std::string{path};
        return std::string{path.substr(pos + 1)};
    }

    [[nodiscard]] static std::string get_extension(std::string_view path) {
        auto filename = get_filename(path);
        auto pos = filename.find_last_of('.');
        if (pos == std::string::npos) return {};
        return filename.substr(pos);
    }

    [[nodiscard]] static std::string remove_extension(std::string_view path) {
        auto pos = path.find_last_of('.');
        auto sep_pos = path.find_last_of("\\/");
        
        // Make sure the dot is in the filename, not in a directory name
        if (pos != std::string_view::npos && 
            (sep_pos == std::string_view::npos || pos > sep_pos)) {
            return std::string{path.substr(0, pos)};
        }
        
        return std::string{path};
    }
};

} // namespace hvnc
