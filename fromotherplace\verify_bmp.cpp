// verify_bmp.cpp - Simple BMP file verification
#include <iostream>
#include <fstream>
#include <vector>

struct BMPHeader {
    char signature[2];
    uint32_t fileSize;
    uint32_t reserved;
    uint32_t dataOffset;
    uint32_t headerSize;
    uint32_t width;
    uint32_t height;
    uint16_t planes;
    uint16_t bitsPerPixel;
    uint32_t compression;
    uint32_t imageSize;
    uint32_t xPixelsPerMeter;
    uint32_t yPixelsPerMeter;
    uint32_t colorsUsed;
    uint32_t importantColors;
};

int main(int argc, char* argv[]) {
    if (argc != 2) {
        std::cout << "Usage: verify_bmp.exe <bmp_file>\n";
        return 1;
    }

    std::ifstream file(argv[1], std::ios::binary);
    if (!file) {
        std::cout << "Error: Cannot open file " << argv[1] << "\n";
        return 1;
    }

    BMPHeader header;
    file.read(reinterpret_cast<char*>(&header), sizeof(header));

    if (file.gcount() != sizeof(header)) {
        std::cout << "Error: File too small to contain BMP header\n";
        return 1;
    }

    std::cout << "BMP File Analysis for: " << argv[1] << "\n";
    std::cout << "========================\n";
    std::cout << "Signature: " << header.signature[0] << header.signature[1] << "\n";
    std::cout << "File Size: " << header.fileSize << " bytes\n";
    std::cout << "Data Offset: " << header.dataOffset << "\n";
    std::cout << "Header Size: " << header.headerSize << "\n";
    std::cout << "Width: " << header.width << " pixels\n";
    std::cout << "Height: " << header.height << " pixels\n";
    std::cout << "Planes: " << header.planes << "\n";
    std::cout << "Bits Per Pixel: " << header.bitsPerPixel << "\n";
    std::cout << "Compression: " << header.compression << "\n";
    std::cout << "Image Size: " << header.imageSize << " bytes\n";

    // Verify signature
    if (header.signature[0] != 'B' || header.signature[1] != 'M') {
        std::cout << "\n❌ ERROR: Invalid BMP signature!\n";
        return 1;
    }

    // Get actual file size
    file.seekg(0, std::ios::end);
    size_t actualSize = file.tellg();
    
    std::cout << "\nActual file size: " << actualSize << " bytes\n";
    
    if (actualSize != header.fileSize) {
        std::cout << "⚠️  WARNING: File size mismatch!\n";
    }

    if (header.width > 0 && header.height > 0 && header.bitsPerPixel == 24) {
        std::cout << "\n✅ BMP file appears to be valid!\n";
        std::cout << "Resolution: " << header.width << "x" << header.height << "\n";
        std::cout << "Color depth: 24-bit RGB\n";
    } else {
        std::cout << "\n❌ BMP file has invalid parameters!\n";
    }

    return 0;
}
