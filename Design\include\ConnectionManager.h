/**
 * @file ConnectionManager.h
 * @brief Connection management for HVNC client connections
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This class manages individual client connections, handles connection
 * establishment, authentication, and maintains connection state.
 */

#pragma once

#include "Common.h"
#include <QTcpSocket>
#include <QTimer>

/**
 * @class ConnectionManager
 * @brief Manages individual client connections
 * 
 * The ConnectionManager class handles the lifecycle of individual client
 * connections, including connection establishment, authentication,
 * keep-alive mechanisms, and graceful disconnection.
 */
class ConnectionManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Connection configuration structure
     */
    struct ConnectionConfig {
        QString hostAddress;            ///< Target host address
        PortNumber port;                ///< Target port
        int connectTimeout;             ///< Connection timeout (ms)
        int keepAliveInterval;          ///< Keep-alive interval (ms)
        bool useAuthentication;         ///< Use authentication
        QString username;               ///< Authentication username
        QString password;               ///< Authentication password
        bool useEncryption;             ///< Use encryption
        QString encryptionKey;          ///< Encryption key
    };

    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit ConnectionManager(QObject* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~ConnectionManager() override;

    /**
     * @brief Connect to remote host
     * @param config Connection configuration
     * @return True if connection initiated successfully
     */
    bool connectToHost(const ConnectionConfig& config);

    /**
     * @brief Disconnect from remote host
     */
    void disconnect();

    /**
     * @brief Get current connection state
     * @return Current connection state
     */
    ConnectionState getConnectionState() const { return m_connectionState; }

    /**
     * @brief Get connection configuration
     * @return Current connection configuration
     */
    const ConnectionConfig& getConnectionConfig() const { return m_config; }

    /**
     * @brief Check if connected
     * @return True if connected
     */
    bool isConnected() const { return m_connectionState == ConnectionState::Connected; }

    /**
     * @brief Get connection duration
     * @return Connection duration in milliseconds
     */
    qint64 getConnectionDuration() const;

    /**
     * @brief Get remote host information
     * @return Remote host info string
     */
    QString getRemoteHostInfo() const;

    /**
     * @brief Send data to remote host
     * @param data Data to send
     * @return True if data sent successfully
     */
    bool sendData(const QByteArray& data);

    /**
     * @brief Get connection statistics
     * @return Connection statistics
     */
    QMap<QString, QVariant> getConnectionStatistics() const;

    /**
     * @brief Set keep-alive enabled
     * @param enabled True to enable keep-alive
     */
    void setKeepAliveEnabled(bool enabled);

    /**
     * @brief Check if keep-alive is enabled
     * @return True if keep-alive is enabled
     */
    bool isKeepAliveEnabled() const { return m_keepAliveEnabled; }

signals:
    /**
     * @brief Emitted when connection state changes
     * @param state New connection state
     */
    void connectionStateChanged(ConnectionState state);

    /**
     * @brief Emitted when data is received
     * @param data Received data
     */
    void dataReceived(const QByteArray& data);

    /**
     * @brief Emitted when connection is established
     */
    void connected();

    /**
     * @brief Emitted when connection is closed
     */
    void disconnected();

    /**
     * @brief Emitted for status messages
     * @param message Status message
     */
    void statusMessage(const QString& message);

    /**
     * @brief Emitted for progress updates
     * @param value Progress value (0-100)
     * @param text Progress text
     */
    void progressUpdate(int value, const QString& text);

    /**
     * @brief Emitted when error occurs
     * @param title Error title
     * @param message Error message
     */
    void errorOccurred(const QString& title, const QString& message);

    /**
     * @brief Emitted for data transfer statistics
     * @param bytesReceived Bytes received
     * @param bytesSent Bytes sent
     */
    void dataTransferUpdate(qint64 bytesReceived, qint64 bytesSent);

private slots:
    /**
     * @brief Handle socket connected
     */
    void onSocketConnected();

    /**
     * @brief Handle socket disconnected
     */
    void onSocketDisconnected();

    /**
     * @brief Handle socket data ready
     */
    void onSocketDataReady();

    /**
     * @brief Handle socket error
     * @param error Socket error
     */
    void onSocketError(QAbstractSocket::SocketError error);

    /**
     * @brief Handle socket state change
     * @param state Socket state
     */
    void onSocketStateChanged(QAbstractSocket::SocketState state);

    /**
     * @brief Handle connection timeout
     */
    void onConnectionTimeout();

    /**
     * @brief Handle keep-alive timer
     */
    void onKeepAliveTimer();

    /**
     * @brief Handle authentication timeout
     */
    void onAuthenticationTimeout();

private:
    /**
     * @brief Initialize connection manager
     */
    void initialize();

    /**
     * @brief Setup socket connections
     */
    void setupSocketConnections();

    /**
     * @brief Cleanup connection resources
     */
    void cleanupConnection();

    /**
     * @brief Set connection state
     * @param state New connection state
     */
    void setConnectionState(ConnectionState state);

    /**
     * @brief Start connection timeout timer
     */
    void startConnectionTimeout();

    /**
     * @brief Stop connection timeout timer
     */
    void stopConnectionTimeout();

    /**
     * @brief Start keep-alive timer
     */
    void startKeepAliveTimer();

    /**
     * @brief Stop keep-alive timer
     */
    void stopKeepAliveTimer();

    /**
     * @brief Perform authentication handshake
     * @return True if authentication successful
     */
    bool performAuthentication();

    /**
     * @brief Send authentication request
     * @return True if request sent successfully
     */
    bool sendAuthenticationRequest();

    /**
     * @brief Process authentication response
     * @param data Response data
     * @return True if authentication successful
     */
    bool processAuthenticationResponse(const QByteArray& data);

    /**
     * @brief Send keep-alive packet
     */
    void sendKeepAlive();

    /**
     * @brief Process received data
     * @param data Received data
     */
    void processReceivedData(const QByteArray& data);

    /**
     * @brief Update connection statistics
     * @param bytesReceived Bytes received
     * @param bytesSent Bytes sent
     */
    void updateStatistics(qint64 bytesReceived, qint64 bytesSent);

    /**
     * @brief Get error string for socket error
     * @param error Socket error
     * @return Error description
     */
    QString getSocketErrorString(QAbstractSocket::SocketError error) const;

private:
    // Core components
    QTcpSocket* m_socket;                    ///< TCP socket
    QTimer* m_connectionTimer;               ///< Connection timeout timer
    QTimer* m_keepAliveTimer;                ///< Keep-alive timer
    QTimer* m_authTimer;                     ///< Authentication timeout timer

    // Connection state
    ConnectionState m_connectionState;       ///< Current connection state
    ConnectionConfig m_config;               ///< Connection configuration
    QDateTime m_connectionStartTime;         ///< Connection start time
    QDateTime m_lastDataReceived;            ///< Last data received time

    // Authentication state
    bool m_isAuthenticated;                  ///< Authentication state
    bool m_authenticationInProgress;         ///< Authentication in progress
    int m_authenticationAttempts;            ///< Authentication attempts count

    // Keep-alive
    bool m_keepAliveEnabled;                 ///< Keep-alive enabled state
    QDateTime m_lastKeepAlive;               ///< Last keep-alive time

    // Statistics
    qint64 m_bytesReceived;                  ///< Total bytes received
    qint64 m_bytesSent;                      ///< Total bytes sent
    int m_packetsReceived;                   ///< Packets received count
    int m_packetsSent;                       ///< Packets sent count

    // Configuration
    static constexpr int DEFAULT_CONNECT_TIMEOUT = 30000;     ///< Default connection timeout
    static constexpr int DEFAULT_KEEPALIVE_INTERVAL = 30000;  ///< Default keep-alive interval
    static constexpr int DEFAULT_AUTH_TIMEOUT = 10000;        ///< Default authentication timeout
    static constexpr int MAX_AUTH_ATTEMPTS = 3;               ///< Maximum authentication attempts

    // State flags
    bool m_isInitialized;                    ///< Initialization flag
};
