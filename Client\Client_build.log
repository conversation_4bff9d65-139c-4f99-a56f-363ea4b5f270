Microsoft (R) C/C++ Optimizing Compiler Version 19.44.35207.1 for x64
Copyright (C) Microsoft Corporation.  All rights reserved.

Main.cpp
C:\Users\<USER>\OneDrive\Desktop\HVNC\Client\../common/Common.h(2): warning C4005: 'SECURITY_WIN32': macro redefinition
C:\Users\<USER>\OneDrive\Desktop\HVNC\Client\../common/Common.h(2): note: 'SECURITY_WIN32' previously declared on the command line
HiddenDesktop.cpp
HiddenDesktop.cpp(1): warning C4005: 'WIN32_LEAN_AND_MEAN': macro redefinition
HiddenDesktop.cpp(1): note: 'WIN32_LEAN_AND_MEAN' previously declared on the command line
C:\Users\<USER>\OneDrive\Desktop\HVNC\Client\../common/Common.h(2): warning C4005: 'SECURITY_WIN32': macro redefinition
C:\Users\<USER>\OneDrive\Desktop\HVNC\Client\../common/Common.h(2): note: 'SECURITY_WIN32' previously declared on the command line
HiddenDesktop.cpp(869): warning C4312: 'type cast': conversion from 'BOOL' to 'HMENU' of greater size
Api.cpp
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): warning C4005: 'SECURITY_WIN32': macro redefinition
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): note: 'SECURITY_WIN32' previously declared on the command line
Utils.cpp
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): warning C4005: 'SECURITY_WIN32': macro redefinition
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): note: 'SECURITY_WIN32' previously declared on the command line
Panel.cpp
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): warning C4005: 'SECURITY_WIN32': macro redefinition
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): note: 'SECURITY_WIN32' previously declared on the command line
HTTP.cpp
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): warning C4005: 'SECURITY_WIN32': macro redefinition
C:\Users\<USER>\OneDrive\Desktop\HVNC\common\Common.h(2): note: 'SECURITY_WIN32' previously declared on the command line
Generating Code...
Microsoft (R) Incremental Linker Version 14.44.35207.1
Copyright (C) Microsoft Corporation.  All rights reserved.

/out:Client.exe 
Main.obj 
HiddenDesktop.obj 
Api.obj 
Utils.obj 
Panel.obj 
HTTP.obj 
user32.lib 
gdi32.lib 
kernel32.lib 
ws2_32.lib 
advapi32.lib 
shell32.lib 
shlwapi.lib 
wininet.lib 
urlmon.lib 
psapi.lib 
secur32.lib 
ole32.lib 
