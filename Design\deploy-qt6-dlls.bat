@echo off
:: ============================================================================
:: HVNC Design Qt6 DLL Deployment Script
:: ============================================================================
:: Copies all necessary Qt6 DLLs to make HVNCDesign.exe run standalone
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC Design Qt6 DLL Deployment                           ║%NC%
echo %BLUE%║                    Making HVNCDesign.exe Standalone                         ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Setup paths
echo %CYAN%[STEP 1]%NC% Setting up Qt6 paths...

set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64"
set "QT6_BIN=%QT6_PATH%\bin"
set "TARGET_DIR=.."

echo %GREEN%[CONFIG]%NC% Qt6 path: %QT6_PATH%
echo %GREEN%[CONFIG]%NC% Target directory: %TARGET_DIR%

if not exist "%QT6_BIN%" (
    echo %RED%[ERROR]%NC% Qt6 bin directory not found: %QT6_BIN%
    pause
    exit /b 1
)

:: Step 2: Check if HVNCDesign.exe exists
echo %CYAN%[STEP 2]%NC% Checking for HVNCDesign.exe...

if not exist "%TARGET_DIR%\HVNCDesign.exe" (
    echo %RED%[ERROR]%NC% HVNCDesign.exe not found in %TARGET_DIR%
    echo %YELLOW%[HINT]%NC% Run build-manual.bat first to create the executable
    pause
    exit /b 1
)

echo %GREEN%[FOUND]%NC% HVNCDesign.exe found

:: Step 3: Copy essential Qt6 DLLs
echo %CYAN%[STEP 3]%NC% Copying essential Qt6 DLLs...

:: Core Qt6 DLLs
set "CORE_DLLS=Qt6Core.dll Qt6Gui.dll Qt6Widgets.dll Qt6Network.dll Qt6Concurrent.dll"

for %%D in (%CORE_DLLS%) do (
    if exist "%QT6_BIN%\%%D" (
        copy "%QT6_BIN%\%%D" "%TARGET_DIR%\" >nul
        if errorlevel 1 (
            echo %RED%[FAILED]%NC% Failed to copy %%D
        ) else (
            echo %GREEN%[COPIED]%NC% %%D
        )
    ) else (
        echo %YELLOW%[MISSING]%NC% %%D not found in Qt6 bin directory
    )
)

:: Step 4: Copy Visual C++ Runtime DLLs (if available in Qt6 bin)
echo %CYAN%[STEP 4]%NC% Copying Visual C++ Runtime DLLs...

set "RUNTIME_DLLS=msvcp140.dll vcruntime140.dll vcruntime140_1.dll"

for %%D in (%RUNTIME_DLLS%) do (
    if exist "%QT6_BIN%\%%D" (
        copy "%QT6_BIN%\%%D" "%TARGET_DIR%\" >nul
        if errorlevel 1 (
            echo %YELLOW%[WARNING]%NC% Failed to copy %%D
        ) else (
            echo %GREEN%[COPIED]%NC% %%D
        )
    ) else (
        echo %YELLOW%[SKIP]%NC% %%D not found (may already be on system)
    )
)

:: Step 5: Copy additional Qt6 support DLLs
echo %CYAN%[STEP 5]%NC% Copying additional Qt6 support DLLs...

set "SUPPORT_DLLS=Qt6Core5Compat.dll"

for %%D in (%SUPPORT_DLLS%) do (
    if exist "%QT6_BIN%\%%D" (
        copy "%QT6_BIN%\%%D" "%TARGET_DIR%\" >nul
        if errorlevel 1 (
            echo %YELLOW%[WARNING]%NC% Failed to copy %%D
        ) else (
            echo %GREEN%[COPIED]%NC% %%D
        )
    ) else (
        echo %YELLOW%[SKIP]%NC% %%D not found (optional)
    )
)

:: Step 6: Create platforms directory and copy platform plugins
echo %CYAN%[STEP 6]%NC% Copying Qt6 platform plugins...

if not exist "%TARGET_DIR%\platforms" mkdir "%TARGET_DIR%\platforms"

if exist "%QT6_PATH%\plugins\platforms\qwindows.dll" (
    copy "%QT6_PATH%\plugins\platforms\qwindows.dll" "%TARGET_DIR%\platforms\" >nul
    if errorlevel 1 (
        echo %RED%[FAILED]%NC% Failed to copy qwindows.dll
    ) else (
        echo %GREEN%[COPIED]%NC% platforms\qwindows.dll
    )
) else (
    echo %RED%[ERROR]%NC% qwindows.dll not found - GUI may not work properly
)

:: Step 7: Copy styles directory (optional but recommended)
echo %CYAN%[STEP 7]%NC% Copying Qt6 style plugins...

if not exist "%TARGET_DIR%\styles" mkdir "%TARGET_DIR%\styles"

if exist "%QT6_PATH%\plugins\styles\qwindowsvistastyle.dll" (
    copy "%QT6_PATH%\plugins\styles\qwindowsvistastyle.dll" "%TARGET_DIR%\styles\" >nul
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% Failed to copy qwindowsvistastyle.dll
    ) else (
        echo %GREEN%[COPIED]%NC% styles\qwindowsvistastyle.dll
    )
) else (
    echo %YELLOW%[SKIP]%NC% qwindowsvistastyle.dll not found (optional)
)

:: Step 8: Use windeployqt if available
echo %CYAN%[STEP 8]%NC% Running windeployqt for automatic deployment...

if exist "%QT6_BIN%\windeployqt.exe" (
    echo %BLUE%[DEPLOY]%NC% Running windeployqt.exe...
    "%QT6_BIN%\windeployqt.exe" "%TARGET_DIR%\HVNCDesign.exe" --release --no-translations --no-system-d3d-compiler --no-opengl-sw --no-compiler-runtime
    
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% windeployqt completed with warnings
    ) else (
        echo %GREEN%[SUCCESS]%NC% windeployqt completed successfully
    )
) else (
    echo %YELLOW%[SKIP]%NC% windeployqt.exe not found, using manual deployment only
)

:: Step 9: Verify deployment
echo %CYAN%[STEP 9]%NC% Verifying deployment...

set "VERIFICATION_PASSED=true"

for %%D in (%CORE_DLLS%) do (
    if not exist "%TARGET_DIR%\%%D" (
        echo %RED%[MISSING]%NC% %%D not found in target directory
        set "VERIFICATION_PASSED=false"
    )
)

if not exist "%TARGET_DIR%\platforms\qwindows.dll" (
    echo %RED%[MISSING]%NC% platforms\qwindows.dll not found
    set "VERIFICATION_PASSED=false"
)

:: Success summary
echo.
if "%VERIFICATION_PASSED%"=="true" (
    echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
    echo %GREEN%║                        DEPLOYMENT SUCCESSFUL!                               ║%NC%
    echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
) else (
    echo %YELLOW%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
    echo %YELLOW%║                        DEPLOYMENT COMPLETED WITH WARNINGS                   ║%NC%
    echo %YELLOW%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
)

echo.
echo %CYAN%Deployed Files:%NC%
echo   %GREEN%✓%NC% HVNCDesign.exe - Main application
echo   %GREEN%✓%NC% Qt6Core.dll - Qt6 core functionality
echo   %GREEN%✓%NC% Qt6Gui.dll - Qt6 GUI support
echo   %GREEN%✓%NC% Qt6Widgets.dll - Qt6 widgets
echo   %GREEN%✓%NC% Qt6Network.dll - Qt6 networking
echo   %GREEN%✓%NC% Qt6Concurrent.dll - Qt6 threading
echo   %GREEN%✓%NC% platforms\qwindows.dll - Windows platform plugin

echo.
echo %CYAN%Usage Instructions:%NC%
echo   %WHITE%1.%NC% HVNCDesign.exe can now run on computers without Qt6 installed
echo   %WHITE%2.%NC% All necessary DLLs are in the same directory
echo   %WHITE%3.%NC% The platforms\ directory is required for GUI to work
echo   %WHITE%4.%NC% You can distribute the entire directory as a package

echo.
echo %CYAN%[TEST]%NC% Would you like to test the standalone application? (y/n)
set /p "test_launch=Enter choice: "

if /i "!test_launch!"=="y" (
    echo %BLUE%[LAUNCH]%NC% Testing standalone HVNCDesign.exe...
    
    cd "%TARGET_DIR%"
    start "" "HVNCDesign.exe"
    
    if errorlevel 1 (
        echo %RED%[FAILED]%NC% Failed to launch standalone application
    ) else (
        echo %GREEN%[SUCCESS]%NC% Standalone application launched successfully!
    )
)

echo.
echo %GREEN%[COMPLETE]%NC% Qt6 DLL deployment finished!
pause
