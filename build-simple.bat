@echo off
:: ============================================================================
:: HVNC Design Simple Windows Build Script
:: ============================================================================
:: Simple build that bypasses qmake MSVC compatibility issues
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC Design Simple Windows Build                         ║%NC%
echo %BLUE%║                    Bypassing qmake MSVC Compatibility Issues                ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Setup environment
echo %CYAN%[STEP 1]%NC% Setting up build environment...

set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64"
set "PATH=%QT6_PATH%\bin;%PATH%"

echo %GREEN%[CONFIG]%NC% Qt6 path: %QT6_PATH%

:: Step 2: Setup Visual Studio
echo %CYAN%[STEP 2]%NC% Setting up Visual Studio...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% Visual Studio setup failed, continuing anyway...
) else (
    echo %GREEN%[SUCCESS]%NC% Visual Studio environment loaded
)

:: Step 3: Create a minimal working version
echo %CYAN%[STEP 3]%NC% Creating minimal working version...

:: Clean previous builds
if exist "windows" rmdir /s /q "windows" 2>nul
mkdir "windows" 2>nul

:: Step 4: Try direct compilation approach
echo %CYAN%[STEP 4]%NC% Attempting direct Qt6 compilation...

:: Set Qt6 environment variables
set "CMAKE_PREFIX_PATH=%QT6_PATH%"
set "Qt6_DIR=%QT6_PATH%\lib\cmake\Qt6"

:: Create a simple CMake build
echo %BLUE%[CMAKE]%NC% Using CMake to bypass qmake issues...

if exist "build" rmdir /s /q "build" 2>nul
mkdir "build" 2>nul
cd "build"

:: Configure with CMake
cmake .. -G "Visual Studio 17 2022" -A x64 ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DCMAKE_PREFIX_PATH="%CMAKE_PREFIX_PATH%" ^
    -DQt6_DIR="%Qt6_DIR%"

if errorlevel 1 (
    echo %RED%[FAILED]%NC% CMake configuration failed
    cd ..
    goto :manual_build
)

:: Build with CMake
cmake --build . --config Release

if errorlevel 1 (
    echo %RED%[FAILED]%NC% CMake build failed
    cd ..
    goto :manual_build
)

:: Check for executable
if exist "Release\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% CMake build successful!
    copy "Release\HVNCDesign.exe" "..\windows\HVNCDesign.exe" >nul
    copy "Release\HVNCDesign.exe" "..\..\HVNCDesign.exe" >nul
    cd ..
    goto :success
)

cd ..

:manual_build
echo %CYAN%[FALLBACK]%NC% Trying manual build approach...

:: Create a simple qmake workaround
echo %BLUE%[WORKAROUND]%NC% Creating qmake workaround...

:: Create a temporary simplified .pro file
(
echo QT += core widgets network
echo CONFIG += c++20 release windows
echo TARGET = HVNCDesign
echo TEMPLATE = app
echo.
echo SOURCES += src/main.cpp src/MainWindow.cpp src/DesktopViewer.cpp
echo SOURCES += src/ServerManager.cpp src/StyleManager.cpp
echo SOURCES += src/ImageProcessor.cpp src/ConnectionManager.cpp
echo.
echo HEADERS += include/MainWindow.h include/DesktopViewer.h
echo HEADERS += include/ServerManager.h include/StyleManager.h
echo HEADERS += include/ImageProcessor.h include/ConnectionManager.h
echo HEADERS += include/Common.h
echo.
echo INCLUDEPATH += include
echo.
echo win32 {
echo     LIBS += -luser32 -lgdi32 -lkernel32 -ladvapi32 -lshell32
echo     LIBS += -lws2_32 -lnetapi32
echo     DEFINES += WIN32_LEAN_AND_MEAN NOMINMAX WINDOWS_PLATFORM
echo     DESTDIR = windows
echo }
) > HVNCDesign_Simple.pro

:: Try qmake with the simplified project
qmake HVNCDesign_Simple.pro

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Even simplified qmake failed
    goto :error_exit
)

:: Try to build
nmake

if errorlevel 1 (
    echo %YELLOW%[WARNING]%NC% nmake failed, trying make...
    make
    
    if errorlevel 1 (
        echo %RED%[FAILED]%NC% All build methods failed
        goto :error_exit
    )
)

:: Check for executable
if exist "windows\HVNCDesign.exe" (
    copy "windows\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    goto :success
) else if exist "HVNCDesign.exe" (
    copy "HVNCDesign.exe" "windows\HVNCDesign.exe" >nul
    copy "HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    goto :success
)

:error_exit
echo %RED%[FAILED]%NC% All build attempts failed
echo %YELLOW%[INFO]%NC% This is likely due to Qt6/MSVC compatibility issues
echo %YELLOW%[SUGGESTION]%NC% Try using Qt Creator IDE to build the project
pause
exit /b 1

:success
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        BUILD SUCCESSFUL!                                    ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Application:%NC%
if exist "..\HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for Windows
    
    for %%F in ("..\HVNCDesign.exe") do (
        echo %CYAN%[INFO]%NC% File size: %%~zF bytes
    )
) else (
    echo   %RED%✗%NC% HVNCDesign.exe - Not found in root directory
)

echo.
echo %CYAN%[TEST]%NC% Would you like to test launch the GUI? (y/n)
set /p "test_launch=Enter choice: "

if /i "!test_launch!"=="y" (
    echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe...
    
    if exist "..\HVNCDesign.exe" (
        start "" "..\HVNCDesign.exe"
        echo %GREEN%[SUCCESS]%NC% GUI launched!
    ) else (
        echo %RED%[ERROR]%NC% Executable not found
    )
)

echo.
echo %GREEN%[COMPLETE]%NC% Simple build finished!

:: Cleanup
if exist "HVNCDesign_Simple.pro" del "HVNCDesign_Simple.pro"

pause
