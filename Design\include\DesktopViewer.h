/**
 * @file DesktopViewer.h
 * @brief Desktop viewer widget for displaying remote desktop
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This widget provides the main desktop viewing area where the remote
 * desktop images are displayed. It handles image scaling, mouse and
 * keyboard input forwarding, and real-time desktop updates.
 */

#pragma once

#include "Common.h"
#include <QGraphicsView>
#include <QGraphicsScene>
#include <QGraphicsPixmapItem>

// Forward declarations
class EventHandler;
class ImageProcessor;
class QTimer;
class QLabel;
class QRubberBand;

/**
 * @class DesktopViewer
 * @brief Main desktop viewing widget
 * 
 * The DesktopViewer class provides a professional desktop viewing interface
 * with support for real-time image updates, input event handling, scaling,
 * and various viewing modes. It uses QGraphicsView for efficient rendering
 * and smooth scaling operations.
 */
class DesktopViewer : public QGraphicsView {
    Q_OBJECT

public:
    /**
     * @brief Scaling mode enumeration
     */
    enum class ScalingMode {
        FitToWindow,    ///< Scale to fit window
        ActualSize,     ///< Show at actual size
        CustomScale     ///< Custom scaling factor
    };

    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit DesktopViewer(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~DesktopViewer() override;

    /**
     * @brief Set desktop image
     * @param image Desktop image to display
     */
    void setDesktopImage(const QImage& image);

    /**
     * @brief Update desktop region
     * @param image Region image
     * @param x X coordinate
     * @param y Y coordinate
     */
    void updateDesktopRegion(const QImage& image, int x, int y);

    /**
     * @brief Set scaling mode
     * @param mode Scaling mode
     */
    void setScalingMode(ScalingMode mode);

    /**
     * @brief Get current scaling mode
     * @return Current scaling mode
     */
    ScalingMode getScalingMode() const { return m_scalingMode; }

    /**
     * @brief Set custom scale factor
     * @param factor Scale factor (0.1 to 5.0)
     */
    void setScaleFactor(double factor);

    /**
     * @brief Get current scale factor
     * @return Current scale factor
     */
    double getScaleFactor() const { return m_scaleFactor; }

    /**
     * @brief Get desktop size
     * @return Desktop size in pixels
     */
    QSize getDesktopSize() const { return m_desktopSize; }

    /**
     * @brief Set event handler
     * @param handler Event handler instance
     */
    void setEventHandler(EventHandler* handler);

    /**
     * @brief Enable/disable input forwarding
     * @param enabled True to enable input forwarding
     */
    void setInputForwardingEnabled(bool enabled);

    /**
     * @brief Check if input forwarding is enabled
     * @return True if input forwarding is enabled
     */
    bool isInputForwardingEnabled() const { return m_inputForwardingEnabled; }

    /**
     * @brief Set cursor visibility
     * @param visible True to show cursor
     */
    void setCursorVisible(bool visible);

    /**
     * @brief Enable/disable selection rectangle
     * @param enabled True to enable selection
     */
    void setSelectionEnabled(bool enabled);

    /**
     * @brief Clear desktop display
     */
    void clearDesktop();

    /**
     * @brief Take screenshot of current view
     * @return Screenshot image
     */
    QImage takeScreenshot() const;

signals:
    /**
     * @brief Emitted when mouse event occurs
     * @param event Mouse event details
     * @param position Position in desktop coordinates
     */
    void mouseEventOccurred(QMouseEvent* event, const QPoint& position);

    /**
     * @brief Emitted when key event occurs
     * @param event Key event details
     */
    void keyEventOccurred(QKeyEvent* event);

    /**
     * @brief Emitted when wheel event occurs
     * @param event Wheel event details
     * @param position Position in desktop coordinates
     */
    void wheelEventOccurred(QWheelEvent* event, const QPoint& position);

    /**
     * @brief Emitted when scaling changes
     * @param factor New scale factor
     */
    void scalingChanged(double factor);

    /**
     * @brief Emitted when desktop size changes
     * @param size New desktop size
     */
    void desktopSizeChanged(const QSize& size);

    /**
     * @brief Emitted when selection area changes
     * @param rect Selection rectangle in desktop coordinates
     */
    void selectionChanged(const QRect& rect);

protected:
    /**
     * @brief Handle mouse press events
     * @param event Mouse event
     */
    void mousePressEvent(QMouseEvent* event) override;

    /**
     * @brief Handle mouse move events
     * @param event Mouse event
     */
    void mouseMoveEvent(QMouseEvent* event) override;

    /**
     * @brief Handle mouse release events
     * @param event Mouse event
     */
    void mouseReleaseEvent(QMouseEvent* event) override;

    /**
     * @brief Handle mouse double click events
     * @param event Mouse event
     */
    void mouseDoubleClickEvent(QMouseEvent* event) override;

    /**
     * @brief Handle wheel events
     * @param event Wheel event
     */
    void wheelEvent(QWheelEvent* event) override;

    /**
     * @brief Handle key press events
     * @param event Key event
     */
    void keyPressEvent(QKeyEvent* event) override;

    /**
     * @brief Handle key release events
     * @param event Key event
     */
    void keyReleaseEvent(QKeyEvent* event) override;

    /**
     * @brief Handle resize events
     * @param event Resize event
     */
    void resizeEvent(QResizeEvent* event) override;

    /**
     * @brief Handle paint events
     * @param event Paint event
     */
    void paintEvent(QPaintEvent* event) override;

    /**
     * @brief Handle focus in events
     * @param event Focus event
     */
    void focusInEvent(QFocusEvent* event) override;

    /**
     * @brief Handle focus out events
     * @param event Focus event
     */
    void focusOutEvent(QFocusEvent* event) override;

    /**
     * @brief Handle context menu events
     * @param event Context menu event
     */
    void contextMenuEvent(QContextMenuEvent* event) override;

private slots:
    /**
     * @brief Handle cursor blink timer
     */
    void onCursorBlink();

    /**
     * @brief Handle FPS update timer
     */
    void onFpsUpdate();

private:
    /**
     * @brief Initialize the desktop viewer
     */
    void initialize();

    /**
     * @brief Setup the graphics scene
     */
    void setupScene();

    /**
     * @brief Apply scaling to view
     */
    void applyScaling();

    /**
     * @brief Update view transform
     */
    void updateViewTransform();

    /**
     * @brief Convert view coordinates to desktop coordinates
     * @param viewPos Position in view coordinates
     * @return Position in desktop coordinates
     */
    QPoint viewToDesktopCoordinates(const QPoint& viewPos) const;

    /**
     * @brief Convert desktop coordinates to view coordinates
     * @param desktopPos Position in desktop coordinates
     * @return Position in view coordinates
     */
    QPoint desktopToViewCoordinates(const QPoint& desktopPos) const;

    /**
     * @brief Update cursor position
     * @param position Cursor position in desktop coordinates
     */
    void updateCursorPosition(const QPoint& position);

    /**
     * @brief Show context menu
     * @param position Menu position
     */
    void showContextMenu(const QPoint& position);

    /**
     * @brief Update selection rectangle
     * @param rect Selection rectangle
     */
    void updateSelection(const QRect& rect);

    /**
     * @brief Calculate optimal scale factor for fit-to-window mode
     * @return Optimal scale factor
     */
    double calculateFitToWindowScale() const;

private:
    // Core components
    QGraphicsScene* m_scene;                 ///< Graphics scene
    QGraphicsPixmapItem* m_desktopItem;      ///< Desktop pixmap item
    EventHandler* m_eventHandler;            ///< Event handler
    ImageProcessor* m_imageProcessor;        ///< Image processor

    // Desktop state
    QImage m_desktopImage;                   ///< Current desktop image
    QSize m_desktopSize;                     ///< Desktop size
    QPoint m_cursorPosition;                 ///< Cursor position

    // Scaling and viewing
    ScalingMode m_scalingMode;               ///< Current scaling mode
    double m_scaleFactor;                    ///< Current scale factor
    double m_minScaleFactor;                 ///< Minimum scale factor
    double m_maxScaleFactor;                 ///< Maximum scale factor

    // Input handling
    bool m_inputForwardingEnabled;           ///< Input forwarding state
    bool m_cursorVisible;                    ///< Cursor visibility
    bool m_selectionEnabled;                 ///< Selection enabled state
    QRubberBand* m_selectionBand;            ///< Selection rubber band
    QPoint m_selectionStart;                 ///< Selection start point

    // UI elements
    QLabel* m_overlayLabel;                  ///< Overlay information label
    QTimer* m_cursorBlinkTimer;              ///< Cursor blink timer
    QTimer* m_fpsTimer;                      ///< FPS calculation timer

    // Performance tracking
    int m_frameCount;                        ///< Frame counter for FPS
    qint64 m_lastFpsUpdate;                  ///< Last FPS update time
    double m_currentFps;                     ///< Current FPS

    // State flags
    bool m_isInitialized;                    ///< Initialization flag
    bool m_isDragging;                       ///< Dragging state
    bool m_isSelecting;                      ///< Selection state
};
