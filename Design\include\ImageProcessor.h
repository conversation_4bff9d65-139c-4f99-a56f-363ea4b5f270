/**
 * @file ImageProcessor.h
 * @brief Image processing utilities for HVNC desktop images
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This class handles image processing operations including decompression,
 * format conversion, and optimization for desktop image data received
 * from HVNC clients.
 */

#pragma once

#include "Common.h"

/**
 * @class ImageProcessor
 * @brief Handles image processing for HVNC desktop data
 * 
 * The ImageProcessor class provides functionality to process raw image
 * data received from HVNC clients, including decompression, format
 * conversion, and various image optimizations.
 */
class ImageProcessor : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Image format enumeration
     */
    enum class ImageFormat {
        Raw24,          ///< Raw 24-bit RGB
        Raw32,          ///< Raw 32-bit RGBA
        Compressed,     ///< Compressed format
        JPEG,           ///< JPEG format
        PNG             ///< PNG format
    };

    /**
     * @brief Compression type enumeration
     */
    enum class CompressionType {
        None,           ///< No compression
        LZNT1,          ///< LZNT1 compression (Windows)
        Zlib,           ///< Zlib compression
        LZ4             ///< LZ4 compression
    };

    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit ImageProcessor(QObject* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~ImageProcessor() override;

    /**
     * @brief Process raw image data
     * @param data Raw image data
     * @param width Image width
     * @param height Image height
     * @param format Image format
     * @return Processed QImage
     */
    QImage processImageData(const QByteArray& data, int width, int height, 
                           ImageFormat format = ImageFormat::Raw24);

    /**
     * @brief Decompress image data
     * @param compressedData Compressed data
     * @param uncompressedSize Expected uncompressed size
     * @param compressionType Compression type
     * @return Decompressed data
     */
    QByteArray decompressImageData(const QByteArray& compressedData, 
                                  int uncompressedSize,
                                  CompressionType compressionType = CompressionType::LZNT1);

    /**
     * @brief Convert raw RGB data to QImage
     * @param data Raw RGB data
     * @param width Image width
     * @param height Image height
     * @param bytesPerPixel Bytes per pixel (3 for RGB, 4 for RGBA)
     * @return Converted QImage
     */
    QImage convertRawToImage(const QByteArray& data, int width, int height, int bytesPerPixel = 3);

    /**
     * @brief Apply transparency mask
     * @param image Source image
     * @param transparentColor Color to treat as transparent
     * @return Image with transparency applied
     */
    QImage applyTransparency(const QImage& image, const QColor& transparentColor);

    /**
     * @brief Scale image efficiently
     * @param image Source image
     * @param targetSize Target size
     * @param smooth Use smooth scaling
     * @return Scaled image
     */
    QImage scaleImage(const QImage& image, const QSize& targetSize, bool smooth = true);

    /**
     * @brief Optimize image for display
     * @param image Source image
     * @return Optimized image
     */
    QImage optimizeForDisplay(const QImage& image);

    /**
     * @brief Get supported compression types
     * @return List of supported compression types
     */
    QList<CompressionType> getSupportedCompressionTypes() const;

    /**
     * @brief Check if compression type is supported
     * @param type Compression type
     * @return True if supported
     */
    bool isCompressionSupported(CompressionType type) const;

    /**
     * @brief Set default compression type
     * @param type Compression type
     */
    void setDefaultCompressionType(CompressionType type);

    /**
     * @brief Get default compression type
     * @return Default compression type
     */
    CompressionType getDefaultCompressionType() const { return m_defaultCompressionType; }

signals:
    /**
     * @brief Emitted when image processing completes
     * @param image Processed image
     */
    void imageProcessed(const QImage& image);

    /**
     * @brief Emitted when processing error occurs
     * @param error Error message
     */
    void processingError(const QString& error);

private:
    /**
     * @brief Initialize image processor
     */
    void initialize();

    /**
     * @brief Setup compression support
     */
    void setupCompressionSupport();

    /**
     * @brief Decompress using LZNT1 (Windows)
     * @param compressedData Compressed data
     * @param uncompressedSize Expected size
     * @return Decompressed data
     */
    QByteArray decompressLZNT1(const QByteArray& compressedData, int uncompressedSize);

    /**
     * @brief Decompress using Zlib
     * @param compressedData Compressed data
     * @return Decompressed data
     */
    QByteArray decompressZlib(const QByteArray& compressedData);

    /**
     * @brief Decompress using LZ4
     * @param compressedData Compressed data
     * @param uncompressedSize Expected size
     * @return Decompressed data
     */
    QByteArray decompressLZ4(const QByteArray& compressedData, int uncompressedSize);

    /**
     * @brief Validate image dimensions
     * @param width Image width
     * @param height Image height
     * @return True if valid
     */
    bool validateImageDimensions(int width, int height) const;

    /**
     * @brief Calculate expected data size
     * @param width Image width
     * @param height Image height
     * @param bytesPerPixel Bytes per pixel
     * @return Expected data size
     */
    int calculateExpectedDataSize(int width, int height, int bytesPerPixel) const;

    /**
     * @brief Convert BGR to RGB
     * @param data Image data (modified in place)
     * @param pixelCount Number of pixels
     */
    void convertBGRToRGB(QByteArray& data, int pixelCount);

    /**
     * @brief Apply gamma correction
     * @param image Source image
     * @param gamma Gamma value
     * @return Gamma corrected image
     */
    QImage applyGammaCorrection(const QImage& image, double gamma);

private:
    // Configuration
    CompressionType m_defaultCompressionType;   ///< Default compression type
    QList<CompressionType> m_supportedTypes;    ///< Supported compression types
    
    // Image processing settings
    int m_maxImageWidth;                        ///< Maximum image width
    int m_maxImageHeight;                       ///< Maximum image height
    bool m_enableOptimization;                  ///< Enable image optimization
    double m_defaultGamma;                      ///< Default gamma value
    
    // Transparent color (from existing server code)
    QColor m_transparentColor;                  ///< Transparent color (RGB(255, 174, 201))
    
    // Performance tracking
    qint64 m_totalProcessingTime;               ///< Total processing time
    int m_processedImageCount;                  ///< Number of processed images
    
    // State flags
    bool m_isInitialized;                       ///< Initialization flag

#ifdef _WIN32
    // Windows-specific compression support
    void* m_ntdllHandle;                        ///< Handle to ntdll.dll
    void* m_rtlDecompressBuffer;                ///< RtlDecompressBuffer function pointer
#endif
};
