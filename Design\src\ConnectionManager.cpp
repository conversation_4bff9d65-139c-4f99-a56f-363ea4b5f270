/**
 * @file ConnectionManager.cpp
 * @brief Implementation of ConnectionManager class
 */

#include "ConnectionManager.h"
#include <QHostAddress>
#include <QNetworkInterface>

ConnectionManager::ConnectionManager(QObject* parent)
    : QObject(parent)
    , m_socket(nullptr)
    , m_connectionTimer(nullptr)
    , m_keepAliveTimer(nullptr)
    , m_authTimer(nullptr)
    , m_connectionState(ConnectionState::Disconnected)
    , m_isAuthenticated(false)
    , m_authenticationInProgress(false)
    , m_authenticationAttempts(0)
    , m_keepAliveEnabled(true)
    , m_bytesReceived(0)
    , m_bytesSent(0)
    , m_packetsReceived(0)
    , m_packetsSent(0)
    , m_isInitialized(false)
{
    initialize();
}

ConnectionManager::~ConnectionManager() {
    // Basic cleanup
    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
    }
}

void ConnectionManager::initialize() {
    if (m_isInitialized) return;

    // Create TCP socket
    m_socket = new QTcpSocket(this);

    // Create timers
    m_connectionTimer = new QTimer(this);
    m_connectionTimer->setSingleShot(true);
    m_connectionTimer->setInterval(DEFAULT_CONNECT_TIMEOUT);

    m_keepAliveTimer = new QTimer(this);
    m_keepAliveTimer->setInterval(DEFAULT_KEEPALIVE_INTERVAL);

    m_authTimer = new QTimer(this);
    m_authTimer->setSingleShot(true);
    m_authTimer->setInterval(DEFAULT_AUTH_TIMEOUT);

    // Connect socket signals
    connect(m_socket, &QTcpSocket::connected, this, &ConnectionManager::onSocketConnected);
    connect(m_socket, &QTcpSocket::disconnected, this, &ConnectionManager::onSocketDisconnected);
    connect(m_socket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::errorOccurred),
            this, &ConnectionManager::onSocketError);
    connect(m_socket, &QTcpSocket::readyRead, this, [this]() {
        QByteArray data = m_socket->readAll();
        if (!data.isEmpty()) {
            m_bytesReceived += data.size();
            m_packetsReceived++;
            emit dataReceived(data);
        }
    });

    // Connect timer signals
    connect(m_connectionTimer, &QTimer::timeout, this, &ConnectionManager::onConnectionTimeout);

    m_isInitialized = true;
    HVNC_INFO() << "ConnectionManager initialized";
}

// Basic connection method - simplified for compilation
bool ConnectionManager::connectToHost(const ConnectionConfig& config) {
    if (m_connectionState == ConnectionState::Connected ||
        m_connectionState == ConnectionState::Connecting) {
        HVNC_WARNING() << "Already connected or connecting";
        return false;
    }

    m_config = config;

    HVNC_INFO() << "Connecting to" << config.hostAddress << ":" << config.port;
    setConnectionState(ConnectionState::Connecting);

    // Start connection timeout timer
    m_connectionTimer->start();

    // Attempt connection
    m_socket->connectToHost(config.hostAddress, config.port);

    emit statusMessage(QString("Connecting to %1:%2...").arg(config.hostAddress).arg(config.port));
    emit progressUpdate(10, "Establishing connection...");

    return true;
}

void ConnectionManager::disconnect() {
    HVNC_INFO() << "Disconnecting from server";
    
    if (m_connectionTimer) {
        m_connectionTimer->stop();
    }
    
    if (m_socket && m_socket->state() != QAbstractSocket::UnconnectedState) {
        m_socket->disconnectFromHost();
    } else {
        setConnectionState(ConnectionState::Disconnected);
    }
    
    emit statusMessage("Disconnected from server");
}

void ConnectionManager::setConnectionState(ConnectionState state) {
    if (m_connectionState != state) {
        m_connectionState = state;
        emit connectionStateChanged(state);

        QString stateStr;
        switch (state) {
            case ConnectionState::Disconnected: stateStr = "Disconnected"; break;
            case ConnectionState::Connecting: stateStr = "Connecting"; break;
            case ConnectionState::Connected: stateStr = "Connected"; break;
            case ConnectionState::Error: stateStr = "Error"; break;
        }

        HVNC_INFO() << "Connection state changed to:" << stateStr;
    }
}

void ConnectionManager::onSocketConnected() {
    HVNC_INFO() << "Socket connected successfully";

    if (m_connectionTimer) {
        m_connectionTimer->stop();
    }

    setConnectionState(ConnectionState::Connected);
    m_authenticationAttempts = 0;

    emit statusMessage(QString("Connected to %1:%2").arg(m_config.hostAddress).arg(m_config.port));
    emit progressUpdate(100, "Connection established");
}

void ConnectionManager::onSocketDisconnected() {
    HVNC_INFO() << "Socket disconnected";
    
    if (m_connectionTimer) {
        m_connectionTimer->stop();
    }
    
    setConnectionState(ConnectionState::Disconnected);
    emit statusMessage("Disconnected from server");
}

void ConnectionManager::onSocketError(QAbstractSocket::SocketError error) {
    Q_UNUSED(error)
    QString errorString = m_socket->errorString();
    HVNC_CRITICAL() << "Socket error:" << errorString;
    
    if (m_connectionTimer) {
        m_connectionTimer->stop();
    }
    
    setConnectionState(ConnectionState::Error);
    emit errorOccurred("Connection Error", errorString);
    emit statusMessage(QString("Connection error: %1").arg(errorString));
}

// onDataReady is now handled in initialize() as a lambda

void ConnectionManager::onConnectionTimeout() {
    HVNC_WARNING() << "Connection timeout";

    if (m_socket) {
        m_socket->abort();
    }

    setConnectionState(ConnectionState::Error);
    emit errorOccurred("Connection Timeout", "Failed to connect within timeout period");
    emit statusMessage("Connection timeout");
}

bool ConnectionManager::sendData(const QByteArray& data) {
    if (!m_socket || m_socket->state() != QAbstractSocket::ConnectedState) {
        return false;
    }

    qint64 bytesWritten = m_socket->write(data);
    if (bytesWritten > 0) {
        m_bytesSent += bytesWritten;
        m_packetsSent++;
        return true;
    }

    return false;
}

QMap<QString, QVariant> ConnectionManager::getConnectionStatistics() const {
    QMap<QString, QVariant> stats;
    stats["state"] = static_cast<int>(m_connectionState);
    stats["isAuthenticated"] = m_isAuthenticated;
    stats["bytesReceived"] = static_cast<qulonglong>(m_bytesReceived);
    stats["bytesSent"] = static_cast<qulonglong>(m_bytesSent);
    stats["packetsReceived"] = static_cast<qulonglong>(m_packetsReceived);
    stats["packetsSent"] = static_cast<qulonglong>(m_packetsSent);
    stats["hostAddress"] = m_config.hostAddress;
    stats["port"] = m_config.port;
    return stats;
}

void ConnectionManager::setKeepAliveEnabled(bool enabled) {
    m_keepAliveEnabled = enabled;
    if (m_keepAliveTimer) {
        if (enabled && m_connectionState == ConnectionState::Connected) {
            m_keepAliveTimer->start();
        } else {
            m_keepAliveTimer->stop();
        }
    }
}

void ConnectionManager::onSocketDataReady() {
    // This is handled in the lambda in initialize()
    // But we need this method for the header declaration
}

void ConnectionManager::onKeepAliveTimer() {
    // Send keep-alive packet if connected
    if (m_connectionState == ConnectionState::Connected && m_keepAliveEnabled) {
        // Send a simple keep-alive packet
        QByteArray keepAlive("KEEPALIVE");
        sendData(keepAlive);
    }
}

void ConnectionManager::onAuthenticationTimeout() {
    HVNC_WARNING() << "Authentication timeout";

    if (m_authTimer) {
        m_authTimer->stop();
    }

    m_authenticationInProgress = false;
    setConnectionState(ConnectionState::Error);
    emit errorOccurred("Authentication Timeout", "Authentication failed within timeout period");
}

void ConnectionManager::onSocketStateChanged(QAbstractSocket::SocketState state) {
    Q_UNUSED(state)
    // Handle socket state changes if needed
    HVNC_DEBUG() << "Socket state changed to:" << state;
}
