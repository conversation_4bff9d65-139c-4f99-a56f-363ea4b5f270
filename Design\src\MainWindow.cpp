/**
 * @file MainWindow.cpp
 * @brief Implementation of MainWindow class
 */

#include "MainWindow.h"
#include "DesktopViewer.h"
#include "ControlPanel.h"
#include "SettingsDialog.h"
#include "QuickServerDialog.h"
#include "ServerManager.h"
#include "ConnectionManager.h"
#include "StyleManager.h"

#include <QSplitter>
#include <QStatusBar>
#include <QMenuBar>
#include <QToolBar>
#include <QLabel>
#include <QProgressBar>
#include <QAction>
#include <QCloseEvent>
#include <QResizeEvent>
#include <QShowEvent>
#include <QKeyEvent>
#include <QSettings>
#include <QMessageBox>
#include <QApplication>
#include <QDesktopServices>
#include <QUrl>

MainWindow::MainWindow(QWidget* parent)
    : QMainWindow(parent)
    , m_desktopViewer(nullptr)
    , m_controlPanel(nullptr)
    , m_settingsDialog(nullptr)
    , m_quickServerDialog(nullptr)
    , m_serverManager(nullptr)
    , m_connectionManager(nullptr)
    , m_mainSplitter(nullptr)
    , m_menuBar(nullptr)
    , m_toolBar(nullptr)
    , m_statusBar(nullptr)
    , m_newConnectionAction(nullptr)
    , m_disconnectAction(nullptr)
    , m_startServerAction(nullptr)
    , m_stopServerAction(nullptr)
    , m_settingsAction(nullptr)
    , m_exitAction(nullptr)
    , m_aboutAction(nullptr)
    , m_toggleControlPanelAction(nullptr)
    , m_toggleStatusBarAction(nullptr)
    , m_toggleToolBarAction(nullptr)
    , m_fullScreenAction(nullptr)
    , m_quickSettingsAction(nullptr)
    , m_serverConfigAction(nullptr)
    , m_statusLabel(nullptr)
    , m_connectionStatusLabel(nullptr)
    , m_serverStatusLabel(nullptr)
    , m_clientCountLabel(nullptr)
    , m_progressBar(nullptr)
    , m_connectionState(ConnectionState::Disconnected)
    , m_serverState(ServerState::Stopped)
    , m_connectedClients(0)
    , m_isFullScreen(false)
    , m_isInitialized(false)
    , m_settings(nullptr) {
    
    initialize();
}

MainWindow::~MainWindow() {
    saveSettings();
    
    // Clean up components
    delete m_serverManager;
    delete m_connectionManager;
    delete m_settings;
    
    HVNC_DEBUG() << "MainWindow destroyed";
}

void MainWindow::initialize() {
    if (m_isInitialized) return;
    
    HVNC_DEBUG() << "Initializing MainWindow";
    
    // Initialize settings
    m_settings = new QSettings(this);
    
    // Setup UI
    setupUI();
    
    // Create core components
    m_serverManager = new ServerManager(this);
    m_connectionManager = new ConnectionManager(this);
    
    // Setup connections
    setupConnections();
    
    // Apply styles
    applyStyles();
    
    // Load settings
    loadSettings();
    
    // Update initial state
    updateUIState();
    updateWindowTitle();
    
    m_isInitialized = true;
    HVNC_INFO() << "MainWindow initialized successfully";
}

void MainWindow::setupUI() {
    // Set window properties
    setMinimumSize(HVNCDesign::WINDOW_MIN_WIDTH, HVNCDesign::WINDOW_MIN_HEIGHT);
    resize(1400, 900);
    
    // Create menu bar
    createMenuBar();
    
    // Create toolbar
    createToolBar();
    
    // Create status bar
    createStatusBar();
    
    // Create central widget
    createCentralWidget();
}

void MainWindow::createMenuBar() {
    m_menuBar = menuBar();
    
    // File menu
    QMenu* fileMenu = m_menuBar->addMenu("&File");
    
    m_newConnectionAction = fileMenu->addAction("&New Connection...");
    m_newConnectionAction->setShortcut(QKeySequence::New);
    m_newConnectionAction->setStatusTip("Create a new connection to a client");
    
    m_disconnectAction = fileMenu->addAction("&Disconnect");
    m_disconnectAction->setShortcut(QKeySequence("Ctrl+D"));
    m_disconnectAction->setStatusTip("Disconnect from current client");
    m_disconnectAction->setEnabled(false);
    
    fileMenu->addSeparator();
    
    m_startServerAction = fileMenu->addAction("&Start Server");
    m_startServerAction->setShortcut(QKeySequence("Ctrl+S"));
    m_startServerAction->setStatusTip("Start the HVNC server");
    
    m_stopServerAction = fileMenu->addAction("St&op Server");
    m_stopServerAction->setShortcut(QKeySequence("Ctrl+Shift+S"));
    m_stopServerAction->setStatusTip("Stop the HVNC server");
    m_stopServerAction->setEnabled(false);
    
    fileMenu->addSeparator();
    
    m_settingsAction = fileMenu->addAction("&Settings...");
    m_settingsAction->setShortcut(QKeySequence::Preferences);
    m_settingsAction->setStatusTip("Open application settings");
    
    fileMenu->addSeparator();
    
    m_exitAction = fileMenu->addAction("E&xit");
    m_exitAction->setShortcut(QKeySequence::Quit);
    m_exitAction->setStatusTip("Exit the application");
    
    // View menu
    QMenu* viewMenu = m_menuBar->addMenu("&View");
    
    m_toggleControlPanelAction = viewMenu->addAction("&Control Panel");
    m_toggleControlPanelAction->setCheckable(true);
    m_toggleControlPanelAction->setChecked(true);
    m_toggleControlPanelAction->setShortcut(QKeySequence("Ctrl+1"));
    
    m_toggleToolBarAction = viewMenu->addAction("&Toolbar");
    m_toggleToolBarAction->setCheckable(true);
    m_toggleToolBarAction->setChecked(true);
    m_toggleToolBarAction->setShortcut(QKeySequence("Ctrl+2"));
    
    m_toggleStatusBarAction = viewMenu->addAction("&Status Bar");
    m_toggleStatusBarAction->setCheckable(true);
    m_toggleStatusBarAction->setChecked(true);
    m_toggleStatusBarAction->setShortcut(QKeySequence("Ctrl+3"));
    
    viewMenu->addSeparator();
    
    m_fullScreenAction = viewMenu->addAction("&Full Screen");
    m_fullScreenAction->setCheckable(true);
    m_fullScreenAction->setShortcut(QKeySequence::FullScreen);
    
    // Help menu
    QMenu* helpMenu = m_menuBar->addMenu("&Help");
    
    m_aboutAction = helpMenu->addAction("&About");
    m_aboutAction->setStatusTip("Show application information");
}

void MainWindow::createToolBar() {
    m_toolBar = addToolBar("HVNC Control");
    m_toolBar->setObjectName("MainToolBar");
    m_toolBar->setMovable(false);
    m_toolBar->setToolButtonStyle(Qt::ToolButtonTextBesideIcon);

    // Connection actions
    m_toolBar->addAction(m_newConnectionAction);
    m_toolBar->addAction(m_disconnectAction);
    m_toolBar->addSeparator();

    // Server control actions
    m_toolBar->addAction(m_startServerAction);
    m_toolBar->addAction(m_stopServerAction);
    m_toolBar->addSeparator();

    // Quick configuration actions
    m_serverConfigAction = new QAction(QIcon(":/icons/server_config.png"), "Server Config", this);
    m_serverConfigAction->setStatusTip("Quick server configuration");
    m_serverConfigAction->setToolTip("Configure server port, password, and connections");
    connect(m_serverConfigAction, &QAction::triggered, this, &MainWindow::showQuickServerConfig);
    m_toolBar->addAction(m_serverConfigAction);

    m_quickSettingsAction = new QAction(QIcon(":/icons/quick_settings.png"), "Settings", this);
    m_quickSettingsAction->setStatusTip("Open settings dialog");
    m_quickSettingsAction->setToolTip("Configure all HVNC settings");
    connect(m_quickSettingsAction, &QAction::triggered, this, &MainWindow::showSettingsDialog);
    m_toolBar->addAction(m_quickSettingsAction);

    m_toolBar->addSeparator();
    m_toolBar->addAction(m_toggleControlPanelAction);
}

void MainWindow::createStatusBar() {
    m_statusBar = statusBar();
    
    // Status label (main status message)
    m_statusLabel = new QLabel("Ready");
    m_statusBar->addWidget(m_statusLabel, 1);
    
    // Progress bar (hidden by default)
    m_progressBar = new QProgressBar();
    m_progressBar->setVisible(false);
    m_progressBar->setMaximumWidth(200);
    m_statusBar->addWidget(m_progressBar);
    
    // Connection status
    m_connectionStatusLabel = new QLabel("Disconnected");
    m_statusBar->addPermanentWidget(m_connectionStatusLabel);
    
    // Server status
    m_serverStatusLabel = new QLabel("Server: Stopped");
    m_statusBar->addPermanentWidget(m_serverStatusLabel);
    
    // Client count
    m_clientCountLabel = new QLabel("Clients: 0");
    m_statusBar->addPermanentWidget(m_clientCountLabel);
}

void MainWindow::createCentralWidget() {
    // Create main splitter
    m_mainSplitter = new QSplitter(Qt::Horizontal, this);
    setCentralWidget(m_mainSplitter);

    // Create desktop viewer
    m_desktopViewer = new DesktopViewer(this);

    // Create professional control panel
    m_controlPanel = new ControlPanel(this);
    m_controlPanel->setFixedWidth(HVNCDesign::CONTROL_PANEL_WIDTH);
    m_controlPanel->show(); // Ensure control panel is visible

    // Load current settings into control panel
    QSettings settings;
    m_controlPanel->setServerPort(settings.value("server/port", HVNCDesign::DEFAULT_SERVER_PORT).toInt());
    m_controlPanel->setServerPassword(settings.value("server/password", "admin").toString());

    // Add widgets to splitter
    m_mainSplitter->addWidget(m_desktopViewer);
    m_mainSplitter->addWidget(m_controlPanel);

    // Set splitter proportions
    m_mainSplitter->setStretchFactor(0, 1);  // Desktop viewer gets most space
    m_mainSplitter->setStretchFactor(1, 0);  // Control panel fixed width
}

void MainWindow::setupConnections() {
    // Menu actions
    connect(m_newConnectionAction, &QAction::triggered, this, &MainWindow::onNewConnection);
    connect(m_disconnectAction, &QAction::triggered, this, &MainWindow::onDisconnect);
    connect(m_startServerAction, &QAction::triggered, this, &MainWindow::onStartServer);
    connect(m_stopServerAction, &QAction::triggered, this, &MainWindow::onStopServer);
    connect(m_settingsAction, &QAction::triggered, this, &MainWindow::showSettingsDialog);
    connect(m_exitAction, &QAction::triggered, this, &MainWindow::onExit);
    connect(m_aboutAction, &QAction::triggered, this, &MainWindow::onAbout);
    
    // View actions
    connect(m_toggleControlPanelAction, &QAction::triggered, this, &MainWindow::onToggleControlPanel);
    connect(m_toggleToolBarAction, &QAction::triggered, this, &MainWindow::onToggleToolBar);
    connect(m_toggleStatusBarAction, &QAction::triggered, this, &MainWindow::onToggleStatusBar);
    connect(m_fullScreenAction, &QAction::triggered, this, &MainWindow::onToggleFullScreen);
    
    // Server manager connections
    if (m_serverManager) {
        connect(m_serverManager, &ServerManager::serverStateChanged, 
                this, &MainWindow::onServerStateChanged);
        connect(m_serverManager, &ServerManager::clientConnected,
                this, &MainWindow::onClientConnected);
        connect(m_serverManager, &ServerManager::clientDisconnected,
                this, &MainWindow::onClientDisconnected);
        connect(m_serverManager, &ServerManager::statusMessage,
                this, [this](const QString& message) {
                    onStatusMessage(message, 0);
                });
        connect(m_serverManager, &ServerManager::errorOccurred,
                this, &MainWindow::onErrorMessage);

        // Connect desktop image signal to viewer
        connect(m_serverManager, &ServerManager::desktopImageReceived,
                this, &MainWindow::onDesktopImageReceived);
    }
    
    // Connection manager connections
    if (m_connectionManager) {
        connect(m_connectionManager, &ConnectionManager::connectionStateChanged,
                this, &MainWindow::onConnectionStateChanged);
        connect(m_connectionManager, &ConnectionManager::statusMessage,
                this, [this](const QString& message) {
                    onStatusMessage(message, 0);
                });
        connect(m_connectionManager, &ConnectionManager::progressUpdate,
                this, &MainWindow::onProgressUpdate);
        connect(m_connectionManager, &ConnectionManager::errorOccurred,
                this, &MainWindow::onErrorMessage);
    }

    // Control panel connections
    if (m_controlPanel) {
        connect(m_controlPanel, &ControlPanel::startServerRequested,
                this, &MainWindow::onStartServerFromPanel);
        connect(m_controlPanel, &ControlPanel::stopServerRequested,
                this, &MainWindow::onStopServerFromPanel);
        connect(m_controlPanel, &ControlPanel::settingsChanged,
                this, &MainWindow::onControlPanelSettingsChanged);
        connect(m_controlPanel, &ControlPanel::disconnectClientRequested,
                this, &MainWindow::onDisconnectClientFromPanel);
    }
}

void MainWindow::applyStyles() {
    StyleManager& styleManager = getStyleManager();

    // Apply main window style
    setStyleSheet(styleManager.getMainWindowStyleSheet());

    // Apply toolbar style
    if (m_toolBar) {
        m_toolBar->setStyleSheet(styleManager.getToolbarStyleSheet());
    }

    // Apply status bar style
    if (m_statusBar) {
        m_statusBar->setStyleSheet(styleManager.getStatusBarStyleSheet());
    }

    // Apply menu style
    if (m_menuBar) {
        m_menuBar->setStyleSheet(styleManager.getMenuStyleSheet());
    }
}

void MainWindow::loadSettings() {
    if (!m_settings) return;

    // Window geometry
    restoreGeometry(m_settings->value("geometry").toByteArray());
    restoreState(m_settings->value("windowState").toByteArray());

    // UI visibility
    bool showControlPanel = m_settings->value("showControlPanel", true).toBool();
    bool showToolBar = m_settings->value("showToolBar", true).toBool();
    bool showStatusBar = m_settings->value("showStatusBar", true).toBool();

    m_toggleControlPanelAction->setChecked(showControlPanel);
    m_toggleToolBarAction->setChecked(showToolBar);
    m_toggleStatusBarAction->setChecked(showStatusBar);

    if (m_controlPanel) {
        m_controlPanel->setVisible(showControlPanel);
    }
    m_toolBar->setVisible(showToolBar);
    m_statusBar->setVisible(showStatusBar);

    // Load server configuration settings
    int serverPort = m_settings->value("server/port", HVNCDesign::DEFAULT_SERVER_PORT).toInt();
    QString serverPassword = m_settings->value("server/password", "admin").toString();
    int maxConnections = m_settings->value("server/maxConnections", 10).toInt();

    // Apply server settings to server manager
    if (m_serverManager) {
        m_serverManager->setServerPort(serverPort);
        m_serverManager->setMaxClients(maxConnections);
        HVNC_INFO() << "Loaded server settings - Port:" << serverPort << "Max connections:" << maxConnections;
    }

    // Apply server settings to control panel
    if (m_controlPanel) {
        m_controlPanel->setServerPort(serverPort);
        m_controlPanel->setServerPassword(serverPassword);
    }

    // Splitter state
    if (m_mainSplitter) {
        m_mainSplitter->restoreState(m_settings->value("splitterState").toByteArray());
    }

    HVNC_DEBUG() << "Settings loaded";
}

void MainWindow::saveSettings() {
    if (!m_settings) return;

    // Window geometry
    m_settings->setValue("geometry", saveGeometry());
    m_settings->setValue("windowState", saveState());

    // UI visibility
    m_settings->setValue("showControlPanel", m_toggleControlPanelAction->isChecked());
    m_settings->setValue("showToolBar", m_toggleToolBarAction->isChecked());
    m_settings->setValue("showStatusBar", m_toggleStatusBarAction->isChecked());

    // Splitter state
    if (m_mainSplitter) {
        m_settings->setValue("splitterState", m_mainSplitter->saveState());
    }

    m_settings->sync();
    HVNC_DEBUG() << "Settings saved";
}

void MainWindow::updateWindowTitle() {
    QString title = QString("%1 v%2").arg(HVNCDesign::APP_NAME).arg(HVNCDesign::APP_VERSION);

    if (m_connectionState == ConnectionState::Connected) {
        title += " - Connected";
    } else if (m_serverState == ServerState::Running) {
        title += QString(" - Server Running (Port: %1)").arg(HVNCDesign::DEFAULT_SERVER_PORT);
    }

    setWindowTitle(title);
}

void MainWindow::updateUIState() {
    // Update action states based on connection and server status
    bool isConnected = (m_connectionState == ConnectionState::Connected);
    bool isServerRunning = (m_serverState == ServerState::Running);
    bool isServerStopped = (m_serverState == ServerState::Stopped);

    m_disconnectAction->setEnabled(isConnected);
    m_startServerAction->setEnabled(isServerStopped);
    m_stopServerAction->setEnabled(isServerRunning);

    // Update status labels
    QString connectionText;
    switch (m_connectionState) {
        case ConnectionState::Disconnected:
            connectionText = "Disconnected";
            break;
        case ConnectionState::Connecting:
            connectionText = "Connecting...";
            break;
        case ConnectionState::Connected:
            connectionText = "Connected";
            break;
        case ConnectionState::Error:
            connectionText = "Connection Error";
            break;
    }
    m_connectionStatusLabel->setText(connectionText);

    QString serverText;
    switch (m_serverState) {
        case ServerState::Stopped:
            serverText = "Server: Stopped";
            break;
        case ServerState::Starting:
            serverText = "Server: Starting...";
            break;
        case ServerState::Running:
            serverText = QString("Server: Running (Port: %1)").arg(HVNCDesign::DEFAULT_SERVER_PORT);
            break;
        case ServerState::Stopping:
            serverText = "Server: Stopping...";
            break;
        case ServerState::Error:
            serverText = "Server: Error";
            break;
    }
    m_serverStatusLabel->setText(serverText);

    m_clientCountLabel->setText(QString("Clients: %1").arg(m_connectedClients));
}

// Event handlers
void MainWindow::closeEvent(QCloseEvent* event) {
    // Ask for confirmation if server is running or clients are connected
    if (m_serverState == ServerState::Running || m_connectedClients > 0) {
        QMessageBox::StandardButton reply = QMessageBox::question(
            this,
            "Confirm Exit",
            "The server is running or clients are connected. Are you sure you want to exit?",
            QMessageBox::Yes | QMessageBox::No,
            QMessageBox::No
        );

        if (reply != QMessageBox::Yes) {
            event->ignore();
            return;
        }
    }

    // Stop server if running
    if (m_serverState == ServerState::Running) {
        m_serverManager->stopServer();
    }

    saveSettings();
    event->accept();
}

void MainWindow::resizeEvent(QResizeEvent* event) {
    QMainWindow::resizeEvent(event);
    updateWindowTitle();
}

void MainWindow::showEvent(QShowEvent* event) {
    QMainWindow::showEvent(event);
    if (!m_isInitialized) {
        initialize();
    }
}

void MainWindow::keyPressEvent(QKeyEvent* event) {
    // Handle global key shortcuts
    if (event->key() == Qt::Key_Escape && m_isFullScreen) {
        onToggleFullScreen();
        return;
    }

    QMainWindow::keyPressEvent(event);
}

// Slot implementations
void MainWindow::onNewConnection() {
    showConnectionDialog();
}

void MainWindow::onDisconnect() {
    if (m_connectionManager) {
        m_connectionManager->disconnect();
    }
}

void MainWindow::onStartServer() {
    if (m_serverManager) {
        // Load current port from settings
        QSettings settings;
        int port = settings.value("server/port", HVNCDesign::DEFAULT_SERVER_PORT).toInt();
        QString password = settings.value("server/password", "admin").toString();
        int maxConnections = settings.value("server/maxConnections", 10).toInt();

        HVNC_INFO() << "Starting server from toolbar/menu - Port:" << port;

        // Configure server with current settings
        m_serverManager->setServerPort(port);
        m_serverManager->setMaxClients(maxConnections);

        // Start the server with the configured port
        if (m_serverManager->startServer(port)) {
            onStatusMessage(QString("HVNC server started on port %1").arg(port), 5000);

            // Update control panel status if it exists
            if (m_controlPanel) {
                m_controlPanel->updateServerStatus(true, port, 0);
                m_controlPanel->addLogMessage("INFO",
                    QString("Server started from toolbar on port %1").arg(port));
            }
        } else {
            onErrorMessage("Server Error", "Failed to start HVNC server");

            if (m_controlPanel) {
                m_controlPanel->addLogMessage("ERROR", "Failed to start server from toolbar");
            }
        }
    }
}

void MainWindow::onStopServer() {
    if (m_serverManager) {
        m_serverManager->stopServer();
    }
}

void MainWindow::onSettings() {
    showSettingsDialog();
}

void MainWindow::onAbout() {
    showAboutDialog();
}

void MainWindow::onExit() {
    close();
}

void MainWindow::onToggleControlPanel() {
    bool visible = m_toggleControlPanelAction->isChecked();
    if (m_controlPanel) {
        m_controlPanel->setVisible(visible);
    }
}

void MainWindow::onToggleStatusBar() {
    bool visible = m_toggleStatusBarAction->isChecked();
    m_statusBar->setVisible(visible);
}

void MainWindow::onToggleToolBar() {
    bool visible = m_toggleToolBarAction->isChecked();
    m_toolBar->setVisible(visible);
}

void MainWindow::onToggleFullScreen() {
    if (m_isFullScreen) {
        showNormal();
        m_isFullScreen = false;
        m_fullScreenAction->setChecked(false);
    } else {
        showFullScreen();
        m_isFullScreen = true;
        m_fullScreenAction->setChecked(true);
    }
}

void MainWindow::onConnectionStateChanged(ConnectionState state) {
    m_connectionState = state;
    updateUIState();
    updateWindowTitle();

    QString message;
    switch (state) {
        case ConnectionState::Disconnected:
            message = "Disconnected from client";
            break;
        case ConnectionState::Connecting:
            message = "Connecting to client...";
            break;
        case ConnectionState::Connected:
            message = "Connected to client";
            break;
        case ConnectionState::Error:
            message = "Connection error occurred";
            break;
    }

    onStatusMessage(message, 3000);
}

void MainWindow::onServerStateChanged(ServerState state) {
    m_serverState = state;
    updateUIState();
    updateWindowTitle();

    QString message;
    switch (state) {
        case ServerState::Stopped:
            message = "Server stopped";
            break;
        case ServerState::Starting:
            message = "Starting server...";
            break;
        case ServerState::Running:
            message = QString("Server started on port %1").arg(HVNCDesign::DEFAULT_SERVER_PORT);
            break;
        case ServerState::Stopping:
            message = "Stopping server...";
            break;
        case ServerState::Error:
            message = "Server error occurred";
            break;
    }

    onStatusMessage(message, 3000);
}

void MainWindow::onClientConnected(ClientId clientId, const QString& clientInfo) {
    m_connectedClients++;
    updateUIState();

    QString message = QString("Client connected: %1 (ID: %2)")
                     .arg(clientInfo)
                     .arg(clientId);
    onStatusMessage(message, 5000);

    HVNC_INFO() << "Client connected:" << clientInfo << "ID:" << clientId;
}

void MainWindow::onClientDisconnected(ClientId clientId) {
    if (m_connectedClients > 0) {
        m_connectedClients--;
    }
    updateUIState();

    QString message = QString("Client disconnected (ID: %1)").arg(clientId);
    onStatusMessage(message, 3000);

    HVNC_INFO() << "Client disconnected, ID:" << clientId;
}

void MainWindow::onDesktopImageReceived(ClientId clientId, const QImage& image) {
    HVNC_INFO() << "Desktop image received from client" << clientId
                << "Size:" << image.width() << "x" << image.height();

    // Update the desktop viewer with the received image
    if (m_desktopViewer) {
        m_desktopViewer->setDesktopImage(image);

        // Update status
        QString message = QString("Desktop updated from client %1 (%2x%3)")
                         .arg(clientId)
                         .arg(image.width())
                         .arg(image.height());
        onStatusMessage(message, 2000);
    }

    // Update control panel if it exists
    if (m_controlPanel) {
        m_controlPanel->addLogMessage("INFO",
            QString("Desktop image received from client %1 (%2x%3)")
            .arg(clientId).arg(image.width()).arg(image.height()));
    }
}

void MainWindow::onStatusMessage(const QString& message, int timeout) {
    if (m_statusLabel) {
        m_statusLabel->setText(message);

        if (timeout > 0) {
            QTimer::singleShot(timeout, [this]() {
                m_statusLabel->setText("Ready");
            });
        }
    }

    HVNC_DEBUG() << "Status:" << message;
}

void MainWindow::onProgressUpdate(int value, const QString& text) {
    if (!m_progressBar) return;

    if (value >= 0 && value <= 100) {
        m_progressBar->setValue(value);
        m_progressBar->setVisible(true);

        if (!text.isEmpty()) {
            m_progressBar->setFormat(text + " (%p%)");
        }

        if (value >= 100) {
            QTimer::singleShot(1000, [this]() {
                m_progressBar->setVisible(false);
            });
        }
    } else {
        m_progressBar->setVisible(false);
    }
}

void MainWindow::onErrorMessage(const QString& title, const QString& message) {
    QMessageBox::critical(this, title, message);
    HVNC_CRITICAL() << title << ":" << message;
}

void MainWindow::showConnectionDialog() {
    // TODO: Implement connection dialog
    onStatusMessage("Connection dialog not yet implemented", 3000);
}

void MainWindow::showSettingsDialog() {
    if (!m_settingsDialog) {
        m_settingsDialog = new SettingsDialog(this);

        // Connect settings dialog signals
        connect(m_settingsDialog, &SettingsDialog::settingsApplied,
                this, &MainWindow::onSettingsApplied);
        connect(m_settingsDialog, &SettingsDialog::serverConfigChanged,
                this, &MainWindow::onServerConfigFromSettings);
    }

    // Load current settings
    m_settingsDialog->loadSettings();

    // Show dialog
    if (m_settingsDialog->exec() == QDialog::Accepted) {
        onStatusMessage("Settings applied successfully", 3000);
    }
}

void MainWindow::showQuickServerConfig() {
    if (!m_quickServerDialog) {
        m_quickServerDialog = new QuickServerDialog(this);

        // Connect quick server dialog signals
        connect(m_quickServerDialog, &QuickServerDialog::serverConfigChanged,
                this, &MainWindow::onQuickServerConfigChanged);
    }

    // Load current settings
    QSettings settings;
    m_quickServerDialog->setServerPort(settings.value("server/port", 4444).toInt());
    m_quickServerDialog->setServerPassword(settings.value("server/password", "admin").toString());
    m_quickServerDialog->setMaxConnections(settings.value("server/maxConnections", 10).toInt());
    m_quickServerDialog->setAutoStartEnabled(settings.value("server/autoStart", false).toBool());

    // Show quick configuration dialog
    if (m_quickServerDialog->exec() == QDialog::Accepted) {
        onStatusMessage("Server configuration updated", 3000);
    }
}

void MainWindow::showAboutDialog() {
    QString aboutText = QString(
        "<h2>%1</h2>"
        "<p>Version: %2</p>"
        "<p>A professional Qt6-based GUI for HVNC (Hidden Virtual Network Computing) server management.</p>"
        "<p>Built with modern C++20 and Qt6 framework.</p>"
        "<p><b>Features:</b></p>"
        "<ul>"
        "<li>Real-time desktop viewing and control</li>"
        "<li>Professional dark theme interface</li>"
        "<li>Multi-client server management</li>"
        "<li>Advanced event handling</li>"
        "</ul>"
        "<p>© 2024 HVNC Design Team</p>"
    ).arg(HVNCDesign::APP_NAME).arg(HVNCDesign::APP_VERSION);

    QMessageBox::about(this, QString("About %1").arg(HVNCDesign::APP_NAME), aboutText);
}

// Control Panel Slot Implementations
void MainWindow::onStartServerFromPanel(int port, const QString& password, int maxConnections)
{
    HVNC_INFO() << "Starting server from control panel - Port:" << port
                << "Max connections:" << maxConnections;

    if (m_serverManager) {
        // Configure server with control panel settings
        m_serverManager->setServerPort(port);
        m_serverManager->setMaxClients(maxConnections);

        // Start the server
        if (m_serverManager->startServer(port)) {
            onStatusMessage(QString("HVNC server started on port %1").arg(port), 5000);

            // Update control panel status
            if (m_controlPanel) {
                m_controlPanel->updateServerStatus(true, port, 0);
                m_controlPanel->addLogMessage("INFO",
                    QString("Server started successfully on port %1").arg(port));
            }
        } else {
            onErrorMessage("Server Error", "Failed to start HVNC server");

            if (m_controlPanel) {
                m_controlPanel->addLogMessage("ERROR", "Failed to start server");
            }
        }
    }
}

void MainWindow::onStopServerFromPanel()
{
    HVNC_INFO() << "Stopping server from control panel";

    if (m_serverManager) {
        m_serverManager->stopServer();
        onStatusMessage("HVNC server stopped", 3000);

        // Update control panel status
        if (m_controlPanel) {
            m_controlPanel->updateServerStatus(false, 0, 0);
            m_controlPanel->addLogMessage("INFO", "Server stopped");
        }
    }
}

void MainWindow::onControlPanelSettingsChanged()
{
    HVNC_DEBUG() << "Control panel settings changed";

    if (m_controlPanel && m_serverManager) {
        // Apply new settings to server manager
        int compressionLevel = m_controlPanel->getCompressionLevel();
        int updateInterval = m_controlPanel->getUpdateInterval();

        // TODO: Apply these settings to the server manager
        // m_serverManager->setCompressionLevel(compressionLevel);
        // m_serverManager->setUpdateInterval(updateInterval);

        onStatusMessage("Settings updated", 2000);
    }
}

void MainWindow::onDisconnectClientFromPanel(const QString& clientId)
{
    HVNC_INFO() << "Disconnecting client from control panel:" << clientId;

    if (m_serverManager) {
        // TODO: Implement client disconnection
        // m_serverManager->disconnectClient(clientId);

        if (m_controlPanel) {
            m_controlPanel->removeConnection(clientId);
            m_controlPanel->addLogMessage("INFO",
                QString("Client %1 disconnected by user").arg(clientId));
        }

        onStatusMessage(QString("Client %1 disconnected").arg(clientId), 3000);
    }
}

void MainWindow::onSettingsApplied()
{
    HVNC_INFO() << "Settings applied from settings dialog";

    // Update UI based on new settings
    updateUIState();

    // Apply any interface settings
    // TODO: Apply theme, logging level, etc.

    onStatusMessage("Settings applied successfully", 3000);
}

void MainWindow::onServerConfigFromSettings(int port, const QString& password, int maxConnections)
{
    HVNC_INFO() << "Server configuration updated from settings - Port:" << port
                << "Max connections:" << maxConnections;

    if (m_serverManager) {
        // Update server manager with new configuration
        m_serverManager->setServerPort(port);
        m_serverManager->setMaxClients(maxConnections);

        // If server is running, show warning about restart requirement
        if (m_serverManager->isRunning()) {
            QMessageBox::information(this, "Server Configuration",
                "Server configuration updated. Please restart the server for changes to take effect.");
        }
    }

    // Update control panel if it exists
    if (m_controlPanel) {
        m_controlPanel->setServerPort(port);
        m_controlPanel->setServerPassword(password);
        m_controlPanel->addLogMessage("INFO",
            QString("Server configuration updated - Port: %1, Max connections: %2")
            .arg(port).arg(maxConnections));
    }

    onStatusMessage(QString("Server configured for port %1").arg(port), 3000);
}

void MainWindow::onQuickServerConfigChanged(int port, const QString& password, int maxConnections, bool autoStart)
{
    HVNC_INFO() << "Quick server configuration updated - Port:" << port
                << "Max connections:" << maxConnections << "Auto-start:" << autoStart;

    if (m_serverManager) {
        // Update server manager with new configuration
        m_serverManager->setServerPort(port);
        m_serverManager->setMaxClients(maxConnections);

        // If auto-start is enabled and server is not running, start it
        if (autoStart && !m_serverManager->isRunning()) {
            if (m_serverManager->startServer(port)) {
                onStatusMessage(QString("Server auto-started on port %1").arg(port), 5000);
            }
        }
    }

    // Update control panel if it exists
    if (m_controlPanel) {
        m_controlPanel->setServerPort(port);
        m_controlPanel->setServerPassword(password);
        m_controlPanel->addLogMessage("INFO",
            QString("Quick server config - Port: %1, Max connections: %2, Auto-start: %3")
            .arg(port).arg(maxConnections).arg(autoStart ? "Yes" : "No"));
    }

    onStatusMessage(QString("Quick server setup complete - Port %1").arg(port), 3000);
}
