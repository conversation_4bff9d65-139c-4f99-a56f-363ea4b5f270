@echo off
:: ============================================================================
:: HVNC Design Manual Windows Build Script
:: ============================================================================
:: Manual compilation bypassing qmake MSVC compatibility issues completely
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                    HVNC Design Manual Windows Build                         ║%NC%
echo %BLUE%║                    Bypassing qmake Completely - Direct Compilation          ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Step 1: Setup environment
echo %CYAN%[STEP 1]%NC% Setting up build environment...

set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64"
set "PATH=%QT6_PATH%\bin;%PATH%"

echo %GREEN%[CONFIG]%NC% Qt6 path: %QT6_PATH%

:: Step 2: Setup Visual Studio
echo %CYAN%[STEP 2]%NC% Setting up Visual Studio...

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1

if errorlevel 1 (
    echo %RED%[ERROR]%NC% Visual Studio setup failed
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Visual Studio environment loaded

:: Step 3: Prepare directories
echo %CYAN%[STEP 3]%NC% Preparing build directories...

if exist "manual_build" rmdir /s /q "manual_build" 2>nul
mkdir "manual_build" 2>nul

echo %GREEN%[READY]%NC% Build directory prepared

:: Step 4: Generate Qt MOC files
echo %CYAN%[STEP 4]%NC% Generating Qt MOC files...

cd manual_build

:: Generate MOC files for Qt headers
"%QT6_PATH%\bin\moc.exe" ..\include\MainWindow.h -o moc_MainWindow.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\DesktopViewer.h -o moc_DesktopViewer.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\ServerManager.h -o moc_ServerManager.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\ConnectionManager.h -o moc_ConnectionManager.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\ImageProcessor.h -o moc_ImageProcessor.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\StyleManager.h -o moc_StyleManager.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\ControlPanel.h -o moc_ControlPanel.cpp
"%QT6_PATH%\bin\moc.exe" ..\include\SettingsDialog.h -o moc_SettingsDialog.cpp

if errorlevel 1 (
    echo %RED%[FAILED]%NC% MOC generation failed
    cd ..
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% MOC files generated

:: Step 5: Manual compilation
echo %CYAN%[STEP 5]%NC% Compiling manually with MSVC...

:: Set compiler flags (with Qt6 requirements)
set "CXXFLAGS=/std:c++20 /Zc:__cplusplus /EHsc /MD /O2 /DWIN32_LEAN_AND_MEAN /DNOMINMAX /DWINDOWS_PLATFORM /D_WIN32_WINNT=0x0601"
set "INCLUDES=/I..\include /I%QT6_PATH%\include /I%QT6_PATH%\include\QtCore /I%QT6_PATH%\include\QtWidgets /I%QT6_PATH%\include\QtNetwork /I%QT6_PATH%\include\QtConcurrent /I%QT6_PATH%\include\QtGui /I."
set "LIBS=%QT6_PATH%\lib\Qt6Core.lib %QT6_PATH%\lib\Qt6Widgets.lib %QT6_PATH%\lib\Qt6Network.lib %QT6_PATH%\lib\Qt6Concurrent.lib %QT6_PATH%\lib\Qt6Gui.lib user32.lib gdi32.lib kernel32.lib advapi32.lib shell32.lib ws2_32.lib ntdll.lib"

:: Compile source files
echo %BLUE%[COMPILE]%NC% Compiling source files...

cl %CXXFLAGS% %INCLUDES% /c ..\src\main.cpp /Fo:main.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\MainWindow.cpp /Fo:MainWindow.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\DesktopViewer.cpp /Fo:DesktopViewer.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\ServerManager.cpp /Fo:ServerManager.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\StyleManager.cpp /Fo:StyleManager.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\ImageProcessor.cpp /Fo:ImageProcessor.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\ConnectionManager.cpp /Fo:ConnectionManager.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\ControlPanel.cpp /Fo:ControlPanel.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c ..\src\SettingsDialog.cpp /Fo:SettingsDialog.obj
if errorlevel 1 goto :compile_error

:: Compile MOC files
echo %BLUE%[COMPILE]%NC% Compiling MOC files...

cl %CXXFLAGS% %INCLUDES% /c moc_MainWindow.cpp /Fo:moc_MainWindow.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_DesktopViewer.cpp /Fo:moc_DesktopViewer.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_ServerManager.cpp /Fo:moc_ServerManager.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_ConnectionManager.cpp /Fo:moc_ConnectionManager.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_ImageProcessor.cpp /Fo:moc_ImageProcessor.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_StyleManager.cpp /Fo:moc_StyleManager.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_ControlPanel.cpp /Fo:moc_ControlPanel.obj
if errorlevel 1 goto :compile_error

cl %CXXFLAGS% %INCLUDES% /c moc_SettingsDialog.cpp /Fo:moc_SettingsDialog.obj
if errorlevel 1 goto :compile_error

echo %GREEN%[SUCCESS]%NC% All source files compiled

:: Step 6: Link executable
echo %CYAN%[STEP 6]%NC% Linking executable...

link /OUT:HVNCDesign.exe /SUBSYSTEM:CONSOLE ^
    main.obj MainWindow.obj DesktopViewer.obj ServerManager.obj ^
    StyleManager.obj ImageProcessor.obj ConnectionManager.obj ControlPanel.obj SettingsDialog.obj ^
    moc_MainWindow.obj moc_DesktopViewer.obj moc_ServerManager.obj ^
    moc_ConnectionManager.obj moc_ImageProcessor.obj moc_StyleManager.obj moc_ControlPanel.obj moc_SettingsDialog.obj ^
    %LIBS%

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Linking failed
    cd ..
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Executable linked successfully!

:: Step 7: Deploy
echo %CYAN%[STEP 7]%NC% Deploying executable...

if exist "HVNCDesign.exe" (
    copy "HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    copy "HVNCDesign.exe" "..\..\HVNCDesign.exe" >nul
    
    echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe deployed to root directory
    
    for %%F in ("HVNCDesign.exe") do (
        echo %CYAN%[INFO]%NC% File size: %%~zF bytes
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found after linking
    cd ..
    pause
    exit /b 1
)

cd ..

:: Success!
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                        MANUAL BUILD SUCCESSFUL!                             ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Application:%NC%
echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for Windows (Manual Build)

echo.
echo %CYAN%[TEST]%NC% Would you like to test launch the GUI? (y/n)
set /p "test_launch=Enter choice: "

if /i "!test_launch!"=="y" (
    echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe...
    
    if exist "..\HVNCDesign.exe" (
        start "" "..\HVNCDesign.exe"
        echo %GREEN%[SUCCESS]%NC% GUI launched!
    ) else (
        echo %RED%[ERROR]%NC% Executable not found
    )
)

echo.
echo %GREEN%[COMPLETE]%NC% Manual build finished!
pause
exit /b 0

:compile_error
echo %RED%[FAILED]%NC% Compilation failed
cd ..
pause
exit /b 1
