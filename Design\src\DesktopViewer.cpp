/**
 * @file DesktopViewer.cpp
 * @brief Desktop viewer widget implementation
 * <AUTHOR> Design Team
 * @version 1.0.0
 */

#include "DesktopViewer.h"
#include <QGraphicsScene>
#include <QGraphicsPixmapItem>
#include <QWheelEvent>
#include <QMouseEvent>
#include <QKeyEvent>
#include <QContextMenuEvent>
#include <QMenu>
#include <QApplication>
#include <QClipboard>
#include <QFileDialog>
#include <QDateTime>
#include <QElapsedTimer>
#include <QTimer>
#include <QLabel>
#include <QRubberBand>

DesktopViewer::DesktopViewer(QWidget* parent)
    : QGraphicsView(parent)
    , m_scene(nullptr)
    , m_desktopItem(nullptr)
    , m_eventHandler(nullptr)
    , m_imageProcessor(nullptr)
    , m_desktopSize(0, 0)
    , m_cursorPosition(0, 0)
    , m_scalingMode(ScalingMode::FitToWindow)
    , m_scaleFactor(1.0)
    , m_minScaleFactor(0.1)
    , m_maxScaleFactor(5.0)
    , m_inputForwardingEnabled(true)
    , m_cursorVisible(true)
    , m_selectionEnabled(false)
    , m_selectionBand(nullptr)
    , m_selectionStart(0, 0)
    , m_overlayLabel(nullptr)
    , m_cursorBlinkTimer(nullptr)
    , m_fpsTimer(new QTimer(this))
    , m_frameCount(0)
    , m_lastFpsUpdate(0)
    , m_currentFps(0.0)
    , m_isInitialized(false)
    , m_isDragging(false)
    , m_isSelecting(false)
{
    initialize();
}

DesktopViewer::~DesktopViewer() {
    // Basic cleanup
}

void DesktopViewer::initialize() {
    if (m_isInitialized) return;
    
    // Create scene
    m_scene = new QGraphicsScene(this);
    setScene(m_scene);
    
    // Create desktop item
    m_desktopItem = m_scene->addPixmap(QPixmap());
    
    // Set view properties
    setDragMode(QGraphicsView::RubberBandDrag);
    setRenderHint(QPainter::Antialiasing, false);
    setRenderHint(QPainter::SmoothPixmapTransform, true);
    
    // Connect FPS timer
    connect(m_fpsTimer, &QTimer::timeout, this, [this]() {
        // Update FPS calculation
        m_currentFps = m_frameCount;
        m_frameCount = 0;
    });
    m_fpsTimer->start(1000); // Update every second
    
    m_isInitialized = true;
}

void DesktopViewer::setDesktopImage(const QImage& image) {
    if (image.isNull()) return;
    
    m_frameCount++;
    m_desktopImage = image;
    m_desktopSize = image.size();
    
    if (m_desktopItem) {
        QPixmap pixmap = QPixmap::fromImage(image);
        m_desktopItem->setPixmap(pixmap);
        
        // Apply current scaling
        applyScaling();
        
        emit desktopSizeChanged(image.size());
    }
}

void DesktopViewer::setScalingMode(ScalingMode mode) {
    if (m_scalingMode != mode) {
        m_scalingMode = mode;
        applyScaling();
        emit scalingChanged(m_scaleFactor);
    }
}

void DesktopViewer::setInputForwardingEnabled(bool enabled) {
    if (m_inputForwardingEnabled != enabled) {
        m_inputForwardingEnabled = enabled;
        // Signal not defined in header - remove emit
    }
}

void DesktopViewer::setCursorVisible(bool visible) {
    if (m_cursorVisible != visible) {
        m_cursorVisible = visible;
        update();
        // Signal not defined in header - remove emit
    }
}

void DesktopViewer::updateCursorPosition(const QPoint& position) {
    if (m_cursorPosition != position) {
        m_cursorPosition = position;
        update();
        // Signal not defined in header - remove emit
    }
}

void DesktopViewer::wheelEvent(QWheelEvent* event) {
    if (event->modifiers() & Qt::ControlModifier) {
        // Zoom with Ctrl+Wheel
        const double scaleFactor = 1.15;
        if (event->angleDelta().y() > 0) {
            scale(scaleFactor, scaleFactor);
        } else {
            scale(1.0 / scaleFactor, 1.0 / scaleFactor);
        }
        event->accept();
    } else {
        QGraphicsView::wheelEvent(event);
    }
}

void DesktopViewer::mousePressEvent(QMouseEvent* event) {
    if (m_inputForwardingEnabled) {
        QPoint desktopPos = mapToScene(event->pos()).toPoint();
        emit mouseEventOccurred(event, desktopPos);
    }
    QGraphicsView::mousePressEvent(event);
}

void DesktopViewer::mouseReleaseEvent(QMouseEvent* event) {
    if (m_inputForwardingEnabled) {
        QPoint desktopPos = mapToScene(event->pos()).toPoint();
        emit mouseEventOccurred(event, desktopPos);
    }
    QGraphicsView::mouseReleaseEvent(event);
}

void DesktopViewer::mouseMoveEvent(QMouseEvent* event) {
    if (m_inputForwardingEnabled) {
        QPoint desktopPos = mapToScene(event->pos()).toPoint();
        // No mouse move signal in header - remove emit
    }
    QGraphicsView::mouseMoveEvent(event);
}

void DesktopViewer::keyPressEvent(QKeyEvent* event) {
    if (m_inputForwardingEnabled) {
        emit keyEventOccurred(event);
    }
    QGraphicsView::keyPressEvent(event);
}

void DesktopViewer::keyReleaseEvent(QKeyEvent* event) {
    if (m_inputForwardingEnabled) {
        emit keyEventOccurred(event);
    }
    QGraphicsView::keyReleaseEvent(event);
}

void DesktopViewer::contextMenuEvent(QContextMenuEvent* event) {
    showContextMenu(event->pos());
}

void DesktopViewer::showContextMenu(const QPoint& position) {
    QMenu contextMenu(this);
    
    contextMenu.addAction("Fit to Window", this, [this]() {
        setScalingMode(ScalingMode::FitToWindow);
    });
    
    contextMenu.addAction("Actual Size", this, [this]() {
        setScalingMode(ScalingMode::ActualSize);
    });
    
    contextMenu.addSeparator();
    
    QAction* inputAction = contextMenu.addAction("Forward Input");
    inputAction->setCheckable(true);
    inputAction->setChecked(m_inputForwardingEnabled);
    connect(inputAction, &QAction::toggled, this, &DesktopViewer::setInputForwardingEnabled);
    
    QAction* cursorAction = contextMenu.addAction("Show Cursor");
    cursorAction->setCheckable(true);
    cursorAction->setChecked(m_cursorVisible);
    connect(cursorAction, &QAction::toggled, this, &DesktopViewer::setCursorVisible);
    
    contextMenu.exec(mapToGlobal(position));
}

void DesktopViewer::applyScaling() {
    if (!m_desktopItem || m_desktopItem->pixmap().isNull()) {
        return;
    }
    
    QTransform transform;
    
    switch (m_scalingMode) {
        case ScalingMode::FitToWindow: {
            QSize viewSize = viewport()->size();
            QSize pixmapSize = m_desktopItem->pixmap().size();
            
            if (!pixmapSize.isEmpty()) {
                double scaleX = double(viewSize.width()) / pixmapSize.width();
                double scaleY = double(viewSize.height()) / pixmapSize.height();
                double scale = qMin(scaleX, scaleY);
                
                scale = qBound(m_minScaleFactor, scale, m_maxScaleFactor);
                transform.scale(scale, scale);
                m_scaleFactor = scale;
            }
            break;
        }
        case ScalingMode::ActualSize:
            m_scaleFactor = 1.0;
            break;
        case ScalingMode::CustomScale:
            transform.scale(m_scaleFactor, m_scaleFactor);
            break;
    }
    
    m_desktopItem->setTransform(transform);
    
    // Center the item
    centerOn(m_desktopItem);
}

QPoint DesktopViewer::desktopToViewCoordinates(const QPoint& desktopPos) const {
    if (m_desktopItem) {
        QPointF scenePos = m_desktopItem->mapToScene(desktopPos);
        return mapFromScene(scenePos);
    }
    return desktopPos;
}

void DesktopViewer::updateSelection(const QRect& rect) {
    // Basic selection update - can be expanded later
    Q_UNUSED(rect)
}

double DesktopViewer::calculateFitToWindowScale() const {
    if (!m_desktopItem || m_desktopItem->pixmap().isNull()) {
        return 1.0;
    }

    QSize viewSize = viewport()->size();
    QSize pixmapSize = m_desktopItem->pixmap().size();

    if (pixmapSize.isEmpty()) {
        return 1.0;
    }

    double scaleX = double(viewSize.width()) / pixmapSize.width();
    double scaleY = double(viewSize.height()) / pixmapSize.height();

    return qBound(m_minScaleFactor, qMin(scaleX, scaleY), m_maxScaleFactor);
}

void DesktopViewer::mouseDoubleClickEvent(QMouseEvent* event) {
    if (m_inputForwardingEnabled) {
        QPoint desktopPos = mapToScene(event->pos()).toPoint();
        emit mouseEventOccurred(event, desktopPos);
    }
    QGraphicsView::mouseDoubleClickEvent(event);
}

void DesktopViewer::resizeEvent(QResizeEvent* event) {
    QGraphicsView::resizeEvent(event);
    applyScaling();
}

void DesktopViewer::paintEvent(QPaintEvent* event) {
    QGraphicsView::paintEvent(event);
}

void DesktopViewer::focusInEvent(QFocusEvent* event) {
    QGraphicsView::focusInEvent(event);
    // Focus handling can be added later if needed
}

void DesktopViewer::focusOutEvent(QFocusEvent* event) {
    QGraphicsView::focusOutEvent(event);
    // Focus handling can be added later if needed
}

void DesktopViewer::onCursorBlink() {
    m_cursorVisible = !m_cursorVisible;
    update();
}

void DesktopViewer::onFpsUpdate() {
    // Calculate FPS
    qint64 currentTime = QDateTime::currentMSecsSinceEpoch();
    if (m_lastFpsUpdate > 0) {
        double elapsed = (currentTime - m_lastFpsUpdate) / 1000.0;
        if (elapsed > 0) {
            m_currentFps = m_frameCount / elapsed;
        }
    }

    // Reset counters
    m_frameCount = 0;
    m_lastFpsUpdate = currentTime;
}
