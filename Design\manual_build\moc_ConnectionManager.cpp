/****************************************************************************
** Meta object code from reading C++ file 'ConnectionManager.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../include/ConnectionManager.h"
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConnectionManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSConnectionManagerENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSConnectionManagerENDCLASS = QtMocHelpers::stringData(
    "ConnectionManager",
    "connectionStateChanged",
    "",
    "ConnectionState",
    "state",
    "dataReceived",
    "data",
    "connected",
    "disconnected",
    "statusMessage",
    "message",
    "progressUpdate",
    "value",
    "text",
    "errorOccurred",
    "title",
    "dataTransferUpdate",
    "bytesReceived",
    "bytesSent",
    "onSocketConnected",
    "onSocketDisconnected",
    "onSocketDataReady",
    "onSocketError",
    "QAbstractSocket::SocketError",
    "error",
    "onSocketStateChanged",
    "QAbstractSocket::SocketState",
    "onConnectionTimeout",
    "onKeepAliveTimer",
    "onAuthenticationTimeout"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSConnectionManagerENDCLASS_t {
    uint offsetsAndSizes[60];
    char stringdata0[18];
    char stringdata1[23];
    char stringdata2[1];
    char stringdata3[16];
    char stringdata4[6];
    char stringdata5[13];
    char stringdata6[5];
    char stringdata7[10];
    char stringdata8[13];
    char stringdata9[14];
    char stringdata10[8];
    char stringdata11[15];
    char stringdata12[6];
    char stringdata13[5];
    char stringdata14[14];
    char stringdata15[6];
    char stringdata16[19];
    char stringdata17[14];
    char stringdata18[10];
    char stringdata19[18];
    char stringdata20[21];
    char stringdata21[18];
    char stringdata22[14];
    char stringdata23[29];
    char stringdata24[6];
    char stringdata25[21];
    char stringdata26[29];
    char stringdata27[20];
    char stringdata28[17];
    char stringdata29[24];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSConnectionManagerENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSConnectionManagerENDCLASS_t qt_meta_stringdata_CLASSConnectionManagerENDCLASS = {
    {
        QT_MOC_LITERAL(0, 17),  // "ConnectionManager"
        QT_MOC_LITERAL(18, 22),  // "connectionStateChanged"
        QT_MOC_LITERAL(41, 0),  // ""
        QT_MOC_LITERAL(42, 15),  // "ConnectionState"
        QT_MOC_LITERAL(58, 5),  // "state"
        QT_MOC_LITERAL(64, 12),  // "dataReceived"
        QT_MOC_LITERAL(77, 4),  // "data"
        QT_MOC_LITERAL(82, 9),  // "connected"
        QT_MOC_LITERAL(92, 12),  // "disconnected"
        QT_MOC_LITERAL(105, 13),  // "statusMessage"
        QT_MOC_LITERAL(119, 7),  // "message"
        QT_MOC_LITERAL(127, 14),  // "progressUpdate"
        QT_MOC_LITERAL(142, 5),  // "value"
        QT_MOC_LITERAL(148, 4),  // "text"
        QT_MOC_LITERAL(153, 13),  // "errorOccurred"
        QT_MOC_LITERAL(167, 5),  // "title"
        QT_MOC_LITERAL(173, 18),  // "dataTransferUpdate"
        QT_MOC_LITERAL(192, 13),  // "bytesReceived"
        QT_MOC_LITERAL(206, 9),  // "bytesSent"
        QT_MOC_LITERAL(216, 17),  // "onSocketConnected"
        QT_MOC_LITERAL(234, 20),  // "onSocketDisconnected"
        QT_MOC_LITERAL(255, 17),  // "onSocketDataReady"
        QT_MOC_LITERAL(273, 13),  // "onSocketError"
        QT_MOC_LITERAL(287, 28),  // "QAbstractSocket::SocketError"
        QT_MOC_LITERAL(316, 5),  // "error"
        QT_MOC_LITERAL(322, 20),  // "onSocketStateChanged"
        QT_MOC_LITERAL(343, 28),  // "QAbstractSocket::SocketState"
        QT_MOC_LITERAL(372, 19),  // "onConnectionTimeout"
        QT_MOC_LITERAL(392, 16),  // "onKeepAliveTimer"
        QT_MOC_LITERAL(409, 23)   // "onAuthenticationTimeout"
    },
    "ConnectionManager",
    "connectionStateChanged",
    "",
    "ConnectionState",
    "state",
    "dataReceived",
    "data",
    "connected",
    "disconnected",
    "statusMessage",
    "message",
    "progressUpdate",
    "value",
    "text",
    "errorOccurred",
    "title",
    "dataTransferUpdate",
    "bytesReceived",
    "bytesSent",
    "onSocketConnected",
    "onSocketDisconnected",
    "onSocketDataReady",
    "onSocketError",
    "QAbstractSocket::SocketError",
    "error",
    "onSocketStateChanged",
    "QAbstractSocket::SocketState",
    "onConnectionTimeout",
    "onKeepAliveTimer",
    "onAuthenticationTimeout"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSConnectionManagerENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
      16,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       8,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    1,  110,    2, 0x06,    1 /* Public */,
       5,    1,  113,    2, 0x06,    3 /* Public */,
       7,    0,  116,    2, 0x06,    5 /* Public */,
       8,    0,  117,    2, 0x06,    6 /* Public */,
       9,    1,  118,    2, 0x06,    7 /* Public */,
      11,    2,  121,    2, 0x06,    9 /* Public */,
      14,    2,  126,    2, 0x06,   12 /* Public */,
      16,    2,  131,    2, 0x06,   15 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      19,    0,  136,    2, 0x08,   18 /* Private */,
      20,    0,  137,    2, 0x08,   19 /* Private */,
      21,    0,  138,    2, 0x08,   20 /* Private */,
      22,    1,  139,    2, 0x08,   21 /* Private */,
      25,    1,  142,    2, 0x08,   23 /* Private */,
      27,    0,  145,    2, 0x08,   25 /* Private */,
      28,    0,  146,    2, 0x08,   26 /* Private */,
      29,    0,  147,    2, 0x08,   27 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3,    4,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::Int, QMetaType::QString,   12,   13,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   15,   10,
    QMetaType::Void, QMetaType::LongLong, QMetaType::LongLong,   17,   18,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 23,   24,
    QMetaType::Void, 0x80000000 | 26,    4,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject ConnectionManager::staticMetaObject = { {
    QMetaObject::SuperData::link<QObject::staticMetaObject>(),
    qt_meta_stringdata_CLASSConnectionManagerENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSConnectionManagerENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSConnectionManagerENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<ConnectionManager, std::true_type>,
        // method 'connectionStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<ConnectionState, std::false_type>,
        // method 'dataReceived'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QByteArray &, std::false_type>,
        // method 'connected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'disconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'statusMessage'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'progressUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<int, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'errorOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QString &, std::false_type>,
        // method 'dataTransferUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<qint64, std::false_type>,
        QtPrivate::TypeAndForceComplete<qint64, std::false_type>,
        // method 'onSocketConnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSocketDisconnected'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSocketDataReady'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onSocketError'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractSocket::SocketError, std::false_type>,
        // method 'onSocketStateChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QAbstractSocket::SocketState, std::false_type>,
        // method 'onConnectionTimeout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onKeepAliveTimer'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onAuthenticationTimeout'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void ConnectionManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConnectionManager *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->connectionStateChanged((*reinterpret_cast< std::add_pointer_t<ConnectionState>>(_a[1]))); break;
        case 1: _t->dataReceived((*reinterpret_cast< std::add_pointer_t<QByteArray>>(_a[1]))); break;
        case 2: _t->connected(); break;
        case 3: _t->disconnected(); break;
        case 4: _t->statusMessage((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1]))); break;
        case 5: _t->progressUpdate((*reinterpret_cast< std::add_pointer_t<int>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 6: _t->errorOccurred((*reinterpret_cast< std::add_pointer_t<QString>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QString>>(_a[2]))); break;
        case 7: _t->dataTransferUpdate((*reinterpret_cast< std::add_pointer_t<qint64>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<qint64>>(_a[2]))); break;
        case 8: _t->onSocketConnected(); break;
        case 9: _t->onSocketDisconnected(); break;
        case 10: _t->onSocketDataReady(); break;
        case 11: _t->onSocketError((*reinterpret_cast< std::add_pointer_t<QAbstractSocket::SocketError>>(_a[1]))); break;
        case 12: _t->onSocketStateChanged((*reinterpret_cast< std::add_pointer_t<QAbstractSocket::SocketState>>(_a[1]))); break;
        case 13: _t->onConnectionTimeout(); break;
        case 14: _t->onKeepAliveTimer(); break;
        case 15: _t->onAuthenticationTimeout(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConnectionManager::*)(ConnectionState );
            if (_t _q_method = &ConnectionManager::connectionStateChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QByteArray & );
            if (_t _q_method = &ConnectionManager::dataReceived; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)();
            if (_t _q_method = &ConnectionManager::connected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)();
            if (_t _q_method = &ConnectionManager::disconnected; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QString & );
            if (_t _q_method = &ConnectionManager::statusMessage; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(int , const QString & );
            if (_t _q_method = &ConnectionManager::progressUpdate; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QString & , const QString & );
            if (_t _q_method = &ConnectionManager::errorOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 6;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(qint64 , qint64 );
            if (_t _q_method = &ConnectionManager::dataTransferUpdate; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 7;
                return;
            }
        }
    }
}

const QMetaObject *ConnectionManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConnectionManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSConnectionManagerENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ConnectionManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 16)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 16;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 16)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 16;
    }
    return _id;
}

// SIGNAL 0
void ConnectionManager::connectionStateChanged(ConnectionState _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ConnectionManager::dataReceived(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ConnectionManager::connected()
{
    QMetaObject::activate(this, &staticMetaObject, 2, nullptr);
}

// SIGNAL 3
void ConnectionManager::disconnected()
{
    QMetaObject::activate(this, &staticMetaObject, 3, nullptr);
}

// SIGNAL 4
void ConnectionManager::statusMessage(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void ConnectionManager::progressUpdate(int _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}

// SIGNAL 6
void ConnectionManager::errorOccurred(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}

// SIGNAL 7
void ConnectionManager::dataTransferUpdate(qint64 _t1, qint64 _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 7, _a);
}
QT_WARNING_POP
