/**
 * @file BuildInfo.h
 * @brief Build information and version details
 * @note This file is auto-generated by CMake
 */

#pragma once

#include <QString>

namespace HVNCDesign {
namespace BuildInfo {

    // Version information
    constexpr const char* VERSION = "@PROJECT_VERSION@";
    constexpr const char* VERSION_MAJOR = "@PROJECT_VERSION_MAJOR@";
    constexpr const char* VERSION_MINOR = "@PROJECT_VERSION_MINOR@";
    constexpr const char* VERSION_PATCH = "@PROJECT_VERSION_PATCH@";
    
    // Project information
    constexpr const char* PROJECT_NAME = "@PROJECT_NAME@";
    constexpr const char* PROJECT_DESCRIPTION = "@PROJECT_DESCRIPTION@";
    
    // Build information
    constexpr const char* BUILD_TYPE = "@CMAKE_BUILD_TYPE@";
    constexpr const char* COMPILER_ID = "@CMAKE_CXX_COMPILER_ID@";
    constexpr const char* COMPILER_VERSION = "@CMAKE_CXX_COMPILER_VERSION@";
    constexpr const char* SYSTEM_NAME = "@CMAKE_SYSTEM_NAME@";
    constexpr const char* SYSTEM_PROCESSOR = "@CMAKE_SYSTEM_PROCESSOR@";
    
    // Qt information
    constexpr const char* QT_VERSION = "@Qt6_VERSION@";
    
    /**
     * @brief Get full version string
     * @return Complete version information
     */
    inline QString getFullVersionString() {
        return QString("%1 v%2 (%3)")
            .arg(PROJECT_NAME)
            .arg(VERSION)
            .arg(BUILD_TYPE);
    }
    
    /**
     * @brief Get build information string
     * @return Complete build information
     */
    inline QString getBuildInfoString() {
        return QString("Built with %1 %2 on %3 (%4)\nQt %5")
            .arg(COMPILER_ID)
            .arg(COMPILER_VERSION)
            .arg(SYSTEM_NAME)
            .arg(SYSTEM_PROCESSOR)
            .arg(QT_VERSION);
    }

} // namespace BuildInfo
} // namespace HVNCDesign
