#pragma once

#include <coroutine>
#include <future>
#include <memory>
#include <optional>
#include <chrono>
#include <thread>
#include <functional>
#include <Windows.h>
#include <WinSock2.h>
#include "ModernWrappers.h"
#include "ModernError.h"

namespace hvnc::coro {

// Forward declarations
template<typename T>
class task;

template<typename T>
class generator;

// Awaitable for Windows events
class event_awaiter {
private:
    HANDLE event_handle_;
    std::chrono::milliseconds timeout_;

public:
    explicit event_awaiter(HANDLE event, std::chrono::milliseconds timeout = (std::chrono::milliseconds::max)())
        : event_handle_(event), timeout_(timeout) {}

    bool await_ready() const noexcept {
        return WaitForSingleObject(event_handle_, 0) == WAIT_OBJECT_0;
    }

    void await_suspend(std::coroutine_handle<> handle) const {
        // Schedule continuation on thread pool
        std::thread([handle, event = event_handle_, timeout = timeout_]() {
            DWORD wait_result = WaitForSingleObject(event, static_cast<DWORD>(timeout.count()));
            handle.resume();
        }).detach();
    }

    bool await_resume() const noexcept {
        return WaitForSingleObject(event_handle_, 0) == WAIT_OBJECT_0;
    }
};

// Awaitable for delays
class delay_awaiter {
private:
    std::chrono::milliseconds duration_;

public:
    explicit delay_awaiter(std::chrono::milliseconds duration) : duration_(duration) {}

    constexpr bool await_ready() const noexcept { return duration_.count() <= 0; }

    void await_suspend(std::coroutine_handle<> handle) const {
        std::thread([handle, duration = duration_]() {
            std::this_thread::sleep_for(duration);
            handle.resume();
        }).detach();
    }

    constexpr void await_resume() const noexcept {}
};

// Awaitable for socket operations
class socket_awaiter {
private:
    SOCKET socket_;
    std::function<bool()> operation_;
    std::chrono::milliseconds timeout_;

public:
    socket_awaiter(SOCKET socket, std::function<bool()> operation, 
                   std::chrono::milliseconds timeout = std::chrono::seconds(30))
        : socket_(socket), operation_(std::move(operation)), timeout_(timeout) {}

    bool await_ready() const {
        return operation_();
    }

    void await_suspend(std::coroutine_handle<> handle) const {
        std::thread([handle, op = operation_, timeout = timeout_]() {
            auto start = std::chrono::steady_clock::now();
            while (!op()) {
                if (std::chrono::steady_clock::now() - start > timeout) {
                    break;
                }
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            handle.resume();
        }).detach();
    }

    bool await_resume() const {
        return operation_();
    }
};

// Task promise type
template<typename T>
struct task_promise {
    std::optional<T> result_;
    std::exception_ptr exception_;

    task<T> get_return_object();

    std::suspend_never initial_suspend() noexcept { return {}; }
    std::suspend_always final_suspend() noexcept { return {}; }

    void unhandled_exception() {
        exception_ = std::current_exception();
    }

    template<typename U>
    void return_value(U&& value) {
        result_ = std::forward<U>(value);
    }
};

// Specialization for void
template<>
struct task_promise<void> {
    std::exception_ptr exception_;

    task<void> get_return_object();

    std::suspend_never initial_suspend() noexcept { return {}; }
    std::suspend_always final_suspend() noexcept { return {}; }

    void unhandled_exception() {
        exception_ = std::current_exception();
    }

    void return_void() noexcept {}
};

// Task type
template<typename T>
class task {
public:
    using promise_type = task_promise<T>;
    using handle_type = std::coroutine_handle<promise_type>;

private:
    handle_type handle_;

public:
    explicit task(handle_type handle) : handle_(handle) {}

    task(task&& other) noexcept : handle_(std::exchange(other.handle_, {})) {}

    task& operator=(task&& other) noexcept {
        if (this != &other) {
            if (handle_) {
                handle_.destroy();
            }
            handle_ = std::exchange(other.handle_, {});
        }
        return *this;
    }

    ~task() {
        if (handle_) {
            handle_.destroy();
        }
    }

    task(const task&) = delete;
    task& operator=(const task&) = delete;

    // Awaitable interface
    bool await_ready() const noexcept {
        return handle_.done();
    }

    void await_suspend(std::coroutine_handle<> awaiting_coroutine) const {
        // Chain coroutines
        std::thread([handle = handle_, awaiting = awaiting_coroutine]() {
            while (!handle.done()) {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
            awaiting.resume();
        }).detach();
    }

    T await_resume() {
        if (handle_.promise().exception_) {
            std::rethrow_exception(handle_.promise().exception_);
        }
        
        if constexpr (!std::is_void_v<T>) {
            return std::move(*handle_.promise().result_);
        }
    }

    // Get result synchronously
    T get() {
        while (!handle_.done()) {
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        return await_resume();
    }

    // Check if completed
    bool is_ready() const noexcept {
        return handle_.done();
    }
};

// Implementation of get_return_object
template<typename T>
task<T> task_promise<T>::get_return_object() {
    return task<T>{std::coroutine_handle<task_promise<T>>::from_promise(*this)};
}

inline task<void> task_promise<void>::get_return_object() {
    return task<void>{std::coroutine_handle<task_promise<void>>::from_promise(*this)};
}

// Generator promise type
template<typename T>
struct generator_promise {
    std::optional<T> current_value_;

    generator<T> get_return_object();

    std::suspend_always initial_suspend() noexcept { return {}; }
    std::suspend_always final_suspend() noexcept { return {}; }

    void unhandled_exception() {
        throw;
    }

    std::suspend_always yield_value(T value) {
        current_value_ = std::move(value);
        return {};
    }

    void return_void() noexcept {}
};

// Generator type
template<typename T>
class generator {
public:
    using promise_type = generator_promise<T>;
    using handle_type = std::coroutine_handle<promise_type>;

private:
    handle_type handle_;

public:
    explicit generator(handle_type handle) : handle_(handle) {}

    generator(generator&& other) noexcept : handle_(std::exchange(other.handle_, {})) {}

    generator& operator=(generator&& other) noexcept {
        if (this != &other) {
            if (handle_) {
                handle_.destroy();
            }
            handle_ = std::exchange(other.handle_, {});
        }
        return *this;
    }

    ~generator() {
        if (handle_) {
            handle_.destroy();
        }
    }

    generator(const generator&) = delete;
    generator& operator=(const generator&) = delete;

    // Iterator interface
    class iterator {
    private:
        handle_type handle_;

    public:
        explicit iterator(handle_type handle) : handle_(handle) {}

        iterator& operator++() {
            handle_.resume();
            return *this;
        }

        const T& operator*() const {
            return *handle_.promise().current_value_;
        }

        bool operator==(const iterator& other) const {
            return handle_.done() == other.handle_.done();
        }

        bool operator!=(const iterator& other) const {
            return !(*this == other);
        }
    };

    iterator begin() {
        if (handle_) {
            handle_.resume();
        }
        return iterator{handle_};
    }

    iterator end() {
        return iterator{{}};
    }
};

template<typename T>
generator<T> generator_promise<T>::get_return_object() {
    return generator<T>{std::coroutine_handle<generator_promise<T>>::from_promise(*this)};
}

// Utility functions
inline delay_awaiter delay(std::chrono::milliseconds duration) {
    return delay_awaiter{duration};
}

inline event_awaiter wait_for_event(HANDLE event, std::chrono::milliseconds timeout = (std::chrono::milliseconds::max)()) {
    return event_awaiter{event, timeout};
}

inline socket_awaiter wait_for_socket(SOCKET socket, std::function<bool()> operation, 
                                     std::chrono::milliseconds timeout = std::chrono::seconds(30)) {
    return socket_awaiter{socket, std::move(operation), timeout};
}

} // namespace hvnc::coro
