/**
 * @file StyleManager.cpp
 * @brief Implementation of StyleManager class
 */

#include "StyleManager.h"
#include <QApplication>
#include <QPalette>
#include <QStyleFactory>

StyleManager::StyleManager(QObject* parent)
    : QObject(parent)
    , m_currentTheme(Theme::Dark)
    , m_initialized(false) {
    initialize();
}

void StyleManager::initialize() {
    if (m_initialized) return;
    
    // Initialize color map
    m_colorMap["background-primary"] = HVNCDesign::Colors::BACKGROUND_PRIMARY;
    m_colorMap["background-secondary"] = HVNCDesign::Colors::BACKGROUND_SECONDARY;
    m_colorMap["background-tertiary"] = HVNCDesign::Colors::BACKGROUND_TERTIARY;
    m_colorMap["text-primary"] = HVNCDesign::Colors::TEXT_PRIMARY;
    m_colorMap["text-secondary"] = HVNCDesign::Colors::TEXT_SECONDARY;
    m_colorMap["text-disabled"] = HVNCDesign::Colors::TEXT_DISABLED;
    m_colorMap["accent-primary"] = HVNCDesign::Colors::ACCENT_PRIMARY;
    m_colorMap["accent-hover"] = HVNCDesign::Colors::ACCENT_HOVER;
    m_colorMap["accent-pressed"] = HVNCDesign::Colors::ACCENT_PRESSED;
    m_colorMap["success"] = HVNCDesign::Colors::SUCCESS;
    m_colorMap["warning"] = HVNCDesign::Colors::WARNING;
    m_colorMap["error"] = HVNCDesign::Colors::ERROR_COLOR;
    m_colorMap["border"] = HVNCDesign::Colors::BORDER;
    m_colorMap["separator"] = HVNCDesign::Colors::SEPARATOR;
    
    m_initialized = true;
    HVNC_DEBUG() << "StyleManager initialized";
}

void StyleManager::applyDarkTheme() {
    setTheme(Theme::Dark);
    
    // Setup application palette
    setupPalette();
    setupFonts();
    
    // Apply base stylesheet
    qApp->setStyleSheet(getBaseStyleSheet());
    
    HVNC_INFO() << "Dark theme applied";
    emit themeChanged(Theme::Dark);
}

void StyleManager::applyLightTheme() {
    setTheme(Theme::Light);
    // Light theme implementation would go here
    HVNC_INFO() << "Light theme applied";
    emit themeChanged(Theme::Light);
}

void StyleManager::setTheme(Theme theme) {
    if (m_currentTheme != theme) {
        m_currentTheme = theme;
        m_styleCache.clear(); // Clear cache when theme changes
    }
}

void StyleManager::setupPalette() {
    QPalette palette;
    
    // Window colors
    palette.setColor(QPalette::Window, QColor(getColor("background-primary")));
    palette.setColor(QPalette::WindowText, QColor(getColor("text-primary")));
    
    // Base colors (for input fields)
    palette.setColor(QPalette::Base, QColor(getColor("background-secondary")));
    palette.setColor(QPalette::AlternateBase, QColor(getColor("background-tertiary")));
    
    // Text colors
    palette.setColor(QPalette::Text, QColor(getColor("text-primary")));
    palette.setColor(QPalette::BrightText, QColor(getColor("text-primary")));
    palette.setColor(QPalette::PlaceholderText, QColor(getColor("text-disabled")));
    
    // Button colors
    palette.setColor(QPalette::Button, QColor(getColor("background-secondary")));
    palette.setColor(QPalette::ButtonText, QColor(getColor("text-primary")));
    
    // Highlight colors
    palette.setColor(QPalette::Highlight, QColor(getColor("accent-primary")));
    palette.setColor(QPalette::HighlightedText, QColor(getColor("text-primary")));
    
    // Disabled colors
    palette.setColor(QPalette::Disabled, QPalette::WindowText, QColor(getColor("text-disabled")));
    palette.setColor(QPalette::Disabled, QPalette::Text, QColor(getColor("text-disabled")));
    palette.setColor(QPalette::Disabled, QPalette::ButtonText, QColor(getColor("text-disabled")));
    
    qApp->setPalette(palette);
}

void StyleManager::setupFonts() {
    QFont appFont(HVNCDesign::Fonts::PRIMARY_FAMILY, HVNCDesign::Fonts::SIZE_NORMAL);
    qApp->setFont(appFont);
}

QString StyleManager::getBaseStyleSheet() const {
    return QString(R"(
        /* Base application styling */
        QWidget {
            background-color: %1;
            color: %2;
            font-family: "%3";
            font-size: %4px;
        }
        
        /* Main window */
        QMainWindow {
            background-color: %1;
            border: none;
        }
        
        /* Tooltips */
        QToolTip {
            background-color: %5;
            color: %2;
            border: 1px solid %6;
            padding: 4px;
            border-radius: 4px;
        }
    )")
    .arg(getColor("background-primary"))
    .arg(getColor("text-primary"))
    .arg(HVNCDesign::Fonts::PRIMARY_FAMILY)
    .arg(HVNCDesign::Fonts::SIZE_NORMAL)
    .arg(getColor("background-tertiary"))
    .arg(getColor("border"));
}

QString StyleManager::getMainWindowStyleSheet() const {
    return QString(R"(
        QMainWindow {
            background-color: %1;
        }
        
        QMainWindow::separator {
            background-color: %2;
            width: 1px;
            height: 1px;
        }
        
        QMainWindow::separator:hover {
            background-color: %3;
        }
    )")
    .arg(getColor("background-primary"))
    .arg(getColor("separator"))
    .arg(getColor("accent-primary"));
}

QString StyleManager::getDesktopViewerStyleSheet() const {
    return QString(R"(
        QGraphicsView {
            background-color: %1;
            border: 1px solid %2;
            border-radius: 4px;
        }
        
        QGraphicsView:focus {
            border-color: %3;
        }
        
        QScrollBar:vertical {
            background-color: %4;
            width: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:vertical {
            background-color: %2;
            border-radius: 6px;
            min-height: 20px;
        }
        
        QScrollBar::handle:vertical:hover {
            background-color: %3;
        }
        
        QScrollBar:horizontal {
            background-color: %4;
            height: 12px;
            border-radius: 6px;
        }
        
        QScrollBar::handle:horizontal {
            background-color: %2;
            border-radius: 6px;
            min-width: 20px;
        }
        
        QScrollBar::handle:horizontal:hover {
            background-color: %3;
        }
        
        QScrollBar::add-line, QScrollBar::sub-line {
            border: none;
            background: none;
        }
    )")
    .arg(getColor("background-secondary"))
    .arg(getColor("border"))
    .arg(getColor("accent-primary"))
    .arg(getColor("background-primary"));
}

QString StyleManager::getControlPanelStyleSheet() const {
    return QString(R"(
        QWidget#ControlPanel {
            background-color: %1;
            border-right: 1px solid %2;
        }
        
        QLabel#SectionTitle {
            font-size: %3px;
            font-weight: bold;
            color: %4;
            padding: 8px 0px 4px 0px;
        }
        
        QLabel#SectionSubtitle {
            font-size: %5px;
            color: %6;
            padding: 4px 0px;
        }
    )")
    .arg(getColor("background-secondary"))
    .arg(getColor("separator"))
    .arg(HVNCDesign::Fonts::SIZE_MEDIUM)
    .arg(getColor("text-primary"))
    .arg(HVNCDesign::Fonts::SIZE_NORMAL)
    .arg(getColor("text-secondary"));
}

QString StyleManager::getButtonStyleSheet(bool primary) const {
    if (primary) {
        return QString(R"(
            QPushButton {
                background-color: %1;
                color: %2;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: 500;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: %3;
            }
            
            QPushButton:pressed {
                background-color: %4;
            }
            
            QPushButton:disabled {
                background-color: %5;
                color: %6;
            }
        )")
        .arg(getColor("accent-primary"))
        .arg(getColor("text-primary"))
        .arg(getColor("accent-hover"))
        .arg(getColor("accent-pressed"))
        .arg(getColor("background-tertiary"))
        .arg(getColor("text-disabled"));
    } else {
        return QString(R"(
            QPushButton {
                background-color: %1;
                color: %2;
                border: 1px solid %3;
                border-radius: 4px;
                padding: 8px 16px;
                min-width: 80px;
            }
            
            QPushButton:hover {
                background-color: %4;
                border-color: %5;
            }
            
            QPushButton:pressed {
                background-color: %6;
            }
            
            QPushButton:disabled {
                background-color: %1;
                color: %7;
                border-color: %8;
            }
        )")
        .arg(getColor("background-secondary"))
        .arg(getColor("text-primary"))
        .arg(getColor("border"))
        .arg(getColor("background-tertiary"))
        .arg(getColor("accent-primary"))
        .arg(getColor("background-primary"))
        .arg(getColor("text-disabled"))
        .arg(getColor("separator"));
    }
}

QString StyleManager::getInputFieldStyleSheet() const {
    return QString(R"(
        QLineEdit, QTextEdit, QPlainTextEdit, QSpinBox, QDoubleSpinBox, QComboBox {
            background-color: %1;
            color: %2;
            border: 1px solid %3;
            border-radius: 4px;
            padding: 6px 8px;
            selection-background-color: %4;
        }
        
        QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, 
        QSpinBox:focus, QDoubleSpinBox:focus, QComboBox:focus {
            border-color: %4;
        }
        
        QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled,
        QSpinBox:disabled, QDoubleSpinBox:disabled, QComboBox:disabled {
            background-color: %5;
            color: %6;
            border-color: %7;
        }
        
        QComboBox::drop-down {
            border: none;
            width: 20px;
        }
        
        QComboBox::down-arrow {
            image: url(:/icons/arrow_down.png);
            width: 12px;
            height: 12px;
        }
        
        QComboBox QAbstractItemView {
            background-color: %1;
            border: 1px solid %3;
            selection-background-color: %4;
        }
    )")
    .arg(getColor("background-secondary"))
    .arg(getColor("text-primary"))
    .arg(getColor("border"))
    .arg(getColor("accent-primary"))
    .arg(getColor("background-primary"))
    .arg(getColor("text-disabled"))
    .arg(getColor("separator"));
}

QString StyleManager::getColor(const QString& colorName) const {
    return m_colorMap.value(colorName, "#000000");
}

QString StyleManager::getFont(int size, int weight) const {
    Q_UNUSED(weight)
    return QString("%1px").arg(size);
}

QFrame* StyleManager::createSeparator(Qt::Orientation orientation) {
    QFrame* separator = new QFrame();
    separator->setFrameShape(orientation == Qt::Horizontal ? QFrame::HLine : QFrame::VLine);
    separator->setFrameShadow(QFrame::Sunken);
    separator->setStyleSheet(QString("QFrame { color: %1; }").arg(getColor("separator")));
    return separator;
}

QGroupBox* StyleManager::createGroupBox(const QString& title) {
    QGroupBox* groupBox = new QGroupBox(title);
    groupBox->setStyleSheet(getGroupBoxStyleSheet());
    return groupBox;
}

QString StyleManager::getGroupBoxStyleSheet() const {
    return QString(R"(
        QGroupBox {
            font-weight: bold;
            border: 1px solid %1;
            border-radius: 4px;
            margin-top: 8px;
            padding-top: 8px;
        }
        
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 8px;
            padding: 0 4px 0 4px;
        }
    )")
    .arg(getColor("border"));
}

QPushButton* StyleManager::createButton(const QString& text, bool primary) {
    QPushButton* button = new QPushButton(text);
    button->setStyleSheet(getButtonStyleSheet(primary));
    return button;
}

QToolButton* StyleManager::createToolButton(const QIcon& icon, const QString& tooltip) {
    QToolButton* button = new QToolButton();
    button->setIcon(icon);
    button->setToolTip(tooltip);
    button->setStyleSheet(getButtonStyleSheet(false));
    return button;
}

QLineEdit* StyleManager::createLineEdit(const QString& placeholder) {
    QLineEdit* lineEdit = new QLineEdit();
    if (!placeholder.isEmpty()) {
        lineEdit->setPlaceholderText(placeholder);
    }
    lineEdit->setStyleSheet(getInputFieldStyleSheet());
    return lineEdit;
}

QLabel* StyleManager::createLabel(const QString& text, const QString& styleClass) {
    QLabel* label = new QLabel(text);
    
    if (styleClass == "title") {
        label->setStyleSheet(QString("font-size: %1px; font-weight: bold; color: %2;")
                           .arg(HVNCDesign::Fonts::SIZE_LARGE)
                           .arg(getColor("text-primary")));
    } else if (styleClass == "subtitle") {
        label->setStyleSheet(QString("font-size: %1px; font-weight: 500; color: %2;")
                           .arg(HVNCDesign::Fonts::SIZE_MEDIUM)
                           .arg(getColor("text-primary")));
    } else if (styleClass == "caption") {
        label->setStyleSheet(QString("font-size: %1px; color: %2;")
                           .arg(HVNCDesign::Fonts::SIZE_SMALL)
                           .arg(getColor("text-secondary")));
    } else {
        label->setStyleSheet(QString("color: %1;").arg(getColor("text-primary")));
    }
    
    return label;
}

void StyleManager::applyCustomStyle(QWidget* widget, const QString& styleClass) {
    if (!widget) return;
    
    // Apply custom styles based on style class
    if (styleClass == "desktop-viewer") {
        widget->setStyleSheet(getDesktopViewerStyleSheet());
    } else if (styleClass == "control-panel") {
        widget->setStyleSheet(getControlPanelStyleSheet());
    }
    // Add more custom styles as needed
}

void StyleManager::onSystemThemeChanged() {
    // Handle system theme changes if auto theme is enabled
    if (m_currentTheme == Theme::Auto) {
        // Detect system theme and apply appropriate theme
        // Implementation would depend on platform
    }
}

// Global instance
static StyleManager* g_styleManager = nullptr;

StyleManager& getStyleManager() {
    if (!g_styleManager) {
        g_styleManager = new StyleManager(qApp);
    }
    return *g_styleManager;
}

QString StyleManager::getToolbarStyleSheet() const {
    return QString(R"(
        QToolBar {
            background-color: %1;
            color: %2;
            border: 1px solid %3;
            spacing: 4px;
        }
        QToolBar::separator {
            background-color: %3;
            width: 1px;
            margin: 4px;
        }
    )")
    .arg(getColor("background-secondary"))
    .arg(getColor("text-primary"))
    .arg(getColor("border"));
}

QString StyleManager::getStatusBarStyleSheet() const {
    return QString(R"(
        QStatusBar {
            background-color: %1;
            color: %2;
            border-top: 1px solid %3;
            padding: 2px;
        }
    )")
    .arg(getColor("background-secondary"))
    .arg(getColor("text-primary"))
    .arg(getColor("border"));
}

QString StyleManager::getMenuStyleSheet() const {
    return QString(R"(
        QMenuBar {
            background-color: %1;
            color: %2;
            border-bottom: 1px solid %3;
        }
        QMenuBar::item {
            padding: 4px 8px;
            background-color: transparent;
        }
        QMenuBar::item:selected {
            background-color: %4;
        }
        QMenu {
            background-color: %1;
            color: %2;
            border: 1px solid %3;
        }
        QMenu::item {
            padding: 4px 16px;
        }
        QMenu::item:selected {
            background-color: %4;
        }
    )")
    .arg(getColor("background-secondary"))
    .arg(getColor("text-primary"))
    .arg(getColor("border"))
    .arg(getColor("accent-primary"));
}
