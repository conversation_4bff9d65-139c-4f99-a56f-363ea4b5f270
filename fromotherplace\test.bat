@echo off
echo Testing Screenshot Transfer System...

REM Check if executables exist
if not exist "build\server.exe" (
    echo Error: server.exe not found. Please run build.bat first.
    pause
    exit /b 1
)

if not exist "build\client.exe" (
    echo Error: client.exe not found. Please run build.bat first.
    pause
    exit /b 1
)

echo.
echo Starting server in background...
start /B "Screenshot Server" build\server.exe

REM Wait a moment for server to start
timeout /t 2 /nobreak >nul

echo.
echo Sending test screenshot...
build\client.exe --quality 80

echo.
echo Test completed! Check the screenshots folder for the captured image.
echo.
echo Press any key to stop the server...
pause >nul

REM Kill the server process
taskkill /F /IM server.exe >nul 2>&1

echo Server stopped.
