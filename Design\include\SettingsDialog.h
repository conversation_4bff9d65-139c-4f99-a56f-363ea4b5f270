/**
 * @file SettingsDialog.h
 * @brief Professional HVNC Settings Dialog with comprehensive configuration options
 * <AUTHOR> Design Team
 * @version 2.0.0
 * 
 * This settings dialog provides a professional interface for configuring all
 * HVNC server settings including network, security, performance, and UI options.
 */

#pragma once

#include "Common.h"
#include <QDialog>

// Forward declarations
class QTabWidget;
class QGroupBox;
class QSpinBox;
class QLineEdit;
class QComboBox;
class QCheckBox;
class QPushButton;
class QLabel;
class QSlider;
class QButtonGroup;
class QRadioButton;

/**
 * @class SettingsDialog
 * @brief Professional settings dialog for HVNC configuration
 * 
 * The SettingsDialog provides a comprehensive interface for configuring all
 * aspects of the HVNC system including server settings, security options,
 * performance tuning, and user interface preferences.
 */
class SettingsDialog : public QDialog {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit SettingsDialog(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~SettingsDialog() override;

    /**
     * @brief Get server port setting
     * @return Server port number
     */
    int getServerPort() const;

    /**
     * @brief Get server password setting
     * @return Server password
     */
    QString getServerPassword() const;

    /**
     * @brief Get maximum connections setting
     * @return Maximum number of connections
     */
    int getMaxConnections() const;

    /**
     * @brief Get compression level setting
     * @return Compression level (0-9)
     */
    int getCompressionLevel() const;

    /**
     * @brief Get update interval setting
     * @return Update interval in milliseconds
     */
    int getUpdateInterval() const;

    /**
     * @brief Get image quality setting
     * @return Image quality index
     */
    int getImageQuality() const;

    /**
     * @brief Check if encryption is enabled
     * @return True if encryption is enabled
     */
    bool isEncryptionEnabled() const;

    /**
     * @brief Check if auto-start is enabled
     * @return True if auto-start is enabled
     */
    bool isAutoStartEnabled() const;

    /**
     * @brief Check if logging is enabled
     * @return True if logging is enabled
     */
    bool isLoggingEnabled() const;

public slots:
    /**
     * @brief Load settings from configuration
     */
    void loadSettings();

    /**
     * @brief Save settings to configuration
     */
    void saveSettings();

    /**
     * @brief Reset settings to defaults
     */
    void resetToDefaults();

signals:
    /**
     * @brief Settings have been applied
     */
    void settingsApplied();

    /**
     * @brief Server configuration changed
     * @param port Server port
     * @param password Server password
     * @param maxConnections Maximum connections
     */
    void serverConfigChanged(int port, const QString& password, int maxConnections);

private slots:
    /**
     * @brief Handle OK button click
     */
    void onOkClicked();

    /**
     * @brief Handle Apply button click
     */
    void onApplyClicked();

    /**
     * @brief Handle Cancel button click
     */
    void onCancelClicked();

    /**
     * @brief Handle Reset button click
     */
    void onResetClicked();

    /**
     * @brief Handle compression slider change
     * @param value New compression value
     */
    void onCompressionChanged(int value);

    /**
     * @brief Handle test connection button click
     */
    void onTestConnection();

private:
    /**
     * @brief Initialize UI components
     */
    void initializeUI();

    /**
     * @brief Create server settings tab
     * @return Server settings widget
     */
    QWidget* createServerSettingsTab();

    /**
     * @brief Create security settings tab
     * @return Security settings widget
     */
    QWidget* createSecuritySettingsTab();

    /**
     * @brief Create performance settings tab
     * @return Performance settings widget
     */
    QWidget* createPerformanceSettingsTab();

    /**
     * @brief Create interface settings tab
     * @return Interface settings widget
     */
    QWidget* createInterfaceSettingsTab();

    /**
     * @brief Apply professional styling
     */
    void applyProfessionalStyling();

    /**
     * @brief Validate settings
     * @return True if settings are valid
     */
    bool validateSettings();

private:
    // Main layout
    QTabWidget* m_tabWidget;

    // Server Settings
    QSpinBox* m_portSpinBox;
    QLineEdit* m_passwordLineEdit;
    QSpinBox* m_maxConnectionsSpinBox;
    QCheckBox* m_autoStartCheckBox;
    QPushButton* m_testConnectionButton;

    // Security Settings
    QCheckBox* m_enableEncryptionCheckBox;
    QComboBox* m_encryptionMethodComboBox;
    QLineEdit* m_encryptionKeyLineEdit;
    QCheckBox* m_requireAuthCheckBox;
    QSpinBox* m_sessionTimeoutSpinBox;

    // Performance Settings
    QSlider* m_compressionSlider;
    QLabel* m_compressionLabel;
    QSpinBox* m_updateIntervalSpinBox;
    QComboBox* m_imageQualityComboBox;
    QSpinBox* m_bufferSizeSpinBox;
    QCheckBox* m_enableMultithreadingCheckBox;

    // Interface Settings
    QCheckBox* m_enableLoggingCheckBox;
    QComboBox* m_logLevelComboBox;
    QCheckBox* m_showControlPanelCheckBox;
    QCheckBox* m_showToolbarCheckBox;
    QCheckBox* m_showStatusBarCheckBox;
    QComboBox* m_themeComboBox;

    // Dialog buttons
    QPushButton* m_okButton;
    QPushButton* m_applyButton;
    QPushButton* m_cancelButton;
    QPushButton* m_resetButton;
};
