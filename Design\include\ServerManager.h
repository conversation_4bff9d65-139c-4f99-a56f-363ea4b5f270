/**
 * @file ServerManager.h
 * @brief Server management and integration with HVNC server logic
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This class manages the HVNC server instance, handles client connections,
 * and provides the bridge between the existing server logic and the GUI.
 * It manages server lifecycle, client sessions, and data flow.
 */

#pragma once

#include "Common.h"
#include <QThread>
#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>

// Forward declarations
class NetworkThread;
class ImageProcessor;

/**
 * @class ServerManager
 * @brief Manages HVNC server operations and client connections
 * 
 * The ServerManager class provides a Qt-based wrapper around the existing
 * HVNC server logic. It handles server startup/shutdown, client connection
 * management, and coordinates data flow between the server and GUI components.
 */
class ServerManager : public QObject {
    Q_OBJECT

public:
    /**
     * @brief Client information structure
     */
    struct ClientInfo {
        ClientId id;                    ///< Client identifier
        QString ipAddress;              ///< Client IP address
        PortNumber port;                ///< Client port
        QDateTime connectTime;          ///< Connection timestamp
        QString userAgent;              ///< Client user agent
        QSize desktopSize;              ///< Client desktop size
        bool isActive;                  ///< Active state
        qint64 bytesReceived;           ///< Bytes received from client
        qint64 bytesSent;               ///< Bytes sent to client
    };

    /**
     * @brief Constructor
     * @param parent Parent object
     */
    explicit ServerManager(QObject* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~ServerManager() override;

    /**
     * @brief Start the HVNC server
     * @param port Server port number
     * @return True if server started successfully
     */
    bool startServer(PortNumber port = HVNCDesign::DEFAULT_SERVER_PORT);

    /**
     * @brief Stop the HVNC server
     */
    void stopServer();

    /**
     * @brief Get current server state
     * @return Current server state
     */
    ServerState getServerState() const { return m_serverState; }

    /**
     * @brief Get server port
     * @return Server port number
     */
    PortNumber getServerPort() const { return m_serverPort; }

    /**
     * @brief Set server port
     * @param port Server port number
     */
    void setServerPort(PortNumber port) { m_serverPort = port; }

    /**
     * @brief Get connected clients count
     * @return Number of connected clients
     */
    int getConnectedClientsCount() const { return m_clients.size(); }

    /**
     * @brief Set maximum number of clients
     * @param maxClients Maximum number of clients
     */
    void setMaxClients(int maxClients) { m_maxClients = maxClients; }

    /**
     * @brief Get maximum number of clients
     * @return Maximum number of clients
     */
    int getMaxClients() const { return m_maxClients; }

    /**
     * @brief Check if server is running
     * @return True if server is running
     */
    bool isRunning() const { return m_isRunning; }

    /**
     * @brief Get client information
     * @param clientId Client identifier
     * @return Client information or nullptr if not found
     */
    const ClientInfo* getClientInfo(ClientId clientId) const;

    /**
     * @brief Get all connected clients
     * @return List of client information
     */
    QList<ClientInfo> getAllClients() const;

    /**
     * @brief Send input event to client
     * @param clientId Client identifier
     * @param event Input event data
     * @return True if event sent successfully
     */
    bool sendInputEvent(ClientId clientId, const QByteArray& event);

    /**
     * @brief Send mouse event to client
     * @param clientId Client identifier
     * @param x X coordinate
     * @param y Y coordinate
     * @param button Mouse button
     * @param pressed Button pressed state
     * @return True if event sent successfully
     */
    bool sendMouseEvent(ClientId clientId, int x, int y, MouseButton button, bool pressed);

    /**
     * @brief Send keyboard event to client
     * @param clientId Client identifier
     * @param key Key code
     * @param pressed Key pressed state
     * @param modifiers Key modifiers
     * @return True if event sent successfully
     */
    bool sendKeyboardEvent(ClientId clientId, int key, bool pressed, KeyModifier modifiers);

    /**
     * @brief Request desktop update from client
     * @param clientId Client identifier
     * @param fullUpdate True for full desktop update
     * @return True if request sent successfully
     */
    bool requestDesktopUpdate(ClientId clientId, bool fullUpdate = false);

    /**
     * @brief Disconnect specific client
     * @param clientId Client identifier
     */
    void disconnectClient(ClientId clientId);

    /**
     * @brief Get server statistics
     * @return Server statistics as key-value pairs
     */
    QMap<QString, QVariant> getServerStatistics() const;

signals:
    /**
     * @brief Emitted when server state changes
     * @param state New server state
     */
    void serverStateChanged(ServerState state);

    /**
     * @brief Emitted when client connects
     * @param clientId Client identifier
     * @param clientInfo Client information string
     */
    void clientConnected(ClientId clientId, const QString& clientInfo);

    /**
     * @brief Emitted when client disconnects
     * @param clientId Client identifier
     */
    void clientDisconnected(ClientId clientId);

    /**
     * @brief Emitted when desktop image is received
     * @param clientId Client identifier
     * @param image Desktop image
     */
    void desktopImageReceived(ClientId clientId, const QImage& image);

    /**
     * @brief Emitted when desktop region is updated
     * @param clientId Client identifier
     * @param image Region image
     * @param x X coordinate
     * @param y Y coordinate
     */
    void desktopRegionUpdated(ClientId clientId, const QImage& image, int x, int y);

    /**
     * @brief Emitted when cursor position changes
     * @param clientId Client identifier
     * @param position Cursor position
     */
    void cursorPositionChanged(ClientId clientId, const QPoint& position);

    /**
     * @brief Emitted for status messages
     * @param message Status message
     */
    void statusMessage(const QString& message);

    /**
     * @brief Emitted when error occurs
     * @param title Error title
     * @param message Error message
     */
    void errorOccurred(const QString& title, const QString& message);

    /**
     * @brief Emitted for data transfer updates
     * @param clientId Client identifier
     * @param bytesReceived Bytes received
     * @param bytesSent Bytes sent
     */
    void dataTransferUpdate(ClientId clientId, qint64 bytesReceived, qint64 bytesSent);

private slots:
    /**
     * @brief Handle new client connection
     */
    void onNewConnection();

    /**
     * @brief Handle client disconnection
     */
    void onClientDisconnected();

    /**
     * @brief Handle client data ready
     */
    void onClientDataReady();

    /**
     * @brief Handle server error
     * @param error Server error
     */
    void onServerError(QAbstractSocket::SocketError error);

    /**
     * @brief Handle heartbeat timer
     */
    void onHeartbeatTimer();

    /**
     * @brief Handle statistics update timer
     */
    void onStatisticsTimer();

private:
    /**
     * @brief Initialize server manager
     */
    void initialize();

    /**
     * @brief Setup server socket
     * @param port Server port
     * @return True if setup successful
     */
    bool setupServer(PortNumber port);

    /**
     * @brief Cleanup server resources
     */
    void cleanupServer();

    /**
     * @brief Process client handshake
     * @param socket Client socket
     * @return Client ID if successful, 0 otherwise
     */
    ClientId processClientHandshake(QTcpSocket* socket);

    /**
     * @brief Process client data
     * @param clientId Client identifier
     * @param data Received data
     */
    void processClientData(ClientId clientId, const QByteArray& data);

    /**
     * @brief Process desktop image data
     * @param clientId Client identifier
     * @param data Image data
     */
    void processDesktopImage(ClientId clientId, const QByteArray& data);

    /**
     * @brief Process desktop region update
     * @param clientId Client identifier
     * @param data Region data
     */
    void processDesktopRegion(ClientId clientId, const QByteArray& data);

    /**
     * @brief Add new client
     * @param socket Client socket
     * @return Client ID
     */
    ClientId addClient(QTcpSocket* socket);

    /**
     * @brief Remove client
     * @param clientId Client identifier
     */
    void removeClient(ClientId clientId);

    /**
     * @brief Get client socket
     * @param clientId Client identifier
     * @return Client socket or nullptr
     */
    QTcpSocket* getClientSocket(ClientId clientId) const;

    /**
     * @brief Update client statistics
     * @param clientId Client identifier
     * @param bytesReceived Bytes received
     * @param bytesSent Bytes sent
     */
    void updateClientStatistics(ClientId clientId, qint64 bytesReceived, qint64 bytesSent);

    /**
     * @brief Set server state
     * @param state New server state
     */
    void setServerState(ServerState state);

    /**
     * @brief Generate unique client ID
     * @return Unique client ID
     */
    ClientId generateClientId();

private:
    // Core components
    QTcpServer* m_tcpServer;                 ///< TCP server instance
    ImageProcessor* m_imageProcessor;        ///< Image processor
    QTimer* m_heartbeatTimer;                ///< Heartbeat timer
    QTimer* m_statisticsTimer;               ///< Statistics timer

    // Server state
    ServerState m_serverState;               ///< Current server state
    PortNumber m_serverPort;                 ///< Server port
    QDateTime m_serverStartTime;             ///< Server start time

    // Client management
    QMap<ClientId, ClientInfo> m_clients;    ///< Connected clients
    QMap<ClientId, QTcpSocket*> m_clientSockets; ///< Client sockets
    QMap<QTcpSocket*, ClientId> m_socketToClient; ///< Socket to client mapping
    ClientId m_nextClientId;                 ///< Next client ID counter

    // Statistics
    qint64 m_totalBytesReceived;             ///< Total bytes received
    qint64 m_totalBytesSent;                 ///< Total bytes sent
    int m_totalConnections;                  ///< Total connections count

    // Configuration
    int m_maxClients;                        ///< Maximum clients allowed
    int m_heartbeatInterval;                 ///< Heartbeat interval (ms)
    int m_statisticsInterval;                ///< Statistics update interval (ms)

    // State flags
    bool m_isInitialized;                    ///< Initialization flag
    bool m_isRunning;                        ///< Server running flag
};
