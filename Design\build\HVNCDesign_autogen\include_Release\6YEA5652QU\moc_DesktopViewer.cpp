/****************************************************************************
** Meta object code from reading C++ file 'DesktopViewer.h'
**
** Created by: The Qt Meta Object Compiler version 68 (Qt 6.5.3)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../../../include/DesktopViewer.h"
#include <QtGui/qtextcursor.h>
#include <QtCore/qmetatype.h>

#if __has_include(<QtCore/qtmochelpers.h>)
#include <QtCore/qtmochelpers.h>
#else
QT_BEGIN_MOC_NAMESPACE
#endif


#include <memory>

#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'DesktopViewer.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 68
#error "This file was generated using the moc from 6.5.3. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

#ifndef Q_CONSTINIT
#define Q_CONSTINIT
#endif

QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
QT_WARNING_DISABLE_GCC("-Wuseless-cast")
namespace {

#ifdef QT_MOC_HAS_STRINGDATA
struct qt_meta_stringdata_CLASSDesktopViewerENDCLASS_t {};
static constexpr auto qt_meta_stringdata_CLASSDesktopViewerENDCLASS = QtMocHelpers::stringData(
    "DesktopViewer",
    "mouseEventOccurred",
    "",
    "QMouseEvent*",
    "event",
    "position",
    "keyEventOccurred",
    "QKeyEvent*",
    "wheelEventOccurred",
    "QWheelEvent*",
    "scalingChanged",
    "factor",
    "desktopSizeChanged",
    "size",
    "selectionChanged",
    "rect",
    "onCursorBlink",
    "onFpsUpdate"
);
#else  // !QT_MOC_HAS_STRING_DATA
struct qt_meta_stringdata_CLASSDesktopViewerENDCLASS_t {
    uint offsetsAndSizes[36];
    char stringdata0[14];
    char stringdata1[19];
    char stringdata2[1];
    char stringdata3[13];
    char stringdata4[6];
    char stringdata5[9];
    char stringdata6[17];
    char stringdata7[11];
    char stringdata8[19];
    char stringdata9[13];
    char stringdata10[15];
    char stringdata11[7];
    char stringdata12[19];
    char stringdata13[5];
    char stringdata14[17];
    char stringdata15[5];
    char stringdata16[14];
    char stringdata17[12];
};
#define QT_MOC_LITERAL(ofs, len) \
    uint(sizeof(qt_meta_stringdata_CLASSDesktopViewerENDCLASS_t::offsetsAndSizes) + ofs), len 
Q_CONSTINIT static const qt_meta_stringdata_CLASSDesktopViewerENDCLASS_t qt_meta_stringdata_CLASSDesktopViewerENDCLASS = {
    {
        QT_MOC_LITERAL(0, 13),  // "DesktopViewer"
        QT_MOC_LITERAL(14, 18),  // "mouseEventOccurred"
        QT_MOC_LITERAL(33, 0),  // ""
        QT_MOC_LITERAL(34, 12),  // "QMouseEvent*"
        QT_MOC_LITERAL(47, 5),  // "event"
        QT_MOC_LITERAL(53, 8),  // "position"
        QT_MOC_LITERAL(62, 16),  // "keyEventOccurred"
        QT_MOC_LITERAL(79, 10),  // "QKeyEvent*"
        QT_MOC_LITERAL(90, 18),  // "wheelEventOccurred"
        QT_MOC_LITERAL(109, 12),  // "QWheelEvent*"
        QT_MOC_LITERAL(122, 14),  // "scalingChanged"
        QT_MOC_LITERAL(137, 6),  // "factor"
        QT_MOC_LITERAL(144, 18),  // "desktopSizeChanged"
        QT_MOC_LITERAL(163, 4),  // "size"
        QT_MOC_LITERAL(168, 16),  // "selectionChanged"
        QT_MOC_LITERAL(185, 4),  // "rect"
        QT_MOC_LITERAL(190, 13),  // "onCursorBlink"
        QT_MOC_LITERAL(204, 11)   // "onFpsUpdate"
    },
    "DesktopViewer",
    "mouseEventOccurred",
    "",
    "QMouseEvent*",
    "event",
    "position",
    "keyEventOccurred",
    "QKeyEvent*",
    "wheelEventOccurred",
    "QWheelEvent*",
    "scalingChanged",
    "factor",
    "desktopSizeChanged",
    "size",
    "selectionChanged",
    "rect",
    "onCursorBlink",
    "onFpsUpdate"
};
#undef QT_MOC_LITERAL
#endif // !QT_MOC_HAS_STRING_DATA
} // unnamed namespace

Q_CONSTINIT static const uint qt_meta_data_CLASSDesktopViewerENDCLASS[] = {

 // content:
      11,       // revision
       0,       // classname
       0,    0, // classinfo
       8,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       6,       // signalCount

 // signals: name, argc, parameters, tag, flags, initial metatype offsets
       1,    2,   62,    2, 0x06,    1 /* Public */,
       6,    1,   67,    2, 0x06,    4 /* Public */,
       8,    2,   70,    2, 0x06,    6 /* Public */,
      10,    1,   75,    2, 0x06,    9 /* Public */,
      12,    1,   78,    2, 0x06,   11 /* Public */,
      14,    1,   81,    2, 0x06,   13 /* Public */,

 // slots: name, argc, parameters, tag, flags, initial metatype offsets
      16,    0,   84,    2, 0x08,   15 /* Private */,
      17,    0,   85,    2, 0x08,   16 /* Private */,

 // signals: parameters
    QMetaType::Void, 0x80000000 | 3, QMetaType::QPoint,    4,    5,
    QMetaType::Void, 0x80000000 | 7,    4,
    QMetaType::Void, 0x80000000 | 9, QMetaType::QPoint,    4,    5,
    QMetaType::Void, QMetaType::Double,   11,
    QMetaType::Void, QMetaType::QSize,   13,
    QMetaType::Void, QMetaType::QRect,   15,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

Q_CONSTINIT const QMetaObject DesktopViewer::staticMetaObject = { {
    QMetaObject::SuperData::link<QGraphicsView::staticMetaObject>(),
    qt_meta_stringdata_CLASSDesktopViewerENDCLASS.offsetsAndSizes,
    qt_meta_data_CLASSDesktopViewerENDCLASS,
    qt_static_metacall,
    nullptr,
    qt_incomplete_metaTypeArray<qt_meta_stringdata_CLASSDesktopViewerENDCLASS_t,
        // Q_OBJECT / Q_GADGET
        QtPrivate::TypeAndForceComplete<DesktopViewer, std::true_type>,
        // method 'mouseEventOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QMouseEvent *, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'keyEventOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QKeyEvent *, std::false_type>,
        // method 'wheelEventOccurred'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<QWheelEvent *, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QPoint &, std::false_type>,
        // method 'scalingChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<double, std::false_type>,
        // method 'desktopSizeChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QSize &, std::false_type>,
        // method 'selectionChanged'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        QtPrivate::TypeAndForceComplete<const QRect &, std::false_type>,
        // method 'onCursorBlink'
        QtPrivate::TypeAndForceComplete<void, std::false_type>,
        // method 'onFpsUpdate'
        QtPrivate::TypeAndForceComplete<void, std::false_type>
    >,
    nullptr
} };

void DesktopViewer::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<DesktopViewer *>(_o);
        (void)_t;
        switch (_id) {
        case 0: _t->mouseEventOccurred((*reinterpret_cast< std::add_pointer_t<QMouseEvent*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 1: _t->keyEventOccurred((*reinterpret_cast< std::add_pointer_t<QKeyEvent*>>(_a[1]))); break;
        case 2: _t->wheelEventOccurred((*reinterpret_cast< std::add_pointer_t<QWheelEvent*>>(_a[1])),(*reinterpret_cast< std::add_pointer_t<QPoint>>(_a[2]))); break;
        case 3: _t->scalingChanged((*reinterpret_cast< std::add_pointer_t<double>>(_a[1]))); break;
        case 4: _t->desktopSizeChanged((*reinterpret_cast< std::add_pointer_t<QSize>>(_a[1]))); break;
        case 5: _t->selectionChanged((*reinterpret_cast< std::add_pointer_t<QRect>>(_a[1]))); break;
        case 6: _t->onCursorBlink(); break;
        case 7: _t->onFpsUpdate(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (DesktopViewer::*)(QMouseEvent * , const QPoint & );
            if (_t _q_method = &DesktopViewer::mouseEventOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (DesktopViewer::*)(QKeyEvent * );
            if (_t _q_method = &DesktopViewer::keyEventOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (DesktopViewer::*)(QWheelEvent * , const QPoint & );
            if (_t _q_method = &DesktopViewer::wheelEventOccurred; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (DesktopViewer::*)(double );
            if (_t _q_method = &DesktopViewer::scalingChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (DesktopViewer::*)(const QSize & );
            if (_t _q_method = &DesktopViewer::desktopSizeChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (DesktopViewer::*)(const QRect & );
            if (_t _q_method = &DesktopViewer::selectionChanged; *reinterpret_cast<_t *>(_a[1]) == _q_method) {
                *result = 5;
                return;
            }
        }
    }
}

const QMetaObject *DesktopViewer::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *DesktopViewer::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_CLASSDesktopViewerENDCLASS.stringdata0))
        return static_cast<void*>(this);
    return QGraphicsView::qt_metacast(_clname);
}

int DesktopViewer::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QGraphicsView::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 8)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 8;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 8)
            *reinterpret_cast<QMetaType *>(_a[0]) = QMetaType();
        _id -= 8;
    }
    return _id;
}

// SIGNAL 0
void DesktopViewer::mouseEventOccurred(QMouseEvent * _t1, const QPoint & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void DesktopViewer::keyEventOccurred(QKeyEvent * _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void DesktopViewer::wheelEventOccurred(QWheelEvent * _t1, const QPoint & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))), const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t2))) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void DesktopViewer::scalingChanged(double _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void DesktopViewer::desktopSizeChanged(const QSize & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 4, _a);
}

// SIGNAL 5
void DesktopViewer::selectionChanged(const QRect & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(std::addressof(_t1))) };
    QMetaObject::activate(this, &staticMetaObject, 5, _a);
}
QT_WARNING_POP
