/**
 * @file MainWindow.h
 * @brief Main application window for HVNC Design
 * <AUTHOR> Design Team
 * @version 1.0.0
 * 
 * This is the main window class that serves as the primary interface
 * for the HVNC Design application. It contains the desktop viewer,
 * control panel, and manages the overall application layout and
 * user interactions.
 */

#pragma once

#include "Common.h"

// Forward declarations
class DesktopViewer;
class ControlPanel;
class SettingsDialog;
class QuickServerDialog;
class ServerManager;
class ConnectionManager;
class QSplitter;
class QStatusBar;
class QMenuBar;
class QToolBar;
class QLabel;
class QProgressBar;

/**
 * @class MainWindow
 * @brief Main application window
 * 
 * The MainWindow class provides the primary user interface for the
 * HVNC Design application. It manages the layout of major components
 * including the desktop viewer, control panel, and various UI elements.
 * It also handles menu actions, toolbar operations, and status updates.
 */
class MainWindow : public QMainWindow {
    Q_OBJECT

public:
    /**
     * @brief Constructor
     * @param parent Parent widget
     */
    explicit MainWindow(QWidget* parent = nullptr);

    /**
     * @brief Destructor
     */
    ~MainWindow() override;

    /**
     * @brief Get desktop viewer instance
     * @return Pointer to desktop viewer
     */
    DesktopViewer* getDesktopViewer() const { return m_desktopViewer; }

    /**
     * @brief Get control panel instance
     * @return Pointer to control panel
     */
    ControlPanel* getControlPanel() const { return m_controlPanel; }

    /**
     * @brief Get server manager instance
     * @return Pointer to server manager
     */
    ServerManager* getServerManager() const { return m_serverManager; }

protected:
    /**
     * @brief Handle close event
     * @param event Close event
     */
    void closeEvent(QCloseEvent* event) override;

    /**
     * @brief Handle resize event
     * @param event Resize event
     */
    void resizeEvent(QResizeEvent* event) override;

    /**
     * @brief Handle show event
     * @param event Show event
     */
    void showEvent(QShowEvent* event) override;

    /**
     * @brief Handle key press event
     * @param event Key event
     */
    void keyPressEvent(QKeyEvent* event) override;

private slots:
    /**
     * @brief Handle new connection action
     */
    void onNewConnection();

    /**
     * @brief Handle disconnect action
     */
    void onDisconnect();

    /**
     * @brief Handle server start action
     */
    void onStartServer();

    /**
     * @brief Handle server stop action
     */
    void onStopServer();

    /**
     * @brief Handle settings action
     */
    void onSettings();

    /**
     * @brief Handle about action
     */
    void onAbout();

    /**
     * @brief Handle exit action
     */
    void onExit();

    /**
     * @brief Handle view menu actions
     */
    void onToggleControlPanel();
    void onToggleStatusBar();
    void onToggleToolBar();
    void onToggleFullScreen();

    /**
     * @brief Handle connection state change
     * @param state New connection state
     */
    void onConnectionStateChanged(ConnectionState state);

    /**
     * @brief Handle server state change
     * @param state New server state
     */
    void onServerStateChanged(ServerState state);

    /**
     * @brief Handle client connected
     * @param clientId Client identifier
     * @param clientInfo Client information
     */
    void onClientConnected(ClientId clientId, const QString& clientInfo);

    /**
     * @brief Handle client disconnected
     * @param clientId Client identifier
     */
    void onClientDisconnected(ClientId clientId);

    /**
     * @brief Handle desktop image received from client
     * @param clientId Client identifier
     * @param image Desktop image
     */
    void onDesktopImageReceived(ClientId clientId, const QImage& image);

    /**
     * @brief Handle mouse event from desktop viewer
     * @param event Mouse event
     * @param desktopPos Position on desktop
     */
    void onDesktopMouseEvent(QMouseEvent* event, const QPoint& desktopPos);

    /**
     * @brief Handle keyboard event from desktop viewer
     * @param event Keyboard event
     */
    void onDesktopKeyEvent(QKeyEvent* event);

    /**
     * @brief Handle mouse wheel event from desktop viewer
     * @param event Wheel event
     * @param desktopPos Position on desktop
     */
    void onDesktopWheelEvent(QWheelEvent* event, const QPoint& desktopPos);

    /**
     * @brief Handle status message update
     * @param message Status message
     * @param timeout Message timeout (0 for permanent)
     */
    void onStatusMessage(const QString& message, int timeout = 0);

    /**
     * @brief Handle progress update
     * @param value Progress value (0-100)
     * @param text Progress text
     */
    void onProgressUpdate(int value, const QString& text = QString());

    /**
     * @brief Handle error message
     * @param title Error title
     * @param message Error message
     */
    void onErrorMessage(const QString& title, const QString& message);

    /**
     * @brief Handle start server request from control panel
     * @param port Server port
     * @param password Server password
     * @param maxConnections Maximum connections
     */
    void onStartServerFromPanel(int port, const QString& password, int maxConnections);

    /**
     * @brief Handle stop server request from control panel
     */
    void onStopServerFromPanel();

    /**
     * @brief Handle settings change from control panel
     */
    void onControlPanelSettingsChanged();

    /**
     * @brief Handle disconnect client request from control panel
     * @param clientId Client ID to disconnect
     */
    void onDisconnectClientFromPanel(const QString& clientId);

    /**
     * @brief Show quick server configuration dialog
     */
    void showQuickServerConfig();

    /**
     * @brief Handle settings applied from settings dialog
     */
    void onSettingsApplied();

    /**
     * @brief Handle server configuration change from settings dialog
     * @param port Server port
     * @param password Server password
     * @param maxConnections Maximum connections
     */
    void onServerConfigFromSettings(int port, const QString& password, int maxConnections);

    /**
     * @brief Handle quick server configuration change
     * @param port Server port
     * @param password Server password
     * @param maxConnections Maximum connections
     * @param autoStart Auto-start enabled
     */
    void onQuickServerConfigChanged(int port, const QString& password, int maxConnections, bool autoStart);

private:
    /**
     * @brief Initialize the main window
     */
    void initialize();

    /**
     * @brief Setup the user interface
     */
    void setupUI();

    /**
     * @brief Create menu bar
     */
    void createMenuBar();

    /**
     * @brief Create toolbar
     */
    void createToolBar();

    /**
     * @brief Create status bar
     */
    void createStatusBar();

    /**
     * @brief Create central widget
     */
    void createCentralWidget();

    /**
     * @brief Setup connections between components
     */
    void setupConnections();

    /**
     * @brief Apply styling to components
     */
    void applyStyles();

    /**
     * @brief Load window settings
     */
    void loadSettings();

    /**
     * @brief Save window settings
     */
    void saveSettings();

    /**
     * @brief Update window title
     */
    void updateWindowTitle();

    /**
     * @brief Update UI state based on connection/server status
     */
    void updateUIState();

    /**
     * @brief Show connection dialog
     */
    void showConnectionDialog();

    /**
     * @brief Show settings dialog
     */
    void showSettingsDialog();

    /**
     * @brief Show about dialog
     */
    void showAboutDialog();

    /**
     * @brief Get the currently active client ID for input forwarding
     * @return Active client ID or 0 if none
     */
    ClientId getActiveClientId() const;

private:
    // Core components
    DesktopViewer* m_desktopViewer;          ///< Desktop viewer widget
    ControlPanel* m_controlPanel;            ///< Control panel widget
    SettingsDialog* m_settingsDialog;       ///< Settings dialog
    QuickServerDialog* m_quickServerDialog; ///< Quick server dialog
    ServerManager* m_serverManager;          ///< Server manager
    ConnectionManager* m_connectionManager;  ///< Connection manager

    // UI components
    QSplitter* m_mainSplitter;              ///< Main splitter
    QMenuBar* m_menuBar;                    ///< Menu bar
    QToolBar* m_toolBar;                    ///< Tool bar
    QStatusBar* m_statusBar;                ///< Status bar

    // Menu actions
    QAction* m_newConnectionAction;         ///< New connection action
    QAction* m_disconnectAction;            ///< Disconnect action
    QAction* m_startServerAction;           ///< Start server action
    QAction* m_stopServerAction;            ///< Stop server action
    QAction* m_settingsAction;              ///< Settings action
    QAction* m_exitAction;                  ///< Exit action
    QAction* m_aboutAction;                 ///< About action
    QAction* m_toggleControlPanelAction;    ///< Toggle control panel action
    QAction* m_toggleStatusBarAction;       ///< Toggle status bar action
    QAction* m_toggleToolBarAction;         ///< Toggle toolbar action
    QAction* m_fullScreenAction;            ///< Full screen action
    QAction* m_quickSettingsAction;         ///< Quick settings action
    QAction* m_serverConfigAction;          ///< Server configuration action

    // Status bar widgets
    QLabel* m_statusLabel;                  ///< Status label
    QLabel* m_connectionStatusLabel;        ///< Connection status label
    QLabel* m_serverStatusLabel;            ///< Server status label
    QLabel* m_clientCountLabel;             ///< Client count label
    QProgressBar* m_progressBar;            ///< Progress bar

    // State variables
    ConnectionState m_connectionState;      ///< Current connection state
    ServerState m_serverState;              ///< Current server state
    int m_connectedClients;                 ///< Number of connected clients
    bool m_isFullScreen;                    ///< Full screen state
    bool m_isInitialized;                   ///< Initialization flag

    // Settings
    QSettings* m_settings;                  ///< Application settings
};
