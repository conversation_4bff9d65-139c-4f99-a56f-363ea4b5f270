# C++20 Screenshot Transfer System

A complete C++20 implementation for capturing, compressing, and transferring screenshots over TCP - similar to HVNC (Hidden VNC) or remote desktop functionality.

## Features

✅ **Modern C++20** - Uses latest C++ features including `std::format`, concepts, and ranges  
✅ **Screen Capture** - Real-time Windows screen capture using WinAPI  
✅ **JPEG Compression** - Efficient image compression using STB library  
✅ **TCP Transfer** - Reliable network transmission with error handling  
✅ **Timestamped Storage** - Automatic screenshot organization  
✅ **Command Line Options** - Flexible configuration  
✅ **Continuous Mode** - Automated screenshot streaming  

## Quick Start

### Windows (Visual Studio)

```bash
# Build using batch script
build.bat

# Or manually:
cl /std:c++20 /EHsc server.cpp /link Ws2_32.lib User32.lib Gdi32.lib
cl /std:c++20 /EHsc client.cpp /link Ws2_32.lib User32.lib Gdi32.lib
```

### Cross-Platform (CMake)

```bash
mkdir build && cd build
cmake ..
cmake --build .
```

### Linux/Unix

```bash
# Build using shell script
chmod +x build.sh
./build.sh
```

## Usage

### 1. Start the Server

```bash
# Windows
build\server.exe

# Linux/Unix
./build/server
```

The server will:
- Listen on port 8888
- Create a `screenshots/` directory
- Save received images with timestamps

### 2. Send Screenshots

```bash
# Basic usage
build\client.exe

# Custom server
client.exe --ip ************* --port 8888

# High quality
client.exe --quality 95

# Continuous mode (every 5 seconds)
client.exe --continuous --interval 5
```

## Command Line Options

### Client Options

| Option | Description | Default |
|--------|-------------|---------|
| `--ip <address>` | Server IP address | 127.0.0.1 |
| `--port <port>` | Server port | 8888 |
| `--quality <1-100>` | JPEG quality | 90 |
| `--continuous` | Send screenshots continuously | false |
| `--interval <sec>` | Interval between screenshots | 5 |
| `--help` | Show help message | - |

### Examples

```bash
# Single screenshot to localhost
client.exe

# Connect to remote server
client.exe --ip ************ --port 9999

# High quality single shot
client.exe --quality 100

# Continuous monitoring every 3 seconds
client.exe --continuous --interval 3

# Remote continuous monitoring
client.exe --ip ********** --continuous --interval 10 --quality 85
```

## Architecture

### Client Side
1. **Screen Capture** - Uses `GetDC()`, `BitBlt()` for Windows screen capture
2. **Image Processing** - Converts BGR to RGB, handles different screen sizes
3. **Compression** - JPEG compression using STB library with configurable quality
4. **Network Transfer** - TCP socket with size prefixing and error handling

### Server Side
1. **Network Listener** - Multi-client TCP server
2. **Data Reception** - Reliable data reception with progress tracking
3. **File Storage** - Timestamped JPEG files in organized directory structure
4. **Error Handling** - Comprehensive error reporting and recovery

## File Structure

```
screenshot-transfer/
├── server.cpp              # Server implementation
├── client.cpp              # Client implementation  
├── stb_image_write.h        # STB image library
├── CMakeLists.txt           # CMake build configuration
├── build.bat                # Windows build script
├── build.sh                 # Linux build script
├── README.md                # This file
└── screenshots/             # Created by server
    ├── screenshot_20241210_143022.jpg
    ├── screenshot_20241210_143027.jpg
    └── ...
```

## Technical Details

### Image Format
- **Capture**: 24-bit RGB (Windows BGR converted to RGB)
- **Compression**: JPEG with configurable quality (1-100)
- **Transfer**: Binary data with 4-byte size prefix
- **Storage**: Standard JPEG files with timestamp names

### Network Protocol
```
[4 bytes: Image Size] [N bytes: JPEG Data] [2 bytes: "OK" ACK]
```

### Performance
- **Compression Ratio**: ~5-20% of original size (quality dependent)
- **Capture Speed**: ~50-100ms per screenshot
- **Network Overhead**: Minimal with size prefixing
- **Memory Usage**: Efficient with RAII and smart pointers

## Security Considerations

⚠️ **Important**: This tool captures screen content and transmits it over network.

- Only use on systems you own or have explicit permission to monitor
- Consider adding authentication for production use
- Use VPN or secure networks for remote connections
- Be aware of privacy and legal implications

## Extending the System

### Adding Authentication
```cpp
// Add to server.cpp
bool authenticateClient(SOCKET clientSocket) {
    // Implement your authentication logic
    return true;
}
```

### Adding Encryption
```cpp
// Consider using OpenSSL for TLS encryption
// Replace raw sockets with SSL_* functions
```

### Cross-Platform Screen Capture
```cpp
#ifdef _WIN32
    // Current Windows implementation
#elif defined(__linux__)
    // Add X11 screen capture
#elif defined(__APPLE__)
    // Add macOS screen capture
#endif
```

## Troubleshooting

### Common Issues

**Build Errors:**
- Ensure C++20 compiler support
- Check that all required libraries are linked
- Verify STB header is in the correct location

**Connection Issues:**
- Check firewall settings
- Verify server is running and listening
- Ensure correct IP/port configuration

**Performance Issues:**
- Lower JPEG quality for faster transfer
- Increase interval for continuous mode
- Check network bandwidth

### Debug Mode

Add debug output by defining:
```cpp
#define DEBUG_MODE
```

## License

This project uses the STB library which is in the public domain.
The rest of the code is provided as-is for educational purposes.

## Contributing

Feel free to submit issues and enhancement requests!

Areas for improvement:
- Cross-platform screen capture
- GUI interface
- Real-time streaming optimization
- Authentication and encryption
- Multiple monitor support
