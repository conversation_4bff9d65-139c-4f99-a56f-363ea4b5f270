#pragma once

#include <Windows.h>
#include <memory>
#include <utility>
#include <concepts>
#include <type_traits>
#include <functional>

namespace hvnc {

// Advanced concepts for Windows handle types
template<typename T>
concept WindowsHandle = std::is_same_v<T, HANDLE> ||
                        std::is_same_v<T, HDC> ||
                        std::is_same_v<T, HBITMAP> ||
                        std::is_same_v<T, HDESK> ||
                        std::is_same_v<T, SOCKET> ||
                        std::is_same_v<T, HMODULE> ||
                        std::is_same_v<T, HKEY> ||
                        std::is_same_v<T, HWND> ||
                        std::is_same_v<T, HICON> ||
                        std::is_same_v<T, HBRUSH> ||
                        std::is_same_v<T, HFONT>;

// Concept for handle deleter functions
template<typename F, typename T>
concept HandleDeleter = WindowsHandle<T> &&
                       std::invocable<F, T> &&
                       std::is_void_v<std::invoke_result_t<F, T>>;

// Concept for invalid handle values
template<typename T, auto InvalidValue>
concept ValidInvalidValue = WindowsHandle<T> &&
                           std::convertible_to<decltype(InvalidValue), T>;

// Concept for RAII-compatible types
template<typename T>
concept RAIICompatible = std::movable<T> &&
                        !std::copyable<T> &&
                        std::destructible<T>;

// Concept for Windows API result types
template<typename T>
concept WindowsApiResult = std::same_as<T, BOOL> ||
                          std::same_as<T, DWORD> ||
                          std::same_as<T, HRESULT> ||
                          WindowsHandle<T>;

// Concept for memory allocators
template<typename F>
concept MemoryAllocator = requires(F f, size_t size) {
    { f(size) } -> std::convertible_to<void*>;
};

// Concept for memory deallocators
template<typename F>
concept MemoryDeallocator = requires(F f, void* ptr) {
    f(ptr);
    std::is_void_v<decltype(f(ptr))>;
};

// RAII wrapper for generic Windows handles
template<WindowsHandle HandleType, auto InvalidValue, auto DeleterFunc>
class unique_handle {
private:
    HandleType handle_;

public:
    // Constructor
    explicit unique_handle(HandleType handle = static_cast<HandleType>(InvalidValue)) noexcept 
        : handle_(handle) {}

    // Destructor
    ~unique_handle() noexcept {
        reset();
    }

    // Move constructor
    unique_handle(unique_handle&& other) noexcept 
        : handle_(std::exchange(other.handle_, static_cast<HandleType>(InvalidValue))) {}

    // Move assignment
    unique_handle& operator=(unique_handle&& other) noexcept {
        if (this != &other) {
            reset();
            handle_ = std::exchange(other.handle_, static_cast<HandleType>(InvalidValue));
        }
        return *this;
    }

    // Delete copy operations
    unique_handle(const unique_handle&) = delete;
    unique_handle& operator=(const unique_handle&) = delete;

    // Get the raw handle
    [[nodiscard]] HandleType get() const noexcept { return handle_; }

    // Release ownership
    [[nodiscard]] HandleType release() noexcept {
        return std::exchange(handle_, static_cast<HandleType>(InvalidValue));
    }

    // Reset with new handle
    void reset(HandleType new_handle = static_cast<HandleType>(InvalidValue)) noexcept {
        if (handle_ != static_cast<HandleType>(InvalidValue)) {
            DeleterFunc(handle_);
        }
        handle_ = new_handle;
    }

    // Boolean conversion
    [[nodiscard]] explicit operator bool() const noexcept {
        return handle_ != static_cast<HandleType>(InvalidValue);
    }

    // Get address for output parameters
    HandleType* addressof() noexcept {
        reset();
        return &handle_;
    }

    // Implicit conversion to raw handle for API calls
    operator HandleType() const noexcept { return handle_; }
};

// Deleter functions
inline void close_handle_deleter(HANDLE handle) noexcept {
    if (handle && handle != INVALID_HANDLE_VALUE) {
        CloseHandle(handle);
    }
}

inline void delete_dc_deleter(HDC hdc) noexcept {
    if (hdc) {
        DeleteDC(hdc);
    }
}

inline void delete_object_deleter(HGDIOBJ obj) noexcept {
    if (obj) {
        DeleteObject(obj);
    }
}

inline void close_desktop_deleter(HDESK hdesk) noexcept {
    if (hdesk) {
        CloseDesktop(hdesk);
    }
}

inline void close_socket_deleter(SOCKET sock) noexcept {
    if (sock != INVALID_SOCKET) {
        closesocket(sock);
    }
}

inline void free_library_deleter(HMODULE hmod) noexcept {
    if (hmod) {
        FreeLibrary(hmod);
    }
}

inline void reg_close_key_deleter(HKEY hkey) noexcept {
    if (hkey) {
        RegCloseKey(hkey);
    }
}

// Type aliases for common handle types
using unique_file_handle = unique_handle<HANDLE, INVALID_HANDLE_VALUE, close_handle_deleter>;
using unique_generic_handle = unique_handle<HANDLE, nullptr, close_handle_deleter>;
using unique_hdc = unique_handle<HDC, nullptr, delete_dc_deleter>;
using unique_hbitmap = unique_handle<HBITMAP, nullptr, delete_object_deleter>;
using unique_hdesk = unique_handle<HDESK, nullptr, close_desktop_deleter>;
using unique_socket = unique_handle<SOCKET, INVALID_SOCKET, close_socket_deleter>;
using unique_hmodule = unique_handle<HMODULE, nullptr, free_library_deleter>;
using unique_hkey = unique_handle<HKEY, nullptr, reg_close_key_deleter>;

// RAII wrapper for memory allocated with LocalAlloc
class unique_local_memory {
private:
    void* ptr_;

public:
    explicit unique_local_memory(void* ptr = nullptr) noexcept : ptr_(ptr) {}
    
    ~unique_local_memory() noexcept {
        reset();
    }

    unique_local_memory(unique_local_memory&& other) noexcept 
        : ptr_(std::exchange(other.ptr_, nullptr)) {}

    unique_local_memory& operator=(unique_local_memory&& other) noexcept {
        if (this != &other) {
            reset();
            ptr_ = std::exchange(other.ptr_, nullptr);
        }
        return *this;
    }

    unique_local_memory(const unique_local_memory&) = delete;
    unique_local_memory& operator=(const unique_local_memory&) = delete;

    [[nodiscard]] void* get() const noexcept { return ptr_; }
    
    [[nodiscard]] void* release() noexcept {
        return std::exchange(ptr_, nullptr);
    }

    void reset(void* new_ptr = nullptr) noexcept {
        if (ptr_) {
            LocalFree(ptr_);
        }
        ptr_ = new_ptr;
    }

    [[nodiscard]] explicit operator bool() const noexcept {
        return ptr_ != nullptr;
    }

    template<typename T>
    [[nodiscard]] T* as() const noexcept {
        return static_cast<T*>(ptr_);
    }
};

// RAII wrapper for memory allocated with malloc/Alloc
class unique_heap_memory {
private:
    void* ptr_;

public:
    explicit unique_heap_memory(void* ptr = nullptr) noexcept : ptr_(ptr) {}
    
    ~unique_heap_memory() noexcept {
        reset();
    }

    unique_heap_memory(unique_heap_memory&& other) noexcept 
        : ptr_(std::exchange(other.ptr_, nullptr)) {}

    unique_heap_memory& operator=(unique_heap_memory&& other) noexcept {
        if (this != &other) {
            reset();
            ptr_ = std::exchange(other.ptr_, nullptr);
        }
        return *this;
    }

    unique_heap_memory(const unique_heap_memory&) = delete;
    unique_heap_memory& operator=(const unique_heap_memory&) = delete;

    [[nodiscard]] void* get() const noexcept { return ptr_; }
    
    [[nodiscard]] void* release() noexcept {
        return std::exchange(ptr_, nullptr);
    }

    void reset(void* new_ptr = nullptr) noexcept {
        if (ptr_) {
            free(ptr_);
        }
        ptr_ = new_ptr;
    }

    [[nodiscard]] explicit operator bool() const noexcept {
        return ptr_ != nullptr;
    }

    template<typename T>
    [[nodiscard]] T* as() const noexcept {
        return static_cast<T*>(ptr_);
    }
};

// Factory functions for creating RAII handles
[[nodiscard]] inline unique_file_handle make_file_handle(
    const char* filename, 
    DWORD desired_access, 
    DWORD share_mode = 0,
    DWORD creation_disposition = OPEN_EXISTING,
    DWORD flags_and_attributes = FILE_ATTRIBUTE_NORMAL) noexcept {
    
    return unique_file_handle{CreateFileA(filename, desired_access, share_mode, 
                                         nullptr, creation_disposition, flags_and_attributes, nullptr)};
}

[[nodiscard]] inline unique_hdc make_compatible_dc(HDC hdc = nullptr) noexcept {
    return unique_hdc{CreateCompatibleDC(hdc)};
}

[[nodiscard]] inline unique_hbitmap make_compatible_bitmap(HDC hdc, int width, int height) noexcept {
    return unique_hbitmap{CreateCompatibleBitmap(hdc, width, height)};
}

[[nodiscard]] inline unique_socket make_socket(int af = AF_INET, int type = SOCK_STREAM, int protocol = IPPROTO_TCP) noexcept {
    return unique_socket{socket(af, type, protocol)};
}

[[nodiscard]] inline unique_local_memory make_local_memory(SIZE_T size, UINT flags = LPTR) noexcept {
    return unique_local_memory{LocalAlloc(flags, size)};
}

[[nodiscard]] inline unique_heap_memory make_heap_memory(size_t size) noexcept {
    return unique_heap_memory{malloc(size)};
}

} // namespace hvnc
