/**
 * @file Main.cpp
 * @brief HVNC Server with Qt6 Design/ GUI Integration
 * <AUTHOR> Team
 * @version 2.0.0
 *
 * This integrates the HVNC server functionality with the Qt6 Design/ GUI.
 * When the server runs, it shows the professional Qt6 interface.
 */

#include "Common.h"
#include "Server.h"
#include "_version.h"

// Include Qt6 Design/ framework
#include "../Design/include/Common.h"
#include "../Design/include/MainWindow.h"
#include "../Design/include/DesktopViewer.h"
#include "../Design/include/ServerManager.h"
#include "../Design/include/StyleManager.h"

#include <QApplication>
#include <QTcpServer>
#include <QTcpSocket>
#include <QTimer>
#include <QImage>
#include <QPixmap>
#include <QMessageBox>
#include <QNetworkInterface>
#include <QHostAddress>
#include <QSettings>
#include <QDateTime>

/**
 * @class IntegratedHVNCServer
 * @brief Integrates HVNC server with Design/ GUI
 */
class IntegratedHVNCServer : public QObject {
    Q_OBJECT

public:
    explicit IntegratedHVNCServer(MainWindow* mainWindow, QObject* parent = nullptr);
    ~IntegratedHVNCServer();

    bool startServer(int port = 4444, const QString& password = "admin");
    void stopServer();
    bool isRunning() const { return m_serverRunning; }

private slots:
    void onNewConnection();
    void onClientDisconnected();
    void onClientDataReceived();

private:
    void processClientData(QTcpSocket* client, const QByteArray& data);
    void sendMouseCommand(QTcpSocket* client, int x, int y, int button, bool pressed);
    void sendKeyCommand(QTcpSocket* client, int keyCode, bool pressed);
    void logMessage(const QString& message);

    MainWindow* m_mainWindow;
    DesktopViewer* m_desktopViewer;
    QTcpServer* m_tcpServer;
    QList<QTcpSocket*> m_clients;
    QMap<QTcpSocket*, QByteArray> m_clientBuffers;

    bool m_serverRunning;
    QString m_serverPassword;
    int m_serverPort;
    QTimer* m_statsTimer;

    // Statistics
    qint64 m_bytesReceived;
    qint64 m_bytesSent;
    int m_frameCount;
    QDateTime m_startTime;
};

IntegratedHVNCServer::IntegratedHVNCServer(MainWindow* mainWindow, QObject* parent)
    : QObject(parent)
    , m_mainWindow(mainWindow)
    , m_desktopViewer(nullptr)
    , m_tcpServer(nullptr)
    , m_serverRunning(false)
    , m_serverPassword("admin")
    , m_serverPort(4444)
    , m_bytesReceived(0)
    , m_bytesSent(0)
    , m_frameCount(0)
{
    // Get the desktop viewer from the main window
    m_desktopViewer = m_mainWindow->findChild<DesktopViewer*>();
    if (!m_desktopViewer) {
        qWarning() << "DesktopViewer not found in MainWindow!";
    }

    // Setup statistics timer
    m_statsTimer = new QTimer(this);
    m_statsTimer->setInterval(1000); // Update every second
    connect(m_statsTimer, &QTimer::timeout, [this]() {
        // Update statistics in the GUI
        QString stats = QString("Clients: %1 | Frames: %2/s | Data: %3 KB/s")
            .arg(m_clients.size())
            .arg(m_frameCount)
            .arg(m_bytesReceived / 1024);

        if (m_mainWindow) {
            m_mainWindow->statusBar()->showMessage(stats);
        }

        m_frameCount = 0;
        m_bytesReceived = 0;
    });

    logMessage("HVNC Server Integration initialized");
}

IntegratedHVNCServer::~IntegratedHVNCServer() {
    stopServer();
}

bool IntegratedHVNCServer::startServer(int port, const QString& password) {
    if (m_serverRunning) {
        return false;
    }

    m_serverPort = port;
    m_serverPassword = password;

    // Create TCP server
    m_tcpServer = new QTcpServer(this);
    connect(m_tcpServer, &QTcpServer::newConnection, this, &IntegratedHVNCServer::onNewConnection);

    if (!m_tcpServer->listen(QHostAddress::Any, m_serverPort)) {
        QString error = QString("Failed to start server on port %1: %2")
            .arg(m_serverPort)
            .arg(m_tcpServer->errorString());
        QMessageBox::critical(m_mainWindow, "Server Error", error);
        delete m_tcpServer;
        m_tcpServer = nullptr;
        return false;
    }

    m_serverRunning = true;
    m_startTime = QDateTime::currentDateTime();
    m_statsTimer->start();

    // Get local IP addresses for display
    QStringList addresses;
    for (const QNetworkInterface& interface : QNetworkInterface::allInterfaces()) {
        if (interface.flags() & QNetworkInterface::IsUp &&
            interface.flags() & QNetworkInterface::IsRunning &&
            !(interface.flags() & QNetworkInterface::IsLoopBack)) {
            for (const QNetworkAddressEntry& entry : interface.addressEntries()) {
                if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol) {
                    addresses << entry.ip().toString();
                }
            }
        }
    }

    QString message = QString("HVNC Server started on port %1").arg(m_serverPort);
    if (!addresses.isEmpty()) {
        message += QString("\nServer accessible at: %1:%2").arg(addresses.join(", ")).arg(m_serverPort);
    }
    message += QString("\nPassword: %1").arg(m_serverPassword);

    QMessageBox::information(m_mainWindow, "Server Started", message);
    logMessage("Server started successfully");

    return true;
}

void IntegratedHVNCServer::stopServer() {
    if (!m_serverRunning) {
        return;
    }

    m_serverRunning = false;
    m_statsTimer->stop();

    // Disconnect all clients
    for (QTcpSocket* client : m_clients) {
        client->disconnectFromHost();
    }
    m_clients.clear();
    m_clientBuffers.clear();

    // Stop TCP server
    if (m_tcpServer) {
        m_tcpServer->close();
        delete m_tcpServer;
        m_tcpServer = nullptr;
    }

    logMessage("Server stopped");
}

void IntegratedHVNCServer::onNewConnection() {
    while (m_tcpServer->hasPendingConnections()) {
        QTcpSocket* client = m_tcpServer->nextPendingConnection();

        connect(client, &QTcpSocket::disconnected, this, &IntegratedHVNCServer::onClientDisconnected);
        connect(client, &QTcpSocket::readyRead, this, &IntegratedHVNCServer::onClientDataReceived);

        m_clients.append(client);
        m_clientBuffers[client] = QByteArray();

        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        logMessage(QString("Client connected: %1").arg(clientInfo));

        // Send welcome message
        QByteArray welcome = QString("WELCOME:HVNC Server v2.0\n").toUtf8();
        client->write(welcome);
    }
}

void IntegratedHVNCServer::onClientDisconnected() {
    QTcpSocket* client = qobject_cast<QTcpSocket*>(sender());
    if (client) {
        QString clientInfo = QString("%1:%2").arg(client->peerAddress().toString()).arg(client->peerPort());
        logMessage(QString("Client disconnected: %1").arg(clientInfo));

        m_clients.removeAll(client);
        m_clientBuffers.remove(client);
        client->deleteLater();
    }
}

void IntegratedHVNCServer::onClientDataReceived() {
    QTcpSocket* client = qobject_cast<QTcpSocket*>(sender());
    if (!client) return;

    QByteArray data = client->readAll();
    m_bytesReceived += data.size();
    m_clientBuffers[client].append(data);

    processClientData(client, m_clientBuffers[client]);
}

void IntegratedHVNCServer::processClientData(QTcpSocket* client, QByteArray& buffer) {
    while (!buffer.isEmpty()) {
        if (buffer.startsWith("AUTH:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString authData = QString::fromUtf8(buffer.left(endIndex));
            QString password = authData.mid(5); // Remove "AUTH:"

            if (password == m_serverPassword) {
                client->write("AUTH:OK\n");
                logMessage(QString("Client authenticated: %1").arg(client->peerAddress().toString()));
            } else {
                client->write("AUTH:FAIL\n");
                logMessage(QString("Authentication failed: %1").arg(client->peerAddress().toString()));
                client->disconnectFromHost();
            }

            buffer.remove(0, endIndex + 1);

        } else if (buffer.startsWith("IMAGE:")) {
            int headerEnd = buffer.indexOf('\n');
            if (headerEnd == -1) break;

            QString header = QString::fromUtf8(buffer.left(headerEnd));
            QStringList parts = header.split(':');
            if (parts.size() >= 4) {
                int width = parts[1].toInt();
                int height = parts[2].toInt();
                int imageSize = parts[3].toInt();

                int totalSize = headerEnd + 1 + imageSize;
                if (buffer.size() < totalSize) break; // Wait for complete image

                QByteArray imageData = buffer.mid(headerEnd + 1, imageSize);
                QImage image;
                if (image.loadFromData(imageData, "JPEG")) {
                    // Display image in the desktop viewer
                    if (m_desktopViewer) {
                        m_desktopViewer->setDesktopImage(image);
                    }
                    m_frameCount++;

                    logMessage(QString("Received desktop image: %1x%2 (%3 KB)")
                        .arg(width).arg(height).arg(imageSize / 1024));
                }

                buffer.remove(0, totalSize);
            } else {
                buffer.clear(); // Invalid header
            }
        } else if (buffer.startsWith("STATUS:")) {
            int endIndex = buffer.indexOf('\n');
            if (endIndex == -1) break;

            QString statusData = QString::fromUtf8(buffer.left(endIndex));
            QString status = statusData.mid(7); // Remove "STATUS:"
            logMessage(QString("Client status: %1").arg(status));

            buffer.remove(0, endIndex + 1);
        } else {
            // Unknown data, skip one byte
            buffer.remove(0, 1);
        }
    }
}

void IntegratedHVNCServer::sendMouseCommand(QTcpSocket* client, int x, int y, int button, bool pressed) {
    QString command = QString("MOUSE:%1:%2:%3:%4\n")
        .arg(x).arg(y).arg(button).arg(pressed ? 1 : 0);
    QByteArray data = command.toUtf8();
    client->write(data);
    m_bytesSent += data.size();
}

void IntegratedHVNCServer::sendKeyCommand(QTcpSocket* client, int keyCode, bool pressed) {
    QString command = QString("KEY:%1:%2\n").arg(keyCode).arg(pressed ? 1 : 0);
    QByteArray data = command.toUtf8();
    client->write(data);
    m_bytesSent += data.size();
}

void IntegratedHVNCServer::logMessage(const QString& message) {
    QString timestamp = QDateTime::currentDateTime().toString("hh:mm:ss");
    QString logEntry = QString("[%1] %2").arg(timestamp, message);

    // Output to debug console
    qDebug() << logEntry;

    // Update status bar
    if (m_mainWindow) {
        m_mainWindow->statusBar()->showMessage(logEntry, 3000);
    }
}

/**
 * @class HVNCMainApplication
 * @brief Main application that integrates Design/ GUI with HVNC server
 */
class HVNCMainApplication : public QObject {
    Q_OBJECT

public:
    explicit HVNCMainApplication(QObject* parent = nullptr);
    ~HVNCMainApplication();

    bool initialize();
    void show();

private slots:
    void onStartServer();
    void onStopServer();

private:
    void setupMenuIntegration();
    void updateServerControls();

    MainWindow* m_mainWindow;
    IntegratedHVNCServer* m_hvncServer;
    QAction* m_startServerAction;
    QAction* m_stopServerAction;

    // Server settings
    int m_serverPort;
    QString m_serverPassword;
};

HVNCMainApplication::HVNCMainApplication(QObject* parent)
    : QObject(parent)
    , m_mainWindow(nullptr)
    , m_hvncServer(nullptr)
    , m_serverPort(4444)
    , m_serverPassword("admin")
{
    // Create the main window using Design/ framework
    m_mainWindow = new MainWindow();

    // Create HVNC server integration
    m_hvncServer = new IntegratedHVNCServer(m_mainWindow, this);

    setupMenuIntegration();
}

HVNCMainApplication::~HVNCMainApplication() {
    if (m_hvncServer) {
        m_hvncServer->stopServer();
        delete m_hvncServer;
    }

    if (m_mainWindow) {
        delete m_mainWindow;
    }
}

bool HVNCMainApplication::initialize() {
    if (!m_mainWindow || !m_hvncServer) {
        return false;
    }

    updateServerControls();

    // Auto-start server
    QTimer::singleShot(1000, this, &HVNCMainApplication::onStartServer);

    return true;
}

void HVNCMainApplication::show() {
    if (m_mainWindow) {
        m_mainWindow->show();
        m_mainWindow->raise();
        m_mainWindow->activateWindow();
    }
}

void HVNCMainApplication::setupMenuIntegration() {
    if (!m_mainWindow) return;

    // Add HVNC server menu to the existing Design/ GUI
    QMenuBar* menuBar = m_mainWindow->menuBar();
    QMenu* serverMenu = menuBar->addMenu("&HVNC Server");

    m_startServerAction = new QAction("&Start Server", this);
    m_startServerAction->setShortcut(QKeySequence("Ctrl+S"));
    m_startServerAction->setStatusTip("Start HVNC server to receive client connections");
    connect(m_startServerAction, &QAction::triggered, this, &HVNCMainApplication::onStartServer);
    serverMenu->addAction(m_startServerAction);

    m_stopServerAction = new QAction("S&top Server", this);
    m_stopServerAction->setShortcut(QKeySequence("Ctrl+T"));
    m_stopServerAction->setStatusTip("Stop HVNC server");
    m_stopServerAction->setEnabled(false);
    connect(m_stopServerAction, &QAction::triggered, this, &HVNCMainApplication::onStopServer);
    serverMenu->addAction(m_stopServerAction);
}

void HVNCMainApplication::onStartServer() {
    if (m_hvncServer->isRunning()) return;

    if (m_hvncServer->startServer(m_serverPort, m_serverPassword)) {
        updateServerControls();
        m_mainWindow->statusBar()->showMessage(QString("HVNC Server started on port %1").arg(m_serverPort));
    }
}

void HVNCMainApplication::onStopServer() {
    if (!m_hvncServer->isRunning()) return;

    m_hvncServer->stopServer();
    updateServerControls();
    m_mainWindow->statusBar()->showMessage("HVNC Server stopped");
}

void HVNCMainApplication::updateServerControls() {
    bool running = m_hvncServer->isRunning();
    m_startServerAction->setEnabled(!running);
    m_stopServerAction->setEnabled(running);
}

// Main function - Windows entry point
int CALLBACK WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    Q_UNUSED(hInstance)
    Q_UNUSED(hPrevInstance)
    Q_UNUSED(lpCmdLine)
    Q_UNUSED(nCmdShow)

    int argc = 0;
    char** argv = nullptr;
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("HVNC Server");
    app.setApplicationVersion("2.0.0");
    app.setOrganizationName("HVNC Team");

    // Apply Design/ style
    StyleManager styleManager;
    styleManager.applyDarkTheme();

    // Create and initialize main application
    HVNCMainApplication hvncApp;

    if (!hvncApp.initialize()) {
        QMessageBox::critical(nullptr, "Initialization Error",
            "Failed to initialize HVNC Server GUI.\nPlease check that all components are properly installed.");
        return 1;
    }

    // Show the Qt6 Design/ GUI
    hvncApp.show();

    return app.exec();
}

#include "Main.moc"
